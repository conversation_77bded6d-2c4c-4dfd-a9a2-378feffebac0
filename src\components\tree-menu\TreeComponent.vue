<template>
  <a-card class="treeMain" style="padding: 0">
    <!-- <a-tree
    :tree-data="filteredTreeData"
    :search-value="searchValue"
    @search="handleSearch"
    @select="handleNodeSelect"
    show-line
    default-expand-all
  >
    <template #title="{ node }">
      <span :class="{ 'highlight-node': selectedNodeId === node.id }">{{ node.name }}</span>
    </template>
</a-tree>
<a-input placeholder="搜索" v-model="searchValue" @input="handleInput" @clear="handleClear" /> -->
    <div class="search-container">
      <a-input-search
        placeholder="请输入传染病分类"
        v-model:value="searchValue"
        @input="handleInput(searchValue)"
        @clear="handleClear"
       
      />
    </div>
    <a-tree
      :replace-fields="{ children: 'children', key: 'id', title: 'name' }"
      draggable
      :isLeaf="true"
      :show-line="{
        showLeafIcon: true,
        showLine: true,
      }"
      :show-icon="true"
      :defaultExpandAll="true"
      highlight-current
      :defaultExpandParent="true"
      @node-drop="handleNodeDrop"
      :tree-data="filteredTreeData"
      :search-value="searchValue"
      @search="handleSearch"
      @select="handleNodeSelect"
      :expanded-keys="expandedKeys"
      @expand="onExpand"
      :auto-expand-parent="autoExpandParent"
    >
      <!-- 通过switcherIcon修改只能修改所有父级的图标,子级不受影响 -->
      <template #switcherIcon="{ dataRef, expanded }">
        <span
          style="margin-top: 6px"
          v-if="dataRef.children && dataRef.children.length > 0"
        >
          <svg-icon
            width="18"
            height="24"
            :name="expanded ? 'tree-open' : 'tree-close'"
          ></svg-icon>
        </span>
      </template>
      <template #leafIcon>
        <span style="margin-top: 6px">
          <svg-icon width="18" height="24" name="tree-child"></svg-icon>
        </span>
      </template>
      <template #title="{ dataRef }">
        <span v-if="dataRef.name.indexOf(searchValue) > -1" style="display: flex">
          <span v-if="dataRef.children && dataRef.children.length > 0">
            <CaretDownOutlined />
          </span>
          {{ dataRef.name.substring(0, dataRef.name.indexOf(searchValue)) }}
          <div style="color: #209e85">{{ searchValue }}</div>
          {{
            dataRef.name.substring(dataRef.name.indexOf(searchValue) + searchValue.length)
          }}
        </span>
        <span v-else>{{ dataRef.name }}</span>
      </template>
    </a-tree>
  </a-card>
</template>

<script lang="ts" setup>
import { ref, computed, watch, defineProps, defineEmits, h } from "vue";
import { message } from "ant-design-vue";
import type { TreeNode } from "ant-design-vue";
import { debounce } from "@/utils/debounce";
import { CaretDownOutlined } from "@ant-design/icons-vue";
// 定义TreeNode接口
interface TreeNode {
  id: number;
  parentId: null;
  name: string;
  level: number;
  description: string;
  status: number;
  createTime: string;
  modifyTime: string;
  creator: string;
  modifier: string | null;
  children?: TreeNode[];
  sort: number;
}

// 接收父组件传递过来的树形数据
const props = defineProps<{
  treeData: TreeNode[];
}>();
const searchValue = ref("");
let expandedKeys = ref<(string | number)[]>([]);
const autoExpandParent = ref<boolean>(true);
let filteredData = ref<any[]>([]);

const iconNode = () => {
  return h("div", 555);
};
// 提取树结构中所有节点的id
const extractIds = (data: any[], keyword: string): number[] => {
  let ids: number[] = [];

  // 遍历每个节点
  data.forEach((item) => {
    // 先处理子节点
    let childIds: number[] = [];
    if (item.children && item.children.length > 0) {
      childIds = extractIds(item.children, keyword); // 递归提取子节点的id
    }

    // 如果当前节点的name包含关键字或子节点包含关键字，才添加该节点
    if (
      (item.name.includes(keyword) &&
        item?.children?.some((item: any) => item.name.includes(keyword))) ||
      childIds.length > 0
    ) {
      ids.push(item.id); // 添加当前节点的id
      ids = ids.concat(childIds); // 合并子节点的id
    }
  });

  return ids;
};
const onExpand = (keys: string[]) => {
  expandedKeys.value = keys;
  autoExpandParent.value = false;
};

const emits = defineEmits(["nodeClicked", "getValue"]);

// 用于存储当前选中的节点ID，初始为空字符串
// const selectedNodeId = ref('')
const filteredTreeData = computed(() => {
  let filteredData: TreeNode[] = props.treeData;

  return filteredData;
});

watch(
  () => props.treeData, // 监听 props.treeData 的变化
  (newValue: any[]) => {
    // 回调函数

    if (searchValue.value) {
      expandedKeys.value = extractIds(newValue, searchValue.value);
    } else {
      expandedKeys.value = [];
    }
  },
  {
    deep: true, // 开启深度监听
    immediate: true, // 如果你希望在初始化时就触发一次回调
  }
);

// 存储当前选中的节点ID
const selectedNodeId = ref<number | null>(null);

// 处理搜索事件
const handleSearch = (value: string) => {
  console.log(value, "value");

  searchValue.value = value;
};

// 处理输入框输入事件
const handleInput = debounce((item: string) => {
  // 这里可以添加一些输入时的逻辑，比如显示加载提示等（可根据需求添加）
  console.log(item, "item");

  emits("getValue", item);
}, 1000);

// 处理输入框清空事件
const handleClear = () => {
  searchValue.value = "";
};

// 查找父节点路径的函数
function findNodePathById(treeData: any[], targetId: number): number[] {
  const path: number[] = [];

  function findParentNode(nodes: any[], currentId: number): boolean {
    for (const node of nodes) {
      if (node.id === currentId) {
        path.push(node.id);
        return true;
      }

      // 如果节点有子节点，递归查找
      if (node.children && Array.isArray(node.children)) {
        if (findParentNode(node.children, currentId)) {
          path.push(node.id);
          return true;
        }
      }
    }
    return false;
  }

  findParentNode(treeData, targetId);
  return path.reverse();
}
// 处理节点选择事件
const handleNodeSelect = (selectedKeys: number[], info: any) => {
  console.log(info, findNodePathById(props.treeData, selectedKeys[0]));
  if (
    selectedKeys.length > 0 &&
    findNodePathById(props.treeData, selectedKeys[0]).length > 0
  ) {
    expandedKeys.value = findNodePathById(props.treeData, selectedKeys[0]);
    autoExpandParent.value = false;
  } else {
    expandedKeys.value = [info.node.parentId];
    autoExpandParent.value = false;
    // expandedKeys.value = selectedKeys
    // autoExpandParent.value = false
  }

  selectedNodeId.value = selectedKeys[0];

  emits("nodeClicked", selectedKeys);
};
// // 根据节点结构返回相应图标
// const getIcon = (node: TreeNode) => {
//   return node.children && node.children.length > 0? 'folder' : 'file';
// };

// 处理节点拖动事件
const handleNodeDrop = ({ node, draggedNode, dropToGap }: any, event: Event) => {
  // 这里可以根据你的需求对拖动后的节点位置进行处理
  // 例如更新树形数据的顺序等
  console.log("节点拖动完成", node, draggedNode, dropToGap);
};

// 处理节点点击事件
const handleNodeClick = (keys: number[], info: { name: any; key: number }) => {
  console.log(keys, info, "jjj");
  if (selectedNodeId.value === keys[0]) {
    selectedNodeId.value = "";
    // 触发父组件的事件，传递空字符串表示取消选中
    emits("nodeClicked", "");
  } else {
    selectedNodeId.value = keys[0];
    // 触发父组件的事件，传递选中节点的ID
    emits("nodeClicked", keys);
  }
};

// 处理编辑节点事件
const handleEditNode = (node: TreeNode) => {
  console.log("编辑节点", node);
  // 这里可以添加编辑节点的具体逻辑，比如弹出编辑框等
};

// 处理删除节点事件
const handleDeleteNode = (node: TreeNode) => {
  message.confirm({
    content: `确定要删除节点 ${node.name} 吗？`,
    onOk: () => {
      // 这里可以添加删除节点的具体逻辑，比如从树形数据中移除该节点等
      console.log("删除节点", node);
    },
  });
};

// 处理添加子节点事件
const handleAddChildNode = (node: TreeNode) => {
  console.log("添加子节点", node);
  // 这里可以添加添加子节点的具体逻辑，比如弹出添加框，添加新节点到树形数据等
};
</script>

<style scoped lang="less">
.search-container {
  // margin-bottom: 16px;
  padding: 4px 4px;
}

::v-deep .ant-tree-node-selected {
  // background-color: #209f85;
  background-color: none;
  color: #209f85;
}

.treeMain {
  padding: 0 !important;
}

.hoverName {
  display: flex;
  flex-wrap: nowrap;
}

.names {
  margin-right: 5px;
}

.showEdit {
  display: none;
}

.hoverName:hover .showEdit {
  display: block;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}

::v-deep .selected-node-font {
  color: #209f85;
  /* 你可以修改为你想要的选中字体颜色 */
}

::v-deep .ant-card-body {
  padding: 8px 8px 16px 8px;
}

:deep(.search-container .ant-input) {
  border-radius: 4px 0 0 4px !important;
}

:deep(.search-container .ant-btn) {
  border-radius: 0 4px 4px 0 !important;
}
::v-deep .ant-tree {
  padding: 8px;
}
</style>
