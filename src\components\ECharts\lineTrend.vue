<template>
  <div class="wrapper-charts">
    <div class="text-title" v-if="isShowTitle">
      <div class="title-h3">{{ title || '' }}</div>
      <div v-if="isShowSelect">
        <!-- <template #select> xxx </template> -->
        <span class="title-tip" v-if="showInstitutionName">{{ institutionName || '' }}</span>
        <slot name="select">
          <a-select size="small" style="width: 180px" :value="modelValue" class="selectType" @select="onChange"
            :fieldNames="labelProps">
            <a-select-option value="全部">全部</a-select-option>
            <a-select-option :value="item" v-for="(item, index) in selectOptions" :key="index">
              {{ item }}
            </a-select-option>
          </a-select>
        </slot>
      </div>
    </div>
    <a-spin wrapperClassName="loading" tip="Loading..." :spinning="loading">
      <div class="wrapper-charts-content" :style="bodyStyle">
        <div class="charts-styles" v-if="chartOptOneSort">
          <div v-size-direct="resizeChart" :ref="chartId" :id="chartId" :style="styles"></div>
        </div>
        <div class="no-data-available" v-else>
          <svg-icon name="noDataAvailable" width="121" height="130"></svg-icon>
          <div>暂无数据</div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount, watch, nextTick, defineSlots, markRaw } from 'vue'
  import * as echarts from 'echarts'

  // Props
  const props = defineProps({
    modelValue: {
      type: [String, Number, null, Array],
      default: '全部'
    },
    isShowTitle: {
      type: Boolean,
      default: true
    },
    isShowSelect: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    selectOptions: {
      type: Array,
      default: []
    },
    labelProps: {
      type: Object,
      default: {
        label: 'name',
        value: 'value',
        options: 'options'
      }
    },
    showInstitutionName: {
      type: Boolean,
      default: true
    },
    institutionName: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '高原病就珍'
    },
    // id 需要不一样，不然会覆盖
    chartId: {
      type: String,
      required: true,
      default: 'chartContainer'
    },

    option: {
      type: Object,
      required: true // option 是必须的
    },

    bodyStyle: {
      type: Object,
      default: {
        width: '100%',
        height: '100%'
      }
    },

    styles: {
      type: Object,
      default: {
        width: '100%',
        height: '100%'
      }
    },
    clickChart: {
      type: Function,
      required: false
    }
  })

  let chartInstance = ref(null)
  const emit = defineEmits(['update:modelValue', 'change', 'clickChart', 'finished']) // 用来触发事件
  const $solt = defineSlots()
  const onChange = (value) => {
    emit('update:modelValue', value)
    emit('change', value)
  }

  const chartOptOneSort = ref(false)
  // 初始化图表
  const initChart = () => {
    if (chartInstance.value) {
      chartInstance.value?.dispose() // 销毁已有实例，避免重复渲染
    }
    const dom = document.getElementById(props.chartId)
    chartInstance.value = markRaw(echarts.init(dom))
    // if (props.loading) {
    //   chartInstance.value?.showLoading()
    // }
    chartInstance.value?.off('click') // 移除旧的点击事件
    chartInstance.value?.setOption(props.option)
    // 监听图表渲染完成事件
    chartInstance.value?.on('finished', () => {
      chartInstance.value?.hideLoading()
      emit('finished')
    })
    // 监听图表点击事件
    chartInstance.value?.on('click', (params) => {
      // 通过 emit 触发 'click' 事件，传递参数
      emit('clickChart', params)
    })
  }

  // 监听 option 的变化并更新图表
  watch(
    () => props.option,
    (newOption) => {
      chartOptOneSort.value = newOption?.series?.length > 0
      if (chartOptOneSort.value) {
        nextTick(() => {
          if (chartInstance.value) {
            // chartInstance.value.clear();
            chartInstance.value?.dispose() // 销毁已有实例，避免重复渲染
          }
          initChart()
        })
      }
    },
    { deep: true, immediate: true } // 深度监听
  )

  // 处理窗口大小变化时，重新调整图表尺寸
  const resizeChart = (contentRect) => {
    if (chartInstance.value) {
      chartInstance.value?.resize()
    }
  }

  // 在组件挂载时初始化图表，并添加 resize 事件监听
  onMounted(() => {
    // initChart();
    // window.addEventListener('resize', handleResize)
  })

  // 在组件卸载时销毁图表，并移除 resize 事件监听
  onBeforeUnmount(() => {
    if (chartInstance.value) {
      chartInstance.value?.dispose()
    }
    // window.removeEventListener('resize', handleResize)
  })

  defineExpose({
    chartInstance,
    resizeChart,
    initChart,
    onChange,
    chartOptOneSort
  })
</script>

<style lang="less" scoped>
  .wrapper-charts {
    width: 100%;
    height: 100%;
    padding: 16px;
    box-sizing: border-box;
  }

  /* 可以根据需要设置图表的样式 */

  .text-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 10px;

    .title-h3 {
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      color: #333;
    }

    .title-tip {
      font-size: 14px;
      font-weight: 400;
      color: #5e6580;
      margin-right: 10px;
    }
  }

  .wrapper-charts-content {
    width: 100%;
    height: calc(100% - 35px);

    .charts-styles {
      width: 100%;
      height: calc(100% - 35px);
    }
  }

  .no-data-available {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    color: #5e6580;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }

  .loading,
  :deep(.loading .ant-spin-container) {
    width: 100%;
    height: 100%;
  }
</style>
