<template>
  <div class="basic-wrapper">
    <div class="filter-wrapper clearfix">
      <a-card class="table-screen clearfix" title="高原病监测报告" :bordered="false" :bodyStyle="{ padding: '0 24px', }">
        <a-form :model="formData" name="time_related_controls" class="aForm">
          <a-form-item name="range-picker" label="统计时间范围">
            <a-range-picker v-model:value="formData.time" @change="handleChangeDate" :presets="presetTimeRange()"
              value-format="YYYY-MM-DD" :allowClear="false" />
            <!-- :disabled-date="disabledStartDate" -->
          </a-form-item>
        </a-form>
      </a-card>
    </div>
    <div class="dashboard">
      <!-- 上半部分 -->
      <a-card class="top-card" :bodyStyle="{ padding: '0', }">
        <div class="top-content">
          <!-- 左侧统计 -->
          <div class="left-stats">
            <!-- 左半部分 -->
            <div class="left-column">
              <a-statistic :title="statsData[0].title" :value="statsData[0].value" class="custom-statistic" />

              <!-- <a-statistic
                          :title="statsData[1].title"
                          :value="statsData[1].value"
                          class="custom-statistic"
                        />
                   -->
            </div>

            <!-- 中间垂直分割线 -->
            <a-divider type="vertical" class="vertical-divider" />
            <!-- <a-divider type="vertical" style="height: 200px; background-color: #7cb305" /> -->
            <!-- 右半部分 -->
            <div class="right-column">
              <a-statistic :title="statsData[1].title" :value="statsData[1].value" class="custom-statistic" />

              <!-- <a-statistic
                                :title="statsData[1].title"
                                :value="statsData[1].value"
                                class="custom-statistic"
                              /> -->
            </div>
          </div>

          <!-- 右侧图表 -->
          <div class="right-content" v-if="isShowSelect">
            <div class="pie-container">
              <div ref="pieChart" class="chart"></div>
            </div>
            <div class="data-labels">
              <div v-for="(item, index) in pieData.data" :key="index" class="label-item">
                <span style="color:#828282;font-size:14px;margin-right:12px;">{{ item.name }}</span>
                <span style="color:#07123C;font-size:16px;font-weight:600;">{{ item.value }}</span>

              </div>
            </div>

          </div>

          <div class="wrapper-charts-content right-content" :style="bodyStyle" v-else>

            <div class="no-data-available">
              <svg-icon name="noDataAvailable" width="121" height="130"></svg-icon>
              <div>暂无数据</div>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 下半部分 -->
      <a-card class="bottom-section" title="患者就诊明细" :bordered="false" :bodyStyle="{ padding: '16px' }">
        <div class="table">
          <table-list ref="myTable" :tableData="tableList" :tableProps="tableProps" :total="total"
            @changePage="handleChangePage">
          </table-list>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onUnmounted, onMounted, toRaw, getCurrentInstance, watch, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { operationLog } from '@/api/log'
import { presetTimeRange } from '@/utils/utils'
import dayjs from 'dayjs'
import { use } from 'echarts/core';
import { PieChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import pieCharts from '@/components/ECharts/pieCharts.vue'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import TableList from '@/components/TableList/index.vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import { getAltDashBoard, getAltListPage } from './index.api'

use([TitleComponent, TooltipComponent, LegendComponent, PieChart, CanvasRenderer]);
const router = useRouter()

const isShowSelect = ref<any>(false)
const tableList = ref([])
const tableProps = ref([
  {
    id: 1,
    title: '患者编号',
    dataIndex: 'patientId',
    key: 'patientId',
    ellipsis: true,
    width: '20%',
  },
  {
    id: 2,
    title: '患者姓名',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true
  },
  {
    id: 3,
    title: '性别',
    dataIndex: 'sex',
    key: 'sex',
    ellipsis: true,

    // slot: 'authority',
    // width:'60%',
    // hideTooltip: true
  },
  {
    id: 4,
    title: '医院',
    dataIndex: 'hospital',
    key: 'hospital',
    ellipsis: true
  },
  {
    id: 5,
    title: '科室',
    dataIndex: 'department',
    key: 'department',
    ellipsis: true
  },
  {
    id: 6,
    title: '疾病名称',
    dataIndex: 'disease',
    key: 'disease',
    ellipsis: true
  },
  {
    id: 7,
    title: '就诊日期',
    dataIndex: 'visitDate',
    key: 'visitDate',
    ellipsis: true
  },
  {
    id: 4,
    title: '操作',
    dataIndex: 'operation',
    slot: 'operation',
    width: 40,
    buttons: [
      {
        name: '患者画像',
        onclick: ({ record }) => {
          viewFile(record)
        }
      }
    ]
  }
])
const viewFile = (key: any) => {
  console.log(key)
  window.open(`http://*************/bigdata-platform/iddb/filter/patient/${key.empiId}?disease=ams`, '_blanck');
  router.push({ name: 'monitoringReports' })
}

const total = ref(0)
const pagiNation = reactive({
  current: 1,
  size: 10
})
// 分页list
async function getList(formData: any) {
  let params = {
    current: pagiNation.current,
    size: pagiNation.size,
    from: formData.time[0],
    to: formData.time[1],
  }

  await getAltListPage(params)
    .then((res) => {
      if (res.code == 0) {
        tableList.value = res?.data?.records ?? []
        total.value = res?.data?.total ?? 0
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
// 分页事件
function handleChangePage({ pageNum, pageSize }) {
  pagiNation.current = pageNum
  pagiNation.size = pageSize
  getList(formData)
}

const statsData = ref<any>(
  [
    { title: '确诊患者数', value: '' },
    { title: '就诊人次', value: '' },
    // { title: '疑似病例数', value: 99 },
    // { title: '累计疑似病例数', value: 9999 }
  ]
)

// Mock 饼图数据
const pieData = reactive({
  data: []
});

// ECharts相关
const pieChart = ref<HTMLElement>()
let chartInstance: ECharts | null = null

const initChart = async (formData: any) => {
  await getHospitalDistributionData(formData);
  if (!pieChart.value) return

  chartInstance = echarts.init(pieChart.value)
  const option = {
    tooltip: { trigger: 'item' },
    title: {
      text: '确诊病例分布',
      left: 'center',
      top: 'top',
    },
    series: [{
      color: ['#13A89B', '#2316AB', '#405BC8', '#CCF56A', '#14705E', '#00CFBE', '#098BEA', '#079A35', '#9DD962'],
      type: 'pie',
      radius: '70%',
      data: pieData.data,
      label: { show: false },
      center: ['50%', '55%'],
      // radius: ['50%', '70%'],
    }]
  }
  chartInstance.setOption(option)
}

// 响应式调整
const resizeChart = () => chartInstance?.resize()


const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '查看监测报告',
    ip: baseIP,
    type: '高原病监测报告'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

// 时间选择
const formData = reactive({
  time: [],
})
// 时间选择
function handleChangeDate(value: any) {
  formData.time = value
  // clearData()
  query()
  // initChart(formData)
}
// 饼图数据请求
async function getHospitalDistributionData(formData: any) {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || '',
  }
  await getAltDashBoard(params)
    .then((res) => {
      if (res.code == 0) {
        statsData.value[0].value = res?.data?.empiSum;
        statsData.value[1].value = res?.data?.visitSum;
        if (res?.data?.altDiseaseAmounts.length > 0) {
          isShowSelect.value = true;
          let count = res?.data?.altDiseaseAmounts?.map((item) => {
            return {
              value: item.cnt,
              name: item.key,
            }
          })
          // 取前五个数据
          const topFive = count.slice(0, 5);

          // 计算其余数据的 cnt 总和
          const otherSum = count.slice(5).reduce((sum, item) => sum + item.value, 0);

          // 创建最终数组
          const result = [...topFive, { "name": "其他", "value": otherSum }];
          pieData.data = result
        } else {
          isShowSelect.value = false;
          pieData.data = []
        }
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

async function query() {
  await initChart(formData);
  // await getHospitalDistributionData(formData);
  await getList(formData);

}
// 生命周期钩子
onMounted(() => {
  // operationLogs()
  formData.time = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  query()

  window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart)
  chartInstance?.dispose()
})
</script>

<style lang="less" scoped>
.basic-wrapper {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;

  .table-screen {
    position: relative;
  }

  .aForm {
    position: absolute;
    right: 2%;
    top: 20%;
  }

  .dashboard {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    // padding: 16px;
    // gap: 8px;
    background-color: #f8fafc;
    overflow-y: auto;

    .top-card {
      flex: 0 0 20%;
      margin-bottom: 24px;
      // overflow: hidden;

      .top-content {
        height: 100%;
        display: flex;
        justify-content: space-between;
        background-color: rgb(248, 250, 252);
      }

    }

    .bottom-section {
      width: 100%;
      min-height: calc(100vh - 280px);
    }
  }
}

.bottom-card {
  flex: 0 0 55%;
  overflow: auto;
}


.custom-statistic :deep(.ant-statistic-title) {
  font-size: 16px;
  color: #07123C;
  margin-bottom: 8px;
  font-weight: 600;
}

.custom-statistic :deep(.ant-statistic-content) {
  font-size: 30px;
  color: #07123C;
  font-weight: 600;
}

/* 分割线样式 */
.vertical-divider {
  height: 180px !important;
  margin: auto 12px !important;
  border-color: #ddd !important;
}



/* 保持原有布局样式 */
.left-stats {
  width: 25%;
  display: flex;
  padding: 25px 25px;
  height: 200px;
  align-items: center;
  background-color: #fff;
}

.left-column,
.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 12px;
  height: 100%;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.right-content {
  width: 73%;
  display: flex;
  align-items: center;
  margin-left: 2%;
  background-color: #fff;
}

.pie-container {
  width: 30%;
  height: 200px;
}

.data-labels {
  width: 65%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding-left: 20px;
}

.label-item {
  min-width: calc(30%);
  padding: 4px 0;

  text-align: right;
  margin-right: 12px;
}

.chart {
  width: 100%;
  height: 100%;
}

::v-deep .ant-card .ant-card-body {
  padding: 0;
}

.no-data-available {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  color: #5e6580;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}
</style>