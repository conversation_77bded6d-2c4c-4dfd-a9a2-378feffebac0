import { useMeanStore } from '@/store/routes/menus'
import { useUserStore } from '@/store/routes/user'
import { usePageSourceStore } from '@/store/pageSource'
import { setConfig } from '@/config/index'

export default async function init() {
  const urlParams = new URLSearchParams(window.location.search)
  //  切换环境
  const service = urlParams.get('service') || ''
  const pageSource = urlParams.get('source') || ''
  const parentOrigin = urlParams.get('parentOrigin') || ''

  // 保存页面来源
  if (pageSource) {
    usePageSourceStore().setPageSource(pageSource)
    usePageSourceStore().setParentOrigin(parentOrigin)

    console.log('页面来源已保存')
  }

  if (service && service == 'ALT') {
    setConfig({
      APP_CODE: urlParams.get('service'),
      REDIRECT_URI: import.meta.env.MODE == 'production' ? `${window.location.origin}/ams-front/home?service=${service}` : `http://localhost:3004/?service=ALT`
    })
  }
  //  解密 token
  const encryptedToken = urlParams.get('ticket')
  //  调用 获取用户信息方法
  let user = useUserStore()
  // 调用菜单
  let menuStore = useMeanStore()
  //  解密 token
  if (encryptedToken) {
    localStorage.setItem('service', service ? service : '')

    console.log('用户信息已获取到')
    let state = JSON.parse(decodeURIComponent(encryptedToken))
    const userInfo = {
      ...state,
      ...state?.data,
      userId: state?.id || state?.data?.userId,
      token: state?.key || state?.data?.token
    }

    delete userInfo.data

    if (state.key || state.errorCode == 0) {
      if (!userInfo.expiresAt) {
        user.remove('登入登出')
        return
      }

      // 存储 token 和 userInfo
      localStorage.setItem('token', userInfo.token)
      localStorage.setItem('expiresAt', userInfo.expiresAt)
      localStorage.setItem('userInfo', JSON.stringify(userInfo))

      // 报存用户信息
      user.SAVE_TOKEN(userInfo)
      user.SET_INFO(userInfo)
      console.log('用户信息已保存')

      // 登入登出的操作日志
      user.operationLogs('登入登出')
      window.location.search = service ? `?service=${service}` : ''
    }
  }

  await user.getAuthorizationsList()
  // user.getPermissionsList(),

  return Promise.all([menuStore.getMenu()])
}
