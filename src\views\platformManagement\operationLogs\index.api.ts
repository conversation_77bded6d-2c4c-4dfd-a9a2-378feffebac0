

import request from '@/utils/request.ts'

// 读取操作日志  列表
export function getoperationLogList(data: any) {
  return request({
    url: '/operationLog/logs',
    method: 'post',
    data
  })
}
// export function getDicList(data) {
//   return request({
//     url: '/visitDict/listAll',
//     method: 'post',
//     data
//   })
// }

// 获取所有的操作类型
export function getoperationLogOprator(params: any) {
  return request({
    url: '/operationLog/categories',
    method: 'get',
    params
  })
}

// 获取所有的操作模块
export function getoperationLogTypes(params: any) {
  return request({
    url: '/operationLog/types',
    method: 'get',
    params
  })
}


// 获取所有的账户
export function getuserAccountsList(params: any) {
  return request({
    url: '/operationLog/userAccounts',
    method: 'get',
    params
  })
}
// 获取所有的操作人
export function getuserNamesList(params: any) {
  return request({
    url: '/operationLog/userNames',
    method: 'get',
    params
  })
}