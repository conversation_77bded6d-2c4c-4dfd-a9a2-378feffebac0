import dayjs from 'dayjs'
// 指定范围随机数
export const getRandomNumber = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1) + min)
}

// 判断空值
export const isEmpty = (value) => {
  if (Array.isArray(value)) {
    return !value.length
  } else if (Object.prototype.toString.call(value) === '[object Object]') {
    return !Object.keys(value).length
  } else {
    return [null, undefined, ''].includes(value)
  }
}

// 预设时间范围
export const presetTimeRange = () => {
  return [
    // { label: '上周', value: [dayjs(new Date()).subtract(7, 'day').startOf('day'), dayjs().endOf('day')] },
    { label: '上周', value: [dayjs().startOf('week').subtract(1, 'week'), dayjs().startOf('week').subtract(1, 'week').endOf('week')] },
    { label: '上月', value: [dayjs(new Date()).subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] },
    { label: '去年', value: [dayjs(new Date()).subtract(1, 'year').startOf('year'), dayjs().subtract(1, 'year').endOf('year')] }
  ]
}
