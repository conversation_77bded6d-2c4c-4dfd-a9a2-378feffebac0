const map = new WeakMap()

const resizeObserver = new ResizeObserver((entries) => {
  for (const entry of entries) {
    // 运行回调
    const handler = map.get(entry.target)
    // 回调函数中可以获取到元素的尺寸信息
    // borderBoxSize 是一个数组，包含元素的宽度和高度，单位为像素
    // contentBoxSize 是一个数组，包含元素内容的宽度和高度，单位为像素
    // 例如：entry.borderBoxSize[0].inlineSize 和 entry.borderBoxSize[0].blockSize 就是元素的宽度和高度
    handler && handler(entry.contentRect)
  }
})

const sizeDirect = {
  mounted(el: HTMLElement, binding: any) {
    resizeObserver.observe(el) // 监听元素尺寸变化
    map.set(el, binding.value) // 保存binding.value到 map 中
  },
  unmounted(el: HTMLElement) {
    resizeObserver.unobserve(el) // 取消监听元素尺寸变化
  }
}

export default sizeDirect
