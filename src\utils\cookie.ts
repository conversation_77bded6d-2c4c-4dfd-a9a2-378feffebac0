import Cookies from 'js-cookie'

// 设置 cookie
// expires: 过期时间（可选，单位通常是天）
// path: 有效的路径（一般设置为 '/'）
// name: 要设置的 cookie 名称
// value: 要设置的 cookie 值·
interface CookieOptions {
  expires?: number
  path?: string
  domain?: string
  httpOnly?: boolean
  secure?: boolean
  sameSite?: 'Lax' | 'Strict' | 'None'
  maxAge?: number | string
  sameOrigin?: boolean
  sameDomain?: boolean
  samePath?: boolean
}

export function setCookie(name: string, value: any, options: CookieOptions = { path: '/', domain: location.hostname, httpOnly: false }) {
  //  例如下面是设置cookie的失效时间为 2 分钟。
  // const millisecond = new Date().getTime()
  // const expiresTime = new Date(millisecond + 60 * 1000 * 2)
  Cookies.set(name, value, options)
}

// 获取 cookie
export function getCookie(name: string) {
  return Cookies.get(name)
}

// 删除 cookie
interface RemoveCookieOptions {
  domain?: string
  path?: string
  httpOnly?: boolean
}
export function removeCookie(name: string, options: RemoveCookieOptions = { domain: location.hostname, path: '/', httpOnly: false }) {
  Cookies.remove(name, options)
}
