<template>
  <card class="containerThree" :style="{ width, height }">
      <!-- 返回按钮 -->

      <a-button v-if="isDrilledDowns" @click="goBack" class="back-button">
          <template #icon>
              <LeftOutlined />
          </template>
          返回
      </a-button>
      <!-- 饼图标题 -->

      <div class="selectText">
          <span class="textTitle">
              活跃用户分布
              <span v-if="isDrilledDowns" class="breadcrumb">{{ parentCategory }}</span>
              <span v-if="isDrilledDowns">/ {{ currentCategorys }}</span>
              <a-tooltip title="当前业务系统在所选时间范围内活跃用户在医院、科室维度的分布">
                  <InfoCircleOutlined style="color: #8f94a7" class="iconDesc" />
              </a-tooltip>

          </span>
      </div>
      <div v-if="currentDatas && currentDatas.total > 0" id="chartContainers" style="width: 100%; height: 300px;">
      </div>


      <div class="charts-styles no-data-available" v-else>
          <span style="position: absolute;top: 130px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        color: #5e6580;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;">
              <svg-icon name="noDataAvailable" width="121" height="130"></svg-icon>
              <div>暂无数据</div>
          </span>

      </div>
  </card>
</template>

<script lang="ts" setup>
import { defineProps, ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import * as echarts from 'echarts';
import { LeftOutlined } from '@ant-design/icons-vue'
import { activeDistributionCount } from '../index.api'
import { InfoCircleOutlined } from '@ant-design/icons-vue'

interface PlatformUserAnalysis {
  key: string;
  cnt: number;
}
interface ChartData {
  total: number;
  platformUserAnalysisNames: PlatformUserAnalysis[];
}

const props = defineProps({
  params: Object,
  width: {
      type: String,
      default: '100%' // 默认宽度
  },
  height: {
      type: String,
      default: '100%' // 默认高度
  },
});
const charts = ref<echarts.ECharts | null>(null); // ECharts 实例
const currentDatas = ref<ChartData | null>(null); // 当前饼图数据
const parentCategory = ref(''); // 父级分类
const currentCategorys = ref(''); // 当前点击的分类
const isDrilledDowns = ref(false); // 是否下钻
const historyDatas = ref<ChartData | null>(null); // 保存父级数据，用于返回操作
const isDrillable = ref(true); // 是否允许下钻，初始化为 true
// 发送请求获取数据
const fetchData = async (value: Record<string, any> | undefined) => {

  const params = {
      areas: value.areas,
      departments: value.departments,
      from: value.dates[0],
      hospitals: value.hospitals,
      to: value.dates[1],
      dimension: value.dimension,
      value: value.value,
  };

  try {
      const response = await activeDistributionCount(params);
      if (response.data) {

          currentDatas.value = response.data;

          if (response.data.total > 0) {
              nextTick(() => {
                  initChart(response.data);
              })
          }


      }
  } catch (error) {
      console.error('请求数据失败:', error);
  }
};
const selectedThree = ref<any>([])
// 初始化图表
const initChart = (data: ChartData) => {
  const chartContainers = document.getElementById('chartContainers');
  if (!chartContainers) return;
  if (!charts.value) {
      charts.value = echarts.init(chartContainers);
  } else {
      charts.value.dispose();
      charts.value = echarts.init(chartContainers);
  }
  const categoryData = data.platformUserAnalysisNames.map(item => ({
      name: item.key,
      value: item.cnt,
  }));
  const option = {
      title: {
          text: `${data.total}`,
          subtext: '访问人次总数', // 副标题
          left: 'center',
          top: 'center',
          textStyle: {
              fontSize: 20,
          },
          subtextStyle: {
              fontSize: 14,
          },
      },
      color: ['#13A89B', '#2316AB', '#405BC8', '#CCF56A', '#14705E', '#00CFBE', '#098BEA', '#079A35', '#9DD962'],
      legend: {
          // orient: 'vertical',
          // x: 'right',
          x: 'center',
          y: 'bottom',
          type: 'scroll', // 数据过多时，分页显示
          selected: selectedThree.value //这里默认显示数组中前十个，如果不设置，则所有的数据都会显示在图表上
      },
      grid: {
          left: '3%',
          right: '4%',
          bottom: '4%',
          containLabel: true
      },
      tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
      },
      series: [
          {
              // name: '分类数据',
              type: 'pie',

              label: {
                  show: true,
                  formatter: '{b}: {c} ({d}%)',
              },
              // data: categoryData,
              data: categoryData.map((item) => ({
                  ...item,
                  label: {
                      show: item.value > 0 // 数据值为 0 时不显示标签
                  },
                  labelLine: {
                      show: item.value > 0 // 数据值为 0 时不显示指引线
                  }
              })),

              emphasis: {
                  itemStyle: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                  label: {
                      show: false,
                      fontSize: 10,
                      fontWeight: 'bold'
                  }
              },
              labelLine: {
                  show: true,
                  length: 10, // 第一段指导线 长度
                  length2: 10 // 第二段指导线 长度
              },
              // name: name,

              radius: ['50%', '70%'],
              avoidLabelOverlap: false
          },
      ],
  };

  charts.value.setOption(option);

  charts.value.on('click', (params: any) => {
      if (isDrilledDowns.value || !isDrillable.value) return; // 禁止下钻
      const category = params.name;
      if (category) {
          historyDatas.value = data; // 保存父级数据
          isDrilledDowns.value = true; // 标记已下钻
          currentCategorys.value = category; // 更新当前分类
          isDrillable.value = false; // 禁止继续下钻
          props.params.dimension = '科室'
          props.params.value = category
          fetchData(props.params)
      }
  });
};

// 返回上一级数据
const goBack = () => {
  if (historyDatas.value) {
      currentDatas.value = { ...historyDatas.value }; // 直接恢复数据
      parentCategory.value = ''; // 清空父级分类
      currentCategorys.value = ''; // 清空当前分类
      props.params.value = ''; // 取消下钻的值
      isDrilledDowns.value = false; // 标记未下钻
      isDrillable.value = true; // 允许下钻

      nextTick(() => {
          if (currentDatas.value && currentDatas.value.total > 0) {
              initChart(currentDatas.value); // 重新渲染饼图
          }
      });

      historyDatas.value = null; // 清空历史数据
  }
};
const propParams = ref<any>()
// 监听父组件参数变化
watch(() => props.params, (newVal) => {

  propParams.value = newVal
  // 当父组件的参数发生变化时，重新请求父级数据
  currentCategorys.value = '';
  isDrilledDowns.value = false; // 标记未下钻
  isDrillable.value = true; // 允许下钻
  historyDatas.value=null;
  fetchData(props.params);
}, { immediate: true });

// // 初始化时请求数据
// onMounted(() => {
//     fetchData(props); // 根据传入的 dimension 初始化请求数据
// });


const handleResize = () => {
  if (charts.value) {
      charts.value.resize()
  }
}

onMounted(() => {

  window.addEventListener('resize', handleResize)
})

// 在组件卸载时销毁图表，并移除 resize 事件监听
onBeforeUnmount(() => {
  if (charts.value) {
      charts.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 按钮样式 */
/* .back-button {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
} */
.get {
  width: 100%;
  height: 100%;
  display: block;
}

.post {

  display: none;
}

.pie-charts-title {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: bold;
}

.breadcrumb {
  color: #1890ff;
  font-size: 14px;
}


.containerThree {
  /* width: 100%;
  height: 100%;
  margin: 0 auto; */
  /* position: relative; */
  /* background-color: #fff; */
  width: 100%;
  height: 100%;
  position: relative;
}

.charts {
  /* height: 400px; */
  width: 100%;
  height: 100%;
}

/* .selectText {
margin-left: 8px;
} */

.textTitle {
  font-size: 16px;
  font-weight: bold;
  color: #07123c;
  font-family: 'Noto Sans SC';
  line-height: 30px;
}

.back-button {
  float: left;
  margin-top: -3px;
  margin-right: 20px;
  margin-left: 8px;
  display: flex;
  padding: 5px 12px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}

.charts-styles {
  width: 100%;
  /* height: calc(100% - 20px); */
  height: 100%;
  /* padding: 10px; */
  box-sizing: border-box;
}

.no-data-available {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  color: #5e6580;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.iconDesc {

  margin-left: 10px;
}
</style>