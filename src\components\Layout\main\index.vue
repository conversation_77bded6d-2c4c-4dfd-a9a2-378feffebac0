<template>
  <a-layout
  class="aLayout"
    style="padding: 0 24px 24px"
    :style="theme == 'light' ? 'background-color: #F8FAFC' : ''"
  >
    <a-breadcrumb style="margin: 14px 0" separator="/">
      <!-- <a-breadcrumb-item v-for="item in breadcrumbs">
        <router-link :to="item.path">{{ item.meta.title }}</router-link>
      </a-breadcrumb-item> -->
      <a-breadcrumb-item v-for="(item, index) in breadcrumbs">
        <router-link :to="item.path">
          <span v-if="index == breadcrumbs.length - 1" style="color: #07123c">
            {{ item.meta.title }}
          </span>
          <span v-else>{{ item.meta.title }}</span>
        </router-link>
      </a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main-content min-h-screen w-full">
      <a-layout-content
        class="overflow-y-auto overflow-x-hidden rounded-md shadow-md clearfix"
        id="main-info-content"
      >
        <router-view v-slot="{ Component }">
          <transition>
            <component :is="Component" />
          </transition>
        </router-view>
      </a-layout-content>
    </div>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { useRoute } from "vue-router";
import { THEME, useTheme } from "@/utils/use-theme";
const { theme, setTheme } = useTheme();
const $route = useRoute();
// 面包屑导航，从route上获取
const breadcrumbs = computed(() => {
  let matched = $route.matched.filter((item) => item.name);
  const second = matched[1];

  if (second && second.name !== "home") {
    // matched.splice(0, 1, { path: '/home', meta: { title: '首页' } })
    matched.splice(0, 1);
    matched.splice(1, 0, {
      path: second.meta.fatherPath,
      meta: { title: second.meta.fatherTitle },
    });
  } else if (second && second.name == "home") {
    matched = [{ path: "/home", meta: { title: "首页" } }];
  }
  return matched.reverse();
});
</script>

<style lang="less" scoped>
.aLayout{
  background-image: url("@/assets/png/bg_top.png");
}
.main-content {
  width: 100%;
  min-height: 280px;
  height: calc(100vh - 120px);
  border-radius: 10px;
  background: #fff;
  // padding: 24px;
  margin: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.ant-layout-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  background-color: var(--normal-bg-color);
}

[data-theme="dark"] .main-content {
  background-color: #555555;
}
</style>
