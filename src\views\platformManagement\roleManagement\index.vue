<template>
  <div class="user-wrapper">
    <a-card title="角色管理" :bordered="false" :bodyStyle="{ padding: '24px', minHeight: 'calc(100vh - 180px)' }">
      <div class="table">
        <table-list ref="myTable" :tableData="state.tableList" :tableProps="state.tableProps" :total="state.total"
          @changePage="handleChangePage">
          <template #roleName="{ record }">
            <a-button class="custom-style" type="link" @click="handleRoleName(record)">{{ record.roleName }}</a-button>
          </template>
        </table-list>
      </div>
    </a-card>

    <!-- 弹窗 -->
    <div class="modal-box">
      <a-modal :maskClosable="false" :closable="false" v-model:open="state.isOpenModal" title="编辑用户"
        @ok="handleOpenModalOk" @cancel="handleCancelModalOk">
        <div>
          <a-form :labelCol="{ span: 4 }" labelAlign="left" :model="formState" name="basic" autocomplete="off">
            <a-form-item label="角色名称" name="roleName" :rules="[{ required: true, message: '该字段是必填字段' }]">
              <a-input v-model:value="formState.roleName" placeholder="请输入角色名称" />
            </a-form-item>
            <a-form-item label="产品" name="roleCode" :rules="[{ required: true, message: '该字段是必填字段' }]">
              <a-select :disabled="state.isOpenModal" v-model:value="formState.roleCode" placeholder="请选择">
                <a-select-option :value="item.appCode" v-for="item in state.appsList" :key="item.id">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="描述" name="roleDescription">
              <a-textarea v-model:value="formState.roleDescription" placeholder="请输入描述" />
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { onMounted, reactive, toRaw } from 'vue'
import TableList from "@/components/TableList/index.vue";
import { getPageList, getAppsList, getEditRoles } from './index.api'
import { message } from 'ant-design-vue';
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'

const router = useRouter()

const formState = reactive({
  roleName: '',
  roleCode: '',
  roleDescription: '',
})
// data
const state = reactive({
  pageNation: {
    pageNum: 1,
    pageSize: 10
  },
  tableList: [],
  tableProps: [
    {
      id: 1,
      title: "角色名称",
      dataIndex: "roleName",
      key: "roleName",
      ellipsis: true,
      slot: "roleName",
      hideTooltip: true,
    },
    {
      id: 2,
      title: "创建者",
      dataIndex: "creator",
      key: "creator",
      ellipsis: true,
    },
    {
      id: 3,
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      ellipsis: true,
    },
    {
      id: 4,
      title: "更新时间",
      dataIndex: "modifyTime",
      key: "modifyTime",
      ellipsis: true,
    },

    {
      id: 99,
      title: "操作",
      dataIndex: "operation",
      slot: "operation",
      justifyContent: "flex-start",
      width: 60,
      hideTooltip: true,
      buttons: [
        {
          name: "编辑",
          onclick: ({ record }) => {
            Object.assign(formState, record)
            getApps()
          },
        },
      ]
    },
  ],
  total: 0,
  // 弹窗
  isOpenModal: false,
  appsList: [],
  currentItem: {}
})

const handleChangePage = ({ pageNum, pageSize }: { pageNum: number, pageSize: number }) => {
  state.pageNation = {
    pageNum: pageNum,
    pageSize: pageSize
  }
  getList()
}


const handleRoleName = (data) => {
  router.push({
    path: '/platformManagement/roleManagement/detail',
    query: {
      appCode: data.appCode,
      roleCode: data.roleCode
    }
  })
}

const getApps = () => {
  getAppsList().then((res) => {
    if (res.code == 200) {
      state.appsList = res?.data ?? []
      state.isOpenModal = true
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

const handleOpenModalOk = () => {
  let data = toRaw(formState)
  let params = {
    appCode: 'ALT',
    ...data,
  }
  getEditRoles(params).then((res) => {
    if (res.code == 200) {
      message.success('操作成功')
      getList()
      handleCancelModalOk()
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

const handleCancelModalOk = () => {
  state.isOpenModal = false
  formState.roleName = ""
  formState.roleCode = ""
  formState.roleDescription = ""
}

function getList() {
  let params = {
    appCode: "ALT",
    pageInfo: state.pageNation,
    sortFields: [],
  }
  getPageList(params).then((res) => {
    if (res.code == 200) {
      state.tableList = res.data && res.data.length > 0 ? res.data.map((item) => {
        return {
          ...item,
          createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
          modifyTime: dayjs(item.modifyTime).format('YYYY-MM-DD HH:mm:ss'),
        }
      }) : []
    }
  }).catch((err) => {
    console.log(err, 'err')
  })
}


function query() {
  getList()
}

onMounted(() => {
  query()
})
</script>

<style scoped lang='less'>
.search-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}


:deep(.search-container .search-box .ant-input) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

:deep(.search-container .search-box .ant-btn) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.table {
  width: 100%;
}

.active-status-box {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #52C41A;
  margin-right: 8px;
}


.modal-box .ant-modal-content {
  border-radius: 10px;
  overflow: hidden;
}

.custom-style {
  padding: 0 !important;
  margin-right: 10px !important;
  // margin-top: 8px;
  word-break: break-all;
  word-wrap: break-word;
  width: auto;
}
</style>