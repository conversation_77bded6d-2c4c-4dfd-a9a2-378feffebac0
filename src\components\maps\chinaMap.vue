<template>
  <div class="investment-screen">
    <span
      class="areaTitle"
      v-if="currentLevel == 'country' && store.leftcurrentDimension == true"
      >{{ store.leftForArea }}</span
    >
    <span
      class="areaTitle"
      v-else-if="currentLevel == 'province'"
      >{{ store.leftForCityProvince }}</span
    >
    <span
      class="areaTitle"
      v-else-if="currentLevel == 'city'"
      >{{ store.leftForUniName }}</span
    >
    <span
      class="areaTitle"
      v-else-if="currentLevel == 'country' && store.leftcurrentDimension == false"
    ></span>
    <!-- 地图容器 -->
    <div
      ref="chartRef"
      class="map-chart"
    ></div>

    <a-drawer
      v-model:open="drawerVisible"
      width="400px"
      :closable="true"
      :footer="null"
    >
      <div class="modalMain">
        <div class="bottonModal">
          <h1 class="classH1">基本信息</h1>
          <div>
            <div class="bottonPad">
              <p class="p1">全部</p>
              <p class="p2">发病种类</p>
            </div>
            <div class="bottonPad">
              <p class="p1">已上报</p>
              <p class="p2">是否上报</p>
            </div>
            <div class="bottonPad">
              <p class="p1">{{ selectedPoint.name }}</p>
              <p class="p2">上报机构名称</p>
            </div>

            <div class="bottonPad">
              <p class="p1">上报人数</p>
              <p class="p2">{{ selectedPoint.value[2] }}</p>
            </div>
          </div>
        </div>
      </div>
    </a-drawer>

    <a-drawer
      v-model:open="open"
      class="custom-class"
      root-class-name="root-class-name"
      :root-style="{ color: 'blue' }"
      placement="right"
      @after-open-change="afterOpenChange"
    >
      <div class="modalMain">
        <div class="topModal">
          <h1 class="classH1">基本信息</h1>
          <div class="flexClass">
            <div>
              <p class="p1">{{ rightData.value }}</p>
              <p class="p2">展区</p>
            </div>
            <div>
              <p class="p1">暂无数据</p>
              <p class="p2">上报机构</p>
            </div>
            <div>
              <p class="p1">{{ rightData.totalInfectiousCnt }}</p>
              <p class="p2">上报人数</p>
            </div>
          </div>
        </div>
        <div class="bottonModal">
          <h1 class="classH1">确诊传染病分布</h1>
          <div>
            <a-directory-tree
              v-model:expandedKeys="expandedKeys"
              v-model:selectedKeys="selectedKeys"
              :tree-data="treeData"
              showIcon="false"
            />
          </div>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, getCurrentInstance, computed, watch, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts'
import { allProvincesDistribution, dimensionIndicator, allCitiesDistribution, allPatientUnitsDistribution,allDimensionIndicator } from './gisIndicator'
import { cityAdcode } from '@/utils/generateCityAdcode'
import { useSharedStore } from '@/store/geoState/state'
import { storeToRefs } from 'pinia'
import { message } from 'ant-design-vue'
const store = useSharedStore()
const { storeInfectiousType, dValue } = storeToRefs(store)

const { proxy }: any = getCurrentInstance()
const props = defineProps({
  dateRangeTimes: {
    type: Array,
    required: true
  },
  dataSelectType: {
    type: String,
    required: true
  }
})
const getInfectiousTypeFromAuto = ref<any>(props.dataSelectType)
const expandedKeys = ref<string[]>([]) // 控制展开的节点
const selectedKeys = ref<string[]>([]) // 当前选中的节点

// 转换树
const treeData = computed(() => {
  const categories = [rightData.value.ainfectiousDiseases, rightData.value.binfectiousDiseases, rightData.value.cinfectiousDiseases, rightData.value.otherInfectiousDiseases]

  return categories.map((category) => ({
    key: category.infectiousType, // 父节点 key
    title: `${category.infectiousType}传染病   (${category.infectiousCnt})`, // 父节点显示内容
    children: category.concreteInfectiousTypeCnt.map((child: any) => ({
      key: `${category.infectiousType}-${child.key}`, // 子节点 key
      title: `${child.key}    (${child.cnt})` // 子节点显示内容
    }))
  }))
})

watch(
  props,
  (newVal: { dataSelectType: any }) => {
    console.log(newVal, 'newValnewVal')
    getInfectiousTypeFromAuto.value = newVal.dataSelectType
    console.log(currentLevel.value, 'currentLevel.value')

    if (currentLevel.value == 'country') {
      fetchHeatmapData(gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
    } else if (currentLevel.value == 'province') {
      const provinceColor = getRegionColor(store.leftForCityProvince)
      loadProvinceMap(store.leftForCityProvince, provinceColor)
      fetchProvinceScatterData(store.leftForCityProvince, gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
    } else {
      const provinceColor = getRegionColor(store.leftForCityProvince)
      loadCityMap(store.leftForUniName, provinceColor)
      fetchCityScatterData(store.leftForUniName, gisDateRangeTime.value)
    }
  }
  // { deep: true }
)
const open = ref(false)
const showDrawer = () => {
  open.value = true
}
const afterOpenChange = (bool: any) => {
  console.log('open', bool)
}
const selectedPoint = ref<any>()
const getDimension = ref<any>('')
const getInfectiousType = ref<any>('')
const gisDateRangeTime = ref<any>(props.dateRangeTimes)
const rightData = ref<any>()

watch(props, (newVal) => {
  console.log(newVal, 'newValnewVal')
  gisDateRangeTime.value = newVal.dateRangeTimes
  console.log(currentLevel.value, 9000000)
  // nextTick(() => {
  if (currentLevel.value == 'country') {
    fetchHeatmapData(gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
  } else if (currentLevel.value == 'province') {
    const provinceColor = getRegionColor(store.leftForCityProvince)
    console.log(provinceColor, 'provinceColor4')
    loadProvinceMap(store.leftForCityProvince, provinceColor)
    fetchProvinceScatterData(store.leftForCityProvince, gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
  } else {
    console.log(store.leftForUniName, 55500)
    const provinceColor = getRegionColor(store.leftForCityProvince)
    loadCityMap(store.leftForUniName, provinceColor)
    fetchCityScatterData(store.leftForUniName, gisDateRangeTime.value)
  }
  // })
})

const getRightDimensionIndicator = async (date: any[]) => {
  const obj = {
    dimension: '',
    from: date[0],
    to: date[1],
    value: ''
  }
  if (currentLevel.value === 'city') {
    obj.dimension = store.leftForUniDimension
    obj.value = store.leftForUniName
  } else if (currentLevel.value === 'province') {
    obj.dimension = store.leftForCityDimension
    obj.value = store.leftForCityProvince
  } else {
  }
  await dimensionIndicator(obj).then((res: any) => {
    console.log(res, '维度指标')

    if (res?.code == 0) {
      rightData.value = res.data

      showDrawer()
    } else {
      proxy.$message.error(res?.errorMsg)
    }
  })
}
// 地图 DOM 容器
const chartRef = ref<HTMLDivElement | null>(null)

// 当前地图层级
const currentLevel = ref<'country' | 'province' | 'city' | 'uni'>('country')
const drawerVisible = ref<boolean>(false) // 控制Drawer显示与否
// 当前省名
const currentProvince = ref<string>('')
// 当前市名
const currentCity = ref<string>('')

// 初始化 ECharts 实例
let chartInstance: echarts.ECharts | null = null
const provinceData = ref<any>([])
const cityData = ref<any>([])
const provinceNmae = ref<any>('')
const provinceAliaName = ref<any>('')
const maxHeatValue = ref<any>('')

// 全国热力数据
const heatmapData = ref<any>([])
// 热力图最大值
const getMaxHeatValue = (data: any[]) => {
  let max = 0
  data.forEach((item: any) => {
    if (item.value[2] > max) {
      max = item.value[2]
    }
  })
  return max
}
// 分区信息
const regions = [
  {
    name: '新疆军区',
    alias: 'xinjiang',
    provinces: ['新疆维吾尔自治区'],
    color: '#209E85'
  },
  {
    name: '西藏军区',
    alias: 'xizang',
    provinces: ['西藏自治区'],
    color: '#7474FF'
  },
  {
    name: '东部战区',
    alias: 'dongbu',
    provinces: ['江苏省', '上海市', '浙江省', '福建省', '江西省', '安徽省'],
    color: '#A53535'
  },
  {
    name: '南部战区',
    alias: 'nanbu',
    provinces: ['广东省', '广西壮族自治区', '海南省', '云南省', '湖南省', '贵州省'],
    color: '#813188'
  },
  {
    name: '西部战区',
    alias: 'xibu',
    provinces: ['四川省', '甘肃省', '青海省', '宁夏回族自治区', '重庆市'],
    color: '#0395B9'
  },
  {
    name: '北部战区',
    alias: 'beibu',
    provinces: ['辽宁省', '吉林省', '黑龙江省', '内蒙古自治区', '山东省'],
    color: '#405BC8'
  },
  {
    name: '中部战区',
    alias: 'zhongbu',
    provinces: ['天津市', '河北省', '陕西省', '山西省', '河南省', '湖北省'],
    color: '#DB6739'
  },

  {
    name: '北京卫戍区',
    alias: 'beijing',
    provinces: ['北京市'],
    color: '#C41D7F'
  },
  {
    name: '台湾',
    alias: 'taiwan',
    provinces: ['台湾省'],
    color: '#94A3B8'
  },
  {
    name: '香港特别行政区',
    alias: 'xianggang',
    provinces: ['香港特别行政区'],
    color: '#94A3B8'
  },
  {
    name: '澳门特别行政区',
    alias: 'aomen',
    provinces: ['澳门特别行政区'],
    color: '#94A3B8'
  },
  {
    name: '其他',
    alias: 'qita',
    provinces: [],
    color: ''
  },
  {
    name: '南海诸岛',
    alias: 'nanhai',
    provinces: ['南海诸岛'],
    color: '#94A3B8'
  }
]

// 构建区域的 `geo.regions`
const geoRegions = regions.flatMap((region) =>
  region.provinces.map((province) => ({
    name: province,
    itemStyle: {
      areaColor: region.color + '99', //分区颜色0.6
      borderColor: '#fff',
      borderWidth: 1
    },
    emphasis: {
      itemStyle: {
        areaColor: region.color,
        borderColor: '#fff',
        borderWidth: 1
      }
    }
  }))
)
// 转换省份名称为 JSON 文件名的方法
const convertProvinceNameToMapName = (name: string): string => {
  switch (name) {
    case '湖北省':
      return 'hubei'
    case '湖南省':
      return 'hunan'
    case '天津市':
      return 'tianjin'
    case '广东省':
      return 'guangdong'
    case '江苏省':
      return 'jiangsu'
    case '安徽省':
      return 'anhui'
    case '澳门特别行政区':
      return 'aomen'
    case '北京市':
      return 'beijing'
    case '重庆市':
      return 'chongqing'
    case '福建省':
      return 'fujian'
    case '甘肃省':
      return 'gansu'
    case '广西壮族自治区':
      return 'guangxi'
    case '贵州省':
      return 'guizhou'
    case '海南省':
      return 'hainan'
    case '河北省':
      return 'hebei'
    case '黑龙江省':
      return 'heilongjiang'
    case '河南省':
      return 'henan'
    case '江西省':
      return 'jiangxi'
    case '吉林省':
      return 'jilin'
    case '辽宁省':
      return 'liaoning'
    case '青海省':
      return 'qinghai'
    case '山东省':
      return 'shandong'
    case '上海市':
      return 'shanghai'
    case '山西省':
      return 'shanxi'
    case '陕西省':
      return 'shanxi1'
    case '四川省':
      return 'sichuan'
    case '台湾省':
      return 'taiwan'
    case '香港特别行政区':
      return 'xianggang'
    case '新疆维吾尔自治区':
      return 'xinjiang'
    case '西藏自治区':
      return 'xizang'
    case '云南省':
      return 'yunnan'
    case '浙江省':
      return 'zhejiang'
    case '宁夏回族自治区':
      return 'ningxia'
    case '内蒙古自治区':
      return 'neimenggu'

    default:
      return name // 如果没有映射，返回原名
  }
}
const provinceAbbreviations: { [key: string]: string } = {
  安徽省: '安徽',
  澳门特别行政区: '澳门',
  北京市: '北京',
  重庆市: '重庆',
  福建省: '福建',
  甘肃省: '甘肃',
  广东省: '广东',
  广西壮族自治区: '广西',
  贵州省: '贵州',
  海南省: '海南',
  河北省: '河北',
  河南省: '河南',
  黑龙江省: '黑龙江',
  湖北省: '湖北',
  湖南省: '湖南',
  江苏省: '江苏',
  江西省: '江西',
  吉林省: '吉林',
  辽宁省: '辽宁',
  内蒙古自治区: '内蒙古',
  宁夏回族自治区: '宁夏',
  青海省: '青海',
  山东省: '山东',
  上海市: '上海',
  山西省: '山西',
  陕西省: '陕西',
  四川省: '四川',
  台湾省: '台湾',
  天津市: '天津',
  新疆维吾尔自治区: '新疆',
  西藏自治区: '西藏',
  香港特别行政区: '香港',
  云南省: '云南',
  浙江省: '浙江'
}
// 根据名称获取 adcode
const getProvinceCode = (name: string): number | undefined => {
  const regionData: { [key: string]: number } = {
    北京市: 110000,
    天津市: 120000,
    河北省: 130000,
    山西省: 140000,
    内蒙古自治区: 150000,
    辽宁省: 210000,
    吉林省: 220000,
    黑龙江省: 230000,
    上海市: 310000,
    江苏省: 320000,
    浙江省: 330000,
    安徽省: 340000,
    福建省: 350000,
    江西省: 360000,
    山东省: 370000,
    河南省: 410000,
    湖北省: 420000,
    湖南省: 430000,
    广东省: 440000,
    广西壮族自治区: 450000,
    海南省: 460000,
    重庆市: 500000,
    四川省: 510000,
    贵州省: 520000,
    云南省: 530000,
    西藏自治区: 540000,
    陕西省: 610000,
    甘肃省: 620000,
    青海省: 630000,
    宁夏回族自治区: 640000,
    新疆维吾尔自治区: 650000,
    香港特别行政区: 810000,
    澳门特别行政区: 820000,
    台湾省: 710000
  }

  return regionData[name]
}
const provinceColor = ref<any>('')
// 初始化地图
const initMap = (date: any, type: string) => {
  if (chartInstance) {
    chartInstance.dispose() // 销毁已有实例，避免重复渲染
  }
  if (!chartRef.value) return
  chartInstance = echarts.init(chartRef.value)

  chartInstance.on('click', async (params: any) => {
    if (params.name) {
      // 当前为国家级地图，点击进入省级地图
      if (currentLevel.value === 'country') {
        // 检查是否已经进入省级地图
        // 添加判断  如果地图不包含左侧展区下的省份 不下钻
        const isContained = store.getAreasProvince.some((item) => item === params.name)
        if (store.leftForCityProvince !== params.name && isContained) {
          currentProvince.value = params.name

          store.leftForCityProvince = params.name
          // 延迟更新currentLevel
          setTimeout(async () => {
            currentLevel.value = 'province'
            store.leftForCityDimension = '省'
            const provinceColor = getRegionColor(store.leftForCityProvince)
            console.log(provinceColor, 'provinceColor')
            await loadProvinceMap(store.leftForCityProvince, provinceColor)
            await fetchProvinceScatterData(store.leftForCityProvince, gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
          }, 100) // 100ms 延迟，确保地图加载完成后再更新层级
        } else {
          message.warning('请在地图上点击左侧柱形图包含的地区进行查看！')
        }
      }

      // 当前为省级地图，点击进入市级地图
      else if (currentLevel.value === 'province') {
        const arr = JSON.parse(JSON.stringify(store.getProvincesCity))
        const isContained = arr.some((item: any) => item === params.name)

        // 点击四个直辖市  地图只下钻到该直辖市 禁止继续下钻
        const targetArray = ['重庆市', '上海市', '天津市', '北京市']
        const isIncludes = targetArray.includes(params.name)

        // 检查是否已经进入市级地图
        // if (store.leftForUniName !== params.name && isContained && !isIncludes) {
        if (isContained && !isIncludes) {
          // store.leftForUniName = params.name
          store.updateleftForUniName(store, params.name)
          store.updateleftForUniDimension(store, '市')
          // 延迟更新currentLevel
          setTimeout(async () => {
            currentLevel.value = 'city'
            store.leftForUniDimension = '市'

            const provinceColor = getRegionColor(store.leftForCityProvince)

            await loadCityMap(store.leftForUniName, provinceColor)
            await fetchCityScatterData(store.leftForUniName, gisDateRangeTime.value)
          }, 100) // 100ms 延迟
        } else {
          message.warning('请在地图上点击左侧柱形图包含的地区进行查看！')
        }
      }
    }
  })
}
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const getRegionColor = (provinceName: string) => {
  const region = regions.find((region) => region.provinces.includes(provinceName))
  return region ? region.color : '#ccc' // 默认颜色为灰色
}

// rgba颜色转换方法，接收颜色值和透明度，返回rgba格式颜色
const rgbaColor = (color: string, alpha: number) => {
  // 如果输入的颜色已经是rgba格式，直接返回
  if (color.startsWith('rgba')) {
    return color
  }

  // 如果是十六进制颜色，转换为rgba
  if (color.startsWith('#')) {
    let r: number, g: number, b: number
    if (color.length === 7) {
      r = parseInt(color.substring(1, 3), 16)
      g = parseInt(color.substring(3, 5), 16)
      b = parseInt(color.substring(5, 7), 16)
    } else if (color.length === 4) {
      r = parseInt(color[1] + color[1], 16)
      g = parseInt(color[2] + color[2], 16)
      b = parseInt(color[3] + color[3], 16)
    }
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }

  // 如果是其他类型的颜色（如rgb），则直接替换透明度
  if (color.startsWith('rgb')) {
    return color.replace('rgb', 'rgba').replace(')', `, ${alpha})`)
  }

  return color // 默认返回原色
}

const renderCountryHeatmap = () => {
  // if (!chartInstance || !heatmapData.value.length) return
  // console.log(geoRegions, 'geoRegions')
  // geoRegions.map(item=>{
  //   if(item.name=='南海诸岛'){
  //     item.itemStyle.borderColor='#5E6580'
  //   }
  // })

  if (!chartInstance) return
  const option: echarts.EChartsOption = {
    geo: {
      map: 'china',
      roam: true, // 允许地图缩放和平移
      zoom: 1.5,
      // layoutCenter: ['80%', '90%'],//位置
      layoutCenter: ['46%', '65%'], //位置
      layoutSize: '85%', //大小
      itemStyle: {
        // areaColor: '#FFC107',
        // borderColor: '#fff',
        // areaColor: {
        //   type: 'pattern',
        //   image: domImg, //配置图片
        //   repeat: 'repeat' //可选值repeat、no-repeat、repeat-x、repeat-y
        // }
      },

      scaleLimit: { min: 0, max: 3 }, // 缩放级别

      emphasis: {
        itemStyle: {
          // areaColor: '#ffecb3'
        }
      },

      label: {
        show: true,
        // color: '#ffffff',
        // color: (params: any) => {
        //   if (params.name == '南海诸岛') {
        //     return '#5E6580'
        //   } else {
        //     return '#ffffff'
        //   }
        // },
        fontSize: 10,

        formatter: (params: any) => {
          return provinceAbbreviations[params.name] || params.name
        }
      },
      // tooltip: {
      //   show: true,
      //   confine: true,
      //   formatter:  (params)=>{
      //     return ['厚制品90% 薄制品10%', 'XX膜 XX厚度  30吨；', 'XX膜 XX厚度  30吨；', 'XX膜 XX厚度  30吨；'].join('<br>')
      //   }
      // },
      
      regions: geoRegions //应用分区颜色
    },

    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        if (params.value && Array.isArray(params.value)) {
          const [lng, lat, value] = params.value // 解构提取经纬度和热力值
          return `${params.name}<br />经度: ${lng.toFixed(2)}<br />纬度: ${lat.toFixed(2)}<br />热力值: ${value}`
        }
        return params.name // 如果数据格式不匹配，显示名称
      }
    },

    visualMap: {
      min: 0,
      max: maxHeatValue.value, // 根据热力值范围调整
      calculable: true,
      // orient: 'vertical',
      // left: 'left',
      // top: 'center',
      left: '0', //组件离容器左侧的距离,'left', 'center', 'right','20%'
      top: 'auto', //组件离容器上侧的距离,'top', 'middle', 'bottom','20%'
      right: 'auto', //组件离容器右侧的距离,'20%'
      bottom: '15%',
      inRange: {
        color: ['#FF4D4F', '#FF8000', '#FFBF00', '#FFDE36']
        // color: ['#FFDE36', '#FFBF00', '#FF8000', '#FF4D4F'],
      },
      text: ['高', '低'],
      show: true
      // textStyle: {
      //   color: '#000'
      // }
    },
    // visualMap: {
    //   min: 0,
    //   max: maxHeatValue.value,
    //   left: '3%',
    //   bottom: '5%',
    //   calculable: true,
    //   seriesIndex: [0],
    //   inRange: {
    //     color: ['#FF4D4F', '#FF8000', '#FFBF00', '#FFDE36']
    //   },
    //   textStyle: {
    //     color: '#24CFF4'
    //   }
    // },
    series: [
      {
        name: '全国热力图',
        type: 'heatmap',
        coordinateSystem: 'geo',
        pointSize: 10, //调整热力点半径大小
        blurSize: 1, //设置模糊程度，值越小点越清晰
        data: heatmapData.value // 热力图数据
      }
    ]
  }

  chartInstance.setOption(option)
}

// 加载全国地图
const loadCountryMap = async () => {
  const mapJson = await import('@/assets/chinaJson/china.json')
  echarts.registerMap('china', mapJson.default)

  chartInstance?.clear()
  renderCountryHeatmap()
}
const calculateProvinceCenter = (geoJson: any): [number, number] => {
  const features = geoJson.features
  console.log(features, 'features')
  const [minLng, minLat, maxLng, maxLat] = features.reduce(
    (bounds: number[], feature: { geometry: { coordinates: any[] } }) => {
      const coords = feature.geometry.coordinates.flat(2)
      console.log(coords, 'coords')

      coords.forEach(([lng, lat]: [number, number]) => {
        bounds[0] = Math.min(bounds[0], lng)
        bounds[1] = Math.min(bounds[1], lat)
        bounds[2] = Math.max(bounds[2], lng)
        bounds[3] = Math.max(bounds[3], lat)
      })
      return bounds
    },
    [Infinity, Infinity, -Infinity, -Infinity]
  )
  return [(minLng + maxLng) / 2, (minLat + maxLat) / 2]
}

// 加载省级地图
const loadProvinceMap = async (provinceName: string, color: string) => {
  const mapName = convertProvinceNameToMapName(provinceName)
  // 添加判断  如果地图不包含左侧展区下的省份 不下钻
  const isContained = store.getAreasProvince.some((item) => item === provinceName)
  if (isContained) {
    try {
      const mapJson = await import(`@/assets/chinaJson/${mapName}.json`)
      echarts.registerMap(mapName, mapJson.default)
      const center = calculateProvinceCenter(mapJson)
      console.log(center, 'center')
      // getRightDimensionIndicator(gisDateRangeTime.value)
      chartInstance?.setOption({
        geo: {
          map: mapName,
          center,
          layoutCenter: ['50%', '50%'],
          layoutSize: '80%',
          roam: true,
          zoom: 1.2,
          backgroundColor: color,
          itemStyle: {
            areaColor: rgbaColor(color, 0.6),
            borderColor: '#fff'
          },
          emphasis: {
            itemStyle: {
              areaColor: rgbaColor(color, 1)
            }
          },

          label: {
            show: true,
            // color: '#5E6580',

            fontSize: 10,
            formatter: (params: any) => {
              return params.name
            }
          }
        },
        series: [
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            data: [] // 初始为空，等待请求返回散点数据
          }
        ],
        visualMap: {
          show: false
        },

        tooltip: {
          trigger: 'item',
          formatter: (params: any) => `机构名称: ${params.name}<br/>传染病数量: ${params.value[2].toLocaleString()} 例`
        }
      })
    } catch (error) {
      console.error('加载省份地图失败:', error)
    }
  } else {
    message.warning('该地区暂无任何发病数据！')
  }
}

// 加载市级地图
const loadCityMap = async (cityName: string, color: string) => {
  console.log(cityName, 'cityName')
  console.log(store.leftForCityProvince, '00066')

  const provinceCode = getProvinceCode(store.leftForCityProvince) // 获取省级编码
  const cityCode = parseInt(cityAdcode[cityName]) // 获取市级编码
  console.log(provinceCode, cityCode, 'cityCode')
  // getRightDimensionIndicator(gisDateRangeTime.value)
  try {
    const mapJson = await import(`@/assets/100000/${provinceCode}/${cityCode}.json`)
    echarts.registerMap(cityName, mapJson.default)
    const center = calculateProvinceCenter(mapJson)
    chartInstance?.setOption({
      geo: {
        map: cityName,
        center,
        layoutCenter: ['50%', '50%'],
        layoutSize: '80%',
        roam: true,
        zoom: 1.2,
        backgroundColor: color,
        itemStyle: {
          areaColor: rgbaColor(color, 0.6),
          borderColor: '#fff'
        },
        emphasis: {
          itemStyle: {
            areaColor: rgbaColor(color, 1)
          }
        },
        label: {
          show: true,
          // color: '#5E6580',

          fontSize: 10,
          formatter: (params: any) => {
            return params.name
          }
        }
      },
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          data: [] // 初始为空，等待请求返回散点数据
        }
      ],
      visualMap: {
        show: false
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => `机构名称: ${params.name}<br/>传染病数量: ${params.value[2].toLocaleString()} 例`
      }
    })
  } catch (error) {
    console.error('加载市级地图失败:', error)
  }
}
const getAllMAPData=ref<any>()
// 请求全国热力图数据
const fetchHeatmapData = async (date: any[], type: string) => {
  const obj = {
    from: date[0],
    to: date[1],
    infectiousType: type,
    "prevDimensionFilter": "",
    "queryDimension": "战区",
  }
  const data = []
  getAllMAPData.value=[]
  try {
    const res = await allDimensionIndicator(obj)
    console.log(res.data, 'heat')
    getDimension.value = res.data.curDimension;
    
    res.data.curDimensionIndicators.forEach(
      (
        item: {
          // province: any
          // infectiousType: any
          // curDimension: any
          // prevDimension: any
          // prevValue: any
          // curDimensionIndicators:any
          // alias: any
          // cnt: any
          curValue:any
          cnt:any
          alias:any
          longitude:any
          nextDimensionIndicators:any[]
        },
        index: any
      ) => {
        item.nextDimensionIndicators.forEach(async (j)=>{
          const mapName = convertProvinceNameToMapName(j.curValue)
          if (j.latitude == null && j.longitude == null && j.cnt !== 0) {
              const map = await import(`@/assets/chinaJson/${mapName}.json`)
              j.latitude = map.features[0].properties.center[0]
              j.longitude = map.features[0].properties.center[1]
            }
            getAllMAPData.value.push({
                name: j.curValue,
                lat: j.latitude,
                lng: j.longitude,
                value: j.cnt
            })
        })
      }
    )

    heatmapData.value =  getAllMAPData.value.map((item: any) => ({
      name: item.name,
      value: [item.lng, item.lat, item.value] // [lng, lat, heat value]
    }))
    console.log(heatmapData.value, 'heatmapData.value')

    // getRightDimensionIndicator(date)
    maxHeatValue.value = getMaxHeatValue(heatmapData.value)
    loadCountryMap()
  } catch (error) {
    console.error('获取热力图数据失败:', error)
  }
}

// 请求省级散点图数据
const fetchProvinceScatterData = async (provinceName: string, date: any, type: string) => {
  store.updaterightForCityProvince(store, provinceName)
  console.log(provinceName, type)

  const obj = {
    from: date[0],
    to: date[1],
    infectiousType: type,
    province: store.leftForCityProvince,
    "prevDimensionFilter": store.leftForCityProvince,
    "queryDimension": "市",
  }
  console.log(provinceName, 7700)

  try {
    const data: { lng: any; lat: any; value: any; name: any }[] = []
    getAllMAPData.value=[]
    const res = await allDimensionIndicator(obj)
    // // await allCitiesDistribution(obj).then((res: any) => {
    // console.log(res.data, 'heat')
    // res.data.map((item: { gisGeographicInformation: any[] }) => {
    //   if (item.gisGeographicInformation.length > 0) {
    //     item.gisGeographicInformation.map((j) => {
    //       data.push({
    //         name: j.patientUnit,
    //         lng: j.longitude,
    //         lat: j.latitude,
    //         value: j.cnt
    //       })
    //     })
    //   }
    // })
    // // })
    res.data.curDimensionIndicators.forEach(
      (
        item: {
          curValue:any
          cnt:any
          alias:any
          longitude:any
          nextDimensionIndicators:any[]
        },
        index: any
      ) => {
        item.nextDimensionIndicators.forEach(async (j)=>{
          const mapName = convertProvinceNameToMapName(j.curValue)
          if (j.latitude == null && j.longitude == null && j.cnt !== 0) {
              const map = await import(`@/assets/chinaJson/${mapName}.json`)
              j.latitude = map.features[0].properties.center[0]
              j.longitude = map.features[0].properties.center[1]
            }
            getAllMAPData.value.push({
                name: j.curValue,
                lat: j.latitude,
                lng: j.longitude,
                value: j.cnt
            })
        })
      }
    )
    provinceData.value = getAllMAPData.value.map((item: any) => ({
      name: item.name,
      value: [item.lng, item.lat, item.value] // [lng, lat, heat value]
    }))

    chartInstance?.setOption({
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          data: provinceData.value,
          // symbolSize: 10,
          symbolSize: (val: any) => {
            return Math.sqrt(val[2]) * 0.5
          },
          label: {
            show: true,
            position: 'right',
            formatter: '{b}'
          },
          itemStyle: {
            color: '#FF5722'
          }
        }
      ],
      // 这里修改散点颜色
      // visualMap:{
      //   dimension:1,
      //   inRange:{
      //     color:['#FFDE36','#eac763','#d94e5d']
      //   }
      // },
    })
    // 添加点击事件监听
    chartInstance?.on('click', (params) => {
      // 只有散点被点击时才处理
      if (params.componentType === 'series' && params.seriesType === 'scatter') {
        selectedPoint.value = params.data
        const { name, value } = selectedPoint.value
        // drawerVisible.value = true
        console.log(selectedPoint.value, 'selectedPoint.value')

        // // 处理点击点的信息
        console.log(`点击的点: ${name}`)
        console.log(`经度: ${value[0]}, 纬度: ${value[1]}, 数量: ${value[2]}`)
        // alert(`点击的点: ${name}\n经度: ${value[0]}\n纬度: ${value[1]}\n数量: ${value[2]}`)
      }
    })
  } catch (error) {
    console.error('获取省级散点图数据失败:', error)
  }
}
// // 监听 bValue 的变化并重新发送请求
watch(
  () => store.leftForCityProvince,
  (newValue: string) => {
    console.log(newValue, 34343)

    // console.log(store.leftForCityProvince, 9999)
    console.log(store.leftForCityDimension, 88888)
    // 添加判断  如果地图不包含左侧展区下的省份 不下钻
    const isContained = store.getAreasProvince.some((item) => item === store.leftForCityProvince)

    if (store.leftForCityDimension == '省' && isContained) {
      const provinceColor = getRegionColor(store.leftForCityProvince)
      console.log(provinceColor, 'provinceColor1')
      loadProvinceMap(newValue, provinceColor)
      fetchProvinceScatterData(newValue, gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
      currentLevel.value = 'province'
    } else {
    }
  }
)
watch(
  () => store.leftForUniName,
  (newValue: string) => {
    console.log(store.leftForUniName, 7777)
    console.log(store.leftForUniDimension, 6666)
    const provinceColor = getRegionColor(store.leftForCityProvince)
    // 点击四个直辖市  地图只下钻到该直辖市 禁止继续下钻
    const targetArray = ['重庆市', '上海市', '天津市', '北京市']
    const isIncludes = targetArray.includes(store.leftForUniName)
    if (store.leftForUniDimension == '市' && !isIncludes) {
      loadCityMap(newValue, provinceColor)
      fetchCityScatterData(newValue, gisDateRangeTime.value)
      currentLevel.value = 'city'
    }
  }
)
// 请求市级散点图数据
const fetchCityScatterData = async (cityName: string, date: any) => {
  console.log(cityName)
  console.log(currentLevel.value, 'currentLevel.value')

  store.updaterightForCityName(store, cityName)
  const obj = {
    from: date[0],
    to: date[1],
    infectiousType: getInfectiousTypeFromAuto.value,
    city: cityName,
    "prevDimensionFilter": cityName,
    "queryDimension": "区",
  }
  getAllMAPData.value=[]
  try {
    const data: { lng: any; lat: any; value: any; name: any }[] = []
    const res = await allDimensionIndicator(obj)
    // // await allPatientUnitsDistribution(obj).then((res: any) => {
    // console.log(res.data, 'heat')
    // res.data.map((item: { gisGeographicInformation: any[] }) => {
    //   if (item.gisGeographicInformation.length > 0) {
    //     item.gisGeographicInformation.map((j) => {
    //       data.push({
    //         name: j.patientUnit,
    //         lng: j.longitude,
    //         lat: j.latitude,
    //         value: j.cnt
    //       })
    //     })
    //   }
    // })
    // // })

    res.data.curDimensionIndicators.forEach(
      (
        item: {
          curValue:any
          cnt:any
          alias:any
          longitude:any
          nextDimensionIndicators:any[]
        },
        index: any
      ) => {
        item.nextDimensionIndicators.forEach(async (j)=>{
          const mapName = convertProvinceNameToMapName(j.curValue)
          if (j.latitude == null && j.longitude == null && j.cnt !== 0) {
              const map = await import(`@/assets/chinaJson/${mapName}.json`)
              j.latitude = map.features[0].properties.center[0]
              j.longitude = map.features[0].properties.center[1]
            }
            getAllMAPData.value.push({
                name: j.curValue,
                lat: j.latitude,
                lng: j.longitude,
                value: j.cnt
            })
        })
      }
    )

    cityData.value = getAllMAPData.value.map((item: any) => ({
      name: item.name,
      value: [item.lng, item.lat, item.value] // [lng, lat, heat value]
    }))

    chartInstance?.setOption({
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          data: cityData.value,
          // symbolSize: 10,
          symbolSize: (val: any) => {
            return Math.sqrt(val[2]) * 0.5
          },
          label: {
            show: true,
            position: 'right',
            formatter: '{b}'
          },
          itemStyle: {
            color: '#FF5722'
          }
        }
      ],
      // 这里修改散点颜色
      // visualMap:{
      //   dimension:1,
      //   inRange:{
      //     color:['#FFDE36','#eac763','#d94e5d']
      //   }
      // },
    })
    // 添加点击事件监听
    chartInstance?.on('click', (params) => {
      // 只有散点被点击时才处理
      if (params.componentType === 'series' && params.seriesType === 'scatter') {
        selectedPoint.value = params.data
        const { name, value } = selectedPoint.value
        // drawerVisible.value = true
        console.log(selectedPoint.value, 'selectedPoint.value')

        // // 处理点击点的信息
        console.log(`点击的点: ${name}`)
        console.log(`经度: ${value[0]}, 纬度: ${value[1]}, 数量: ${value[2]}`)
      }
    })
  } catch (error) {
    console.error('获取省级散点图数据失败:', error)
  }
}
// 返回上一级地图
watch(
  () => store.backCurrentLevel,
  (newValue: string) => {
    console.log(store.backCurrentLevel, 'backCurrentLevel')
    console.log(currentLevel.value, '444444')

    handleBack()
  }
)

// // 返回上一级地图
const handleBack = async () => {
  if (currentLevel.value === 'city') {
    // currentCity.value = null
    // store.leftForUniName = null

    const provinceColor = getRegionColor(store.leftForCityProvince)
    console.log(provinceColor, 'provinceColor2')
    await loadProvinceMap(store.leftForCityProvince, provinceColor)
    await fetchProvinceScatterData(store.leftForCityProvince, gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
    currentLevel.value = 'province'
  } else if (currentLevel.value === 'province') {
    // store.leftForCityProvince = null
    // alert(store.leftForUniName)
    // alert(1000)
    loadCountryMap()
    store.updateBackCurrentLevel(store, 'country')
    currentLevel.value = 'country'
  }
  // else if(currentLevel.value === 'uni'){
  //   currentLevel.value = 'city'
  //   const provinceColor = getRegionColor(store.leftForCityProvince)
  //   console.log(provinceColor, 'provinceColor2')
  //   await loadProvinceMap(store.leftForCityProvince, provinceColor)
  //   await fetchProvinceScatterData(store.leftForCityProvince, gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
  // }
}

// 初始化地图并加载全国数据
onMounted(() => {
  initMap(gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
  fetchHeatmapData(gisDateRangeTime.value, getInfectiousTypeFromAuto.value)
  window.addEventListener('resize', handleResize)
})
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="less">
.investment-screen {
  background-color: transparent;
  width: 100%;
  height: 100%;
  position: relative;

  // display: flex;
  // justify-content: center;
  // align-items: center;
}
.areaTitle {
  position: absolute;
  top: -6.2%;
  left: 7%;
  font-size: 16px;
  font-family: PingFang SC;
  font-weight: bold;
}
.backIcon {
  width: 50px;
  height: 50px;
  // border: 1px solid red;
  position: absolute;
  left: 25%;
  top: 2%;
  cursor: pointer;
}
.map-chart {
  width: 100%;
  height: 100%;
  // background-image: url('@/assets/icons/mapbg.svg');
  // background-size: 100% 100%;
  // background-repeat: no-repeat;
}
.modalMain {
  padding: 20px 10px;
}
.topModal {
  border-bottom: 1px solid #e8efff;
}
.classH1 {
  color: #209e85;
  font-size: 18px;
  font-weight: bold;
  font-family: YouSheBiaoTiHei;
  margin-bottom: 20px;
}
.flexClass {
  margin: 20px 0;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.p1 {
  color: #07123c;
  font-size: 18px;
  font-family: Noto Sans SC;
  font-weight: bold;
}
.p2 {
  color: #8f94a7;
  font-size: 14px;
  font-family: Noto Sans SC;
  // font-weight: bold;
}
.bottonModal {
  padding: 20px 0;
}
.bottonPad {
  border: 1px solid #e8efff;
}
.classH1 {
  color: #209e85;
  font-size: 18px;
  font-weight: bold;
  font-family: YouSheBiaoTiHei;
}
.flexClass {
  margin: 20px 0;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.p1 {
  color: #07123c;
  font-size: 18px;
  font-family: Noto Sans SC;
  font-weight: bold;
}
.p2 {
  color: #8f94a7;
  font-size: 14px;
  font-family: Noto Sans SC;
  // font-weight: bold;
}
.bottonModal {
  padding: 20px 0;
}
.bottonPad {
  font-size: 18px;
  font-weight: bold;
  font-family: YouSheBiaoTiHei;
  margin: 20px 0;
  border: none;
}
.flexClass {
  margin: 20px 0;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.p1 {
  color: #07123c;
  font-size: 18px;
  font-family: Noto Sans SC;
  font-weight: bold;
}
.p2 {
  color: #8f94a7;
  font-size: 14px;
  font-family: Noto Sans SC;
  // font-weight: bold;
}
.bottonModal {
  padding: 20px 0;
}

::v-deep .ant-tree-title {
  font-size: 16px;
  line-height: 1.5;
}

::v-deep .ant-tree-child-tree {
  margin-top: 10px;
}

::v-deep .ant-tree-node-content-wrapper {
  margin-bottom: 5px;
}

::v-deep .ant-tree-title:first-of-type {
  font-weight: bold;
}
</style>
