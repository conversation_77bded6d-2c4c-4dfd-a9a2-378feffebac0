<template>
  <div class="wrapper">
    <a-card
      title="高原病知识库"
      :bordered="false"
      :bodyStyle="{ minHeight: 'calc(100vh - 180px)' }"
    >
    <div class="flex-1 flex flex-row">
      <div class="card noboard">
        <TreeComponent
          :treeData="treeData"
          @nodeClicked="handleChildEvent"
          @getValue="getSonValue"
        />
      </div>
      <div class="lineDiv"></div>
      <div class="card ml-4 flex-1">
        <a-table
          align="left"
          :columns="columns"
          :data-source="dataSource"
          bordered
          :pagination="false"
          border="none"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a-tooltip>
                <template #title>{{ record.name }}</template>
                <a class="single-line">
                  {{ record.name }}
                </a>
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'description'">
              <a-tooltip>
                <template #title>{{ record.description }}</template>
                <a class="single-line">
                  {{ record.description }}
                </a>
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'modifier'">
              <a>
                {{ record.modifier }}
              </a>
            </template>
            <template v-else-if="column.key === 'modifyTime'">
              <a-tooltip>
                <template #title>{{ record.modifyTime }}</template>
                <a class="single-line">
                  {{ record.modifyTime }}
                </a>
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'status'">
              <span class="tagsStyle">
                <a-tag v-if="record.status == 0" class="tags" color="default">
                  <span style="color: green">正常</span>
                </a-tag>
                <a-tag v-else-if="record.status == 1" color="default">
                  <span style="color: #cd201f">删除</span>
                </a-tag>
                <a-tag v-else-if="record.status == 2" color="default">
                  <span style="color: #3b5999">待审核</span>
                </a-tag>
                <a-tag v-else color="default">
                  <span style="color: #87d068">已审核</span>
                </a-tag>
              </span>
            </template>
            <template v-else-if="column.key === 'operation'">
              <div class="operator">
                <span class="operatorButton">
                  <a @click="viewFile(record.storagePath)">查看</a>
                </span>
                <!-- <span class="operatorButton">
                  <a @click="auditFile(record.key)">审核</a>
                </span> -->
              </div>
            </template>
          </template>
        </a-table>
        <a-pagination
        v-show="dataLength"
          style="float: right; margin-top: 16px"
          :total="totals"
          :show-total="(totals: any) => `共 ${totals} 条`"
          :current="current"
          :page-size="size"
          show-size-changer
          @showSizeChange="onShowSizeChange"
          @change="handlePageChange"
        />
      </div>
    </div>
  </a-card>
    
  </div>
</template>

<script lang="ts" setup>
import TreeComponent from "@/components/tree-menu/TreeComponent.vue";
import {
  onMounted,
  onBeforeMount,
  defineComponent,
  watch,
  ref,
  reactive,
  getCurrentInstance,
} from "vue";

import { getKnowledgeNodeTree, guideQueryPage, getPdfFile } from "@/api/getKnowledgeInfo.ts";

import { saveAs } from "file-saver";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { operationLog } from '@/api/log'

const searchValue = ref("");

const getSonValue = (item: string) => {
  console.log(item, "son");
  searchValue.value = item;
  getKnowledgeTreeList(searchValue.value);
};

const { proxy }: any = getCurrentInstance();
const treeData = ref<any[]>([]);
const isLoading = ref(true);
async function getKnowledgeTreeList(params: string) {
  await getKnowledgeNodeTree({ keyword: params }).then((res: any) => {
    if (res?.code == 0) {
      treeData.value = res.data;
      isLoading.value = false;
      console.log("获取到的树形数据部分节点信息:", treeData.value.slice(0, 3));
    } else {
      proxy.$message.error(res?.errorMsg);
      isLoading.value = false;
    }
  });
}
interface DataItem {
  name: string;
  description: string;
  modifier: string | null;
  modifyTime: string;
  status: number;
  id: number;
  nodeId: null;
  storagePath: string;
}

const fileBlob = null;
const auditFile = (key: string) => {};
const viewFile = async (key: string) => {
  await getPdfFile({ path: key }).then((res: any) => {
    if (res?.status == 200) {
      const file = new Blob([res.data], { type: "application/pdf" });
      saveAs(file, key);
      // 打开新页面并加载文件
      const newWindow = window.open("", "_blank");
      if (newWindow) {
        newWindow.onload = function () {
          console.log("新窗口已成功加载文件");
        };
        newWindow.location.href = URL.createObjectURL(file);
        // 转换为PDF并添加功能
        convertToPDF(newWindow.document);
      }
    } else {
      proxy.$message.error(res?.errorMsg);
    }
  });
  async function convertToPDF(document: Document) {
    const doc: any = new jsPDF();
    const canvas = await html2canvas(document.body);
    const imgData = canvas.toDataURL("image/png");
    const imgProps = doc.getImageProperties(imgData);
    const pdfWidth = doc.internal.pageSize.getWidth();
    const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    doc.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);

    // 实现模糊搜索功能
    const searchInput = document.createElement("input");
    searchInput.type = "text";
    searchInput.placeholder = "输入搜索内容";
    document.body.appendChild(searchInput); // 将input添加到新打开页面的body中

    searchInput.addEventListener("input", () => {
      const searchText = searchInput.value.toLowerCase();
      const pageElements = doc.internal.pages.map((page: any) => page.canvas).flat();
      pageElements.forEach((page: any) => {
        const pageContent = page.getContext("2d")!.getTextContent()!.toLowerCase();
        const isMatch = pageContent.includes(searchText);
        page.style.display = isMatch ? "block" : "hidden";
      });
    });

    // 实现分页功能
    const totalPages = doc.internal.getNumberOfPages();
    const paginationDiv = document.createElement("div");
    paginationDiv.style.textAlign = "center";
    for (let i = 1; i <= totalPages; i++) {
      const pageButton = document.createElement("button");
      pageButton.textContent = `${i}`;
      pageButton.addEventListener("click", () => {
        doc.setPage(i);
        const pageCanvas = doc.internal.pages[i - 1].canvas;
        pageCanvas.style.display = "block";
        for (let j = 1; j <= totalPages; j++) {
          if (j !== i) {
            doc.internal.pages[j - 1].canvas.style.display = "hidden";
          }
        }
      });
      paginationDiv.appendChild(pageButton);
    }
    document.body.appendChild(paginationDiv);

    // 实现缩放功能
    const zoomInButton = document.createElement("button");
    zoomInButton.textContent = "放大";
    zoomInButton.addEventListener("click", () => {
      const currentScale = doc.internal.scale;
      doc.internal.scale = currentScale * 1.2;
      doc.output("pdf");
    });
    const zoomOutButton = document.createElement("button");
    zoomOutButton.textContent = "缩小";
    zoomOutButton.addEventListener("click", () => {
      const currentScale = doc.internal.scale;
      doc.internal.scale = currentScale / 1.2;
      doc.output("pdf");
    });
    document.body.appendChild(zoomInButton);
    document.body.appendChild(zoomOutButton);

    doc.save("output.pdf");
  }
};

const columns = ref([
  {
    title: "指南名称",
    dataIndex: "name",
    key: "name",
    // width: '15%',
    ellipsis: true,
  },
  {
    title: "指南描述",
    dataIndex: "description",
    key: "description",
    ellipsis: true,
    // width: '25%'
  },
  {
    title: "更新者",
    dataIndex: "modifier",
    key: "modifier",
    // width: '10%'
  },
  {
    title: "更新时间",
    dataIndex: "modifyTime",
    key: "modifyTime",
    // width: '20%',
    ellipsis: true,
  },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   key: 'status',
  //   width: '10%'
  // },
  {
    title: "操作",
    dataIndex: "operation",
    align: "left",
    key: "operation",
  },
]);

const dataSource: DataItem[] = ref([]) as any;

// 数据总数
const totals = ref(0);
// 当前页码
const current = ref(1);
// 每页显示数量
const size = ref(10);
const receivedMessage = ref<any>(null);

async function handleChildEvent(message: any) {
  console.log(message);
  if (message.length > 0) {
    receivedMessage.value = message[0];
    current.value = 1;
    size.value = 10;
    getQueryPages();
  }
}
const dataLength = ref<any>(false)
async function getQueryPages() {
  const getPages = {
    nodeId: receivedMessage.value,
    current: current.value,
    size: size.value,
    status: 3, // 0 正常  1 已删除 2 待审核 3 已审核 4 已驳回
  };
  if (receivedMessage.value) {
    await guideQueryPage(getPages).then((res: any) => {
      if (res?.code == 0) {
        dataSource.value = res.data.records;
        if (dataSource.value.length < 1) {
          dataLength.value = false
        } else {
          dataLength.value = true
        }
        current.value = res.data.current;
        size.value = res.data.size;
        totals.value = res.data.total;
      } else {
        proxy.$message.error(res?.errorMsg);
      }
    });
  } else {
    await guideQueryPage(getPages).then((res: any) => {
      if (res?.code == 0) {
        dataSource.value = res.data.records;
        if (dataSource.value.length < 1) {
          dataLength.value = false
        } else {
          dataLength.value = true
        }
      } else {
        proxy.$message.error(res?.errorMsg);
      }
    });
  }
}
const onShowSizeChange = (current: number, pageSize: number) => {
  console.log(current, pageSize);
};
// // 监听子组件传递的数据变化
// watch(receivedMessage, (newValue, oldValue) => {
//   if (newValue !== oldValue) {
//     console.log(newValue, 'newValue')

//     // 数据发生了变化，调用处理子组件消息的函数来更新查询条件等
//     handleChildEvent([newValue])
//   }
// })
// 处理分页切换事件
const handlePageChange = (page: number, pageSize: number) => {
  console.log(page, pageSize);
  current.value = page;
  size.value = pageSize;
  console.log(current.value, size.value);
  getQueryPages();
};
onMounted(() => {
  getKnowledgeTreeList(searchValue.value);
});


const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '高原病知识库',
    ip: baseIP,
    type: '高原病知识库'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
onMounted(() => {
  operationLogs()
})
</script>

<style scoped lang="less">
.tagsStyle {
  display: flex;
  justify-content: space-around;
}

.operator {
  display: flex;
  justify-content: flex-start;
}

.operatorButton {
  color: #209e85;
}

canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.single-line {
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  // -o-text-overflow: ellipsis;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  max-width: 100%;
  // min-width: 200px;
}

.wrapper {
  width: 100%;
  height: 100%;
  display: inline-flex;
  flex-direction: column;
  padding: 0 10px;
  background-color: #f8fafc;
}

:deep(.ant-table-thead > tr > th) {
  // padding: 16px !important;
  background-color: var(--table-thead-color);
}
::v-deep .ant-card .ant-card-body {
    padding: 0;
    border-radius: 0 0 2px 2px;
}
::v-deep .ant-card{
  border: none;
}
// .noboard{
//   border-right: 1px solid red;
// }
// .ml-4{
//   border-left: 0.5px solid #e2e8f0;;
// }
::v-deep .ant-table-container{
  border: none;
}
.lineDiv{
  height: 100vh;
  // width: 1px;
  border-right: 0.5px solid #e2e8f0;
}
::v-deep .ant-table-wrapper .ant-table.ant-table-bordered >.ant-table-container {
  border: none;
}
::v-deep .ant-card .ant-card-head {
  border-bottom: 0.5px solid #e2e8f0;
  min-height: 48px;
}
// ::v-deep .ant-table-thead>tr>th{

//   height: 40px !important;
// }
// ::v-deep .ant-table-wrapper .ant-table.ant-table-bordered >.ant-table-container >.ant-table-content >table >thead>tr>th{
//   height: 40px !important;
// }
// ::v-deep .ant-table-wrapper .ant-table.ant-table-middle .ant-table-thead>tr>th{
//   padding: 8px;
// }
.card{
  // padding: 16px 0;
  box-shadow: none;
  margin-top: 8px;
  
}

</style>
