import request from '@/utils/request.ts'

// 字典
export function getDicList(params: any) {
  return request({
    url: '/api/visitDict/listAll',
    method: 'get',
    params
  })
}

// 分页
export function getPageList(params: any) {
  return request({
    url: '/v1/users/sortedList',
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'post',
    data: params
  })
}

export function getRolesList(params: any) {
  return request({
    url: '/v1/roles',
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'get',
    params
  })
}

export function getAddUser(data: any) {
  return request({
    url: `/v1/users`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'post',
    data
  })
}

export function getEditUser(data: any) {
  return request({
    url: `/v1/users/${data.userId}`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'put',
    data
  })
}

export function getRestPassword(userId: string, params: any) {
  return request({
    url: `/v1/users/${userId}/pass-reset`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'put',
    params
  })
}

export function getDisableUser(data: any) {
  return request({
    url: `/v1/users/${data.userId}/disable-user`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'put',
    data
  })
}

export function getEnableUser(data: any) {
  return request({
    url: `/v1/users/${data.userId}/enable-user`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'put',
    data
  })
}


export function getUserDetail(userId: string, params: any) {
  return request({
    url: `/v1/users/${userId}`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'get',
    params
  })
}

export function getUserBindPermission(data: any) {
  return request({
    url: `/v1/roles/bind-users`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'post',
    data
  })
}

export function getUserUnbindPermission(data: any) {
  return request({
    url: `/v1/roles/unbind-users`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'POST',
    data
  })
}

export function getInstitutionsList() {
  return request({
    url: `/institutions/institutionSimpleList`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'get',
  })
}

export function getWithUsername(params: { username: string }) {
  return request({
    url: `/v1/users/withUsername`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'get',
    params
  })
}