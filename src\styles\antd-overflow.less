// antd 公共样式的覆盖放这里
.ant-btn {
  &-link {
    @apply text-primary-color;
  }

  &-link:not(:disabled):hover {
    @apply text-primary-color;
  }

  &-primary {
    box-shadow: none;
  }
}

.ant-btn-link:not(:disabled):hover {
  @apply text-primary-color;
}

.ant-form-item {
  @apply mb-4;
}

.ant-form-inline .ant-form-item {
  margin-inline-end: 8px;
}

.ant-tabs-top > .ant-tabs-nav {
  margin-bottom: 0px !important;
}

.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: var(--select-bg-color);
}

.ant-table-wrapper .ant-table-pagination.ant-pagination {
  @apply my-2;
}

.ant-modal {
  .ant-modal-content {
    @apply px-0 py-3;

    .ant-modal-body,
    .ant-modal-header,
    .ant-modal-footer {
      @apply px-6;
    }

    .ant-modal-body {
      @apply py-3;
    }

    .ant-modal-header,
    .ant-modal-footer {
      @apply border-normal-border-color border-solid pb-3;
    }

    .ant-modal-header {
      @apply border-b;
    }

    .ant-modal-footer {
      @apply my-0 pt-3 pb-0 border-t;
    }
  }
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: var(--select-bg-color);
}

.ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover,
.ant-tree.ant-tree-directory .ant-tree-treenode-selected {
  &::before {
    background-color: var(--select-bg-color);
    @apply rounded-sm;
  }
}

.ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected {
  color: #000;
}

.ant-steps {
  .ant-steps-item-title {
    @apply pr-0;
  }
}

.ant-select-selector,
.ant-input-number,
.ant-picker-range,
.ant-input {
  border-radius: 4px !important;
}
