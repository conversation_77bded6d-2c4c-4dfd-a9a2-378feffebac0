<template>
    <div class="my-charts">
        <div class="my-charts-box" v-if="isData">
            <MixBasicChart :option="options" @up="handleClick" />
        </div>
        <div class="no-data-available" v-else>
            <svg-icon name="noDataAvailable" width="121" height="130"></svg-icon>
            <div>暂无数据</div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import MixBasicChart from '@/components/charts/MixBasicChart.vue'
import { color } from 'echarts'
import { symbolName } from 'typescript'
import { defineProps, ref, watch } from 'vue'
import { PropType } from 'vue'

const options = ref<any>({})

const { data, name } = defineProps({
    data: {
        type: Array,
        required: true,
        default: () => []
    },
    name: {
        type: String,
        default: ''
    }
})

// 渲染折线图函数
const initCharts = (dataList: any[]) => {

    // 提取 X 和 Y 数据
    const months = dataList.map((item) => item.date)
    const values = dataList.map((item) => item.cnt)

    // 配置 ECharts 图表
    options.value = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'line'
            },
            formatter: (params: any) => {
                const point = params[0]
                return `
            <strong>${point.name}</strong><br/>
            数量: ${point.value} 人次
          `
            }
        },
        legend: {
            data: [],
            top: '6%',
            left: '2%'
        },
        grid: {
            left: '3%',
            right: '5%',
            bottom: '4%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: months,
            axisLabel: {
                interval: 0 // 保证所有月份都显示
            }
        },
        yAxis: {
            type: 'value'
            // name: '数量'
        },
        series: [
            {
                name: '',
                type: 'line',
                //   smooth: false,
                //   showSymbol: true, //显示圆点
                symbol: 'circle',
                symbolSize: 8,
                data: values,
                // symbolSize: 8,
                lineStyle: {
                    width: 3,

                    color: '#209E85', // 蓝色实线
                },
                itemStyle: {
                    color: '#209E85',
                    borderColor: '#ffffff',
                    borderType: 'solid',
                    borderWidth: 1
                },
                emphasis: {
                    focus: 'series',
                    itemStyle: {
                        color: '#209E85',
                        borderWidth: 1,
                        borderType: 'solid',
                        borderColor: '#ffffff'
                    }
                },
                label: {
                    show: true,
                    position: 'top'
                }
            },

        ],
        dataZoom: {
            type: 'inside'
        }
    }
}
const isData = ref(true)
watch(
    () => data,
    (newVal) => {
        console.log(newVal, 'newVal')
        // 数量均为0时 不显示图表
        // isData.value = newVal?.some((item: any) => item.value > 0)
        // console.log(isData.value, '000')
        isData.value = newVal?.some((item: any) => item)
        initCharts(newVal)
    },
    { deep: true, immediate: true }
)

const handleClick = (e: any) => {
    console.log(e, '点击的数据')
}
</script>

<style lang="less" scoped>
.my-charts {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .my-charts-box {
        width: 100%;
        height: 100%;
    }

    .no-data-available {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        color: #5e6580;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
    }
}
</style>