<template>
  <div class="basic-wrapper">
    <div class="main">
      <a-form :model="formState" name="time_related_controls" class="forms">
        <a-form-item name="range-picker" label="统计时间截至">
          {{ getMomentTime }}
        </a-form-item>
      </a-form>
    </div>
    <a-card :bordered="false" :bodyStyle="{ padding: '0' }" class="table-screen clearfix" title="数据概览">
      <div style="  width: 100%; height: 100%;position: relative;">
        <a-row :gutter="24" class="pie-row" style="background-color: #fff;">
          <a-col :span="12" style="padding-left: 12px;">
            <div class="card typeCategory" style="border: none;">
              <p>平台用户量</p>
              <a-tooltip title="当前业务系统在所选时间范围内的每日新增用户数和总用户数">
                <InfoCircleOutlined style="color: #8f94a7" class="iconDesc" />
              </a-tooltip>
              <div class="my-charts">
                <span class="userSearchCounts">总数：{{ userCount }}</span>
                <div class="my-charts-box" v-if="isxAxisDataData">
                  <MixBasicChart :option="chartOpt" class="chart" />
                </div>
                <div class="no-data-available" v-else>
                  <svg-icon name="noDataAvailable" width="121" height="130"></svg-icon>
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
            
          </a-col>
          <a-divider type="vertical" style="height: 100%; background-color: #F8FAFC;position: absolute;top: 0;right: 50%;" />
          <a-col :span="12" style="padding-left: 12px;border: none;">
            <div class="card typeCategory" style="border:none">
              <registerUser :param="paramObjs" />
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>
    <!-- 筛选 -->
    <div class="filter-wrapper clearfix" style="margin-top: 16px;background-color: #f8fafc;">
      <!-- form -->
      <a-card :bordered="false" :bodyStyle="{ padding: '16px 24px', }" class="table-screen clearfix" title="平台用户分析">
        <a-form layout="inline" :model="formData" class="form-data-inline clearfix">
          <a-form-item label="战区">

            <a-select class="select-styles" allowClear :show-search="false" :default-active-first-option="false"
              show-arrow style="width: 380px" v-model:value="formData.area" :filter-option="false" mode="tags"
              :not-found-content="null" placeholder="请选择" @change="handleAreaChange">
              <a-select-option :value="item" v-for="item in selectedData.areaList" :key="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="医院">
            <!-- @search="getHospitalDataList" -->
            <a-select class="select-styles" allowClear :show-search="false" :default-active-first-option="false"
              mode="tags" show-arrow style="width: 380px" v-model:value="formData.hospital" :filter-option="false"
              :not-found-content="null" placeholder="请选择" @change="handleHospitalChange" @focus="handleFocusHospital">
              <!-- @focus="handleFocusHospital" -->
              <a-select-option :value="item" v-for="item in selectedData.hospitalList" :key="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="科室">
            <!-- @search="getDepartmentDataList" -->
            <a-select class="select-styles" allowClear :show-search="false" :default-active-first-option="false"
              mode="tags" show-arrow style="width: 380px" v-model:value="formData.department" :filter-option="false"
              :not-found-content="null" placeholder="请选择" @change="handleDepartmentChange"
              @focus="handleFocusDepartment">
              <a-select-option :value="item" v-for="item in selectedData.departmentList" :key="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间范围">
            <a-range-picker allowClear v-model:value="formData.dates" 
              :presets="presetTimeRange()" value-format="YYYY-MM-DD" style="width: 380px" />
          </a-form-item>
          <!-- :disabled-date="disabledStartDate" -->
          <div class="btn-export">
            <a-button @click="handleClear" class="letter-text" style="margin-right: 8px">清空</a-button>
            <a-button type="primary" class="letter-text" style="background-color: #209e85"
              @click="handleSearch(formData)">筛选</a-button>
          </div>
        </a-form>
      </a-card>
    </div>

    <div style="background-color: #f8fafc; margin: 16px 0; width: 100%; height: 100%">

      <a-row :gutter="24" class="pie-row">
        <a-col :span="12" style="padding-left: 12px">
          <div class="card">
            <p>用户活跃度</p>
            <a-tooltip title="当前业务系统在所选时间范围内的用户访问量">
              <InfoCircleOutlined style="color: #8f94a7" class="iconDesc" />
            </a-tooltip>
            <div class="my-charts">
              <div class="my-charts-box" v-if="isShowUsersAactivity">

                <basicLineCharts :data="usersAactivityTypes" class="chart"></basicLineCharts>
              </div>
              <div class="no-data-available" v-else>
                <svg-icon name="noDataAvailable" width="121" height="130"></svg-icon>
                <div>暂无数据</div>
              </div>
            </div>
          </div>
        </a-col>

        <a-col :span="12" style="padding-left: 12px">

          <div class="card">
            <activeUser :params="paramObj" />
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24" class="pie-row">
        <a-col :span="24" style="padding-left: 12px">
          <div class="card">
            <p>操作类型占比</p>
            <a-tooltip title="当前业务系统在所选时间范围内的所有操作类型占比">
              <InfoCircleOutlined style="color: #8f94a7" class="iconDesc" />
            </a-tooltip>
            <div class="my-charts">
              <div class="my-charts-box" v-if="isShowOperationTypes">
                <pieShowTextCharts :data="operationTypes" class="chart" :name="operationTypesName"
                  :count="operationTypesCount" />
              </div>
              <div class="no-data-available" v-else>
                <svg-icon name="noDataAvailable" width="121" height="130"></svg-icon>
                <div>暂无数据</div>
              </div>
            </div>
          </div>
        </a-col>


      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, toRaw, getCurrentInstance, onBeforeMount } from 'vue'
import dayjs from 'dayjs'
import _ from 'lodash'
import { presetTimeRange } from '@/utils/utils'
import { message } from 'ant-design-vue'
import MixBasicChart from '@/components/charts/MixBasicChart.vue'
import pieShowTextCharts from '@/components/ECharts/pieShowTextCharts.vue'
import basicLineCharts from '@/components/ECharts/basicLineCharts.vue'
import { AppstoreOutlined, MenuOutlined, CloseOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { usersPlatform, operationTypesData, getHospitalData, getDepartmentData, usersAactivity, userSearchCount, getAreaData } from './index.api.ts'
import registerUser from './child/registerUser.vue'
import activeUser from './child/activeUser.vue'

const loading = ref<any>(false)

const isShowData = ref<any>(false)


const getMomentTime = ref<any>();
const getBeforTime = ref<any>();
onBeforeMount(() => {
  getMomentTime.value = dayjs().subtract(1, 'day').format('YYYY年MM月DD日')


  // 获取当前日期
  var curDate = new Date();
  // 计算前一天的时间戳
  var preDate = new Date(curDate.getTime() - 24 * 60 * 60 * 1000);
  // 格式化前一天的日期
  var year = preDate.getFullYear();
  var month = preDate.getMonth() + 1; // 月份从0开始，需要加1
  var day = preDate.getDate();

  // 格式化为 YYYY-MM-DD
  getBeforTime.value = year + '-' + (month > 9 ? month : '0' + month) + '-' + (day > 9 ? day : '0' + day);

  console.log(getBeforTime.value, 99999); // 输出前一天的日期
})


// 时间配置
const disabledStartDate = (current: dayjs.Dayjs) => current.isBefore(dayjs('2022-01-01'))

// 搜索数据
const formData = reactive({
  area: [],
  hospital: [],
  department: [] as string[],
  dates: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  dimension: '医院',
  value: ''
})


// interface SelectedData {
//   // areaList: string[]
//   hospitalList: string[]
//   departmentList: string[]

// }
const selectedData = reactive<any>({
  areaList: [], // 战区
  hospitalList: [], // 医院
  departmentList: [], // 科室

})
// 获取战区数据
const getAreaDataList = _.debounce(async function (name: string = '') {
  try {
    const params = {}
    const res = await getAreaData(params)
    if (res.code === 0) {
      selectedData.areaList = ['全部战区', ...res?.data]
    }
  } catch (err) {
    console.log(err, 'err')
  }
}, 300)
// 战区
// function handleAreaChange(value: any) {
//   formData.area = value
//   // formData.groupField = 'hospital'
//   formData.hospital = []
//   formData.department = []

//   selectedData.hospitalList = []
//   selectedData.departmentList = []

//   getHospitalDataList()
// }
function handleAreaChange(value: any) {
  const allOption = '全部战区'
  
  if (value.includes(allOption)) {
    // 如果选了"全部战区"，只保留"全部战区"
    formData.area = [allOption]
  } else {
    // 如果没选"全部"，但选了所有其他项，自动转成"全部"
    if (value.length === selectedData.areaList.length - 1) {
      formData.area = [allOption]
    } else {
      formData.area = value
    }
  }

  // 清空医院、科室以及对应数据
  formData.hospital = []
  formData.department = []
  selectedData.hospitalList = []
  selectedData.departmentList = []

  getHospitalDataList()
}
// 医院 判断是否选择了战区和医院，并更新 formData 和 selectedData
// function handleHospitalChange(value: []) {
//   // 判断是否选择了战区
//   if (formData.area.length === 0) {
//     formData.hospital = []
//     return message.error('请先选择战区')
//   }

//   // 判断是否选择了医院
//   if (value.length === 0) {
//     formData.hospital = []
//     return message.error('请选择医院')
//   }

//   // 更新 formData 和 selectedData
//   formData.department = []
//   // formData.groupField = value.length > 0 ? 'department' : 'hospital'
//   formData.hospital = value
//   selectedData.departmentList = []

//   getDepartmentDataList()
// }
function handleHospitalChange(value: any) {
  const allOption = '全部医院'
  if (formData.area.length === 0) {
    formData.hospital = []
    return message.error('请先选择战区')
  }

  if (value.includes(allOption)) {
    formData.hospital = [allOption]
  } else {
    if (value.length === selectedData.hospitalList.length - 1) {
      formData.hospital = [allOption]
    } else {
      formData.hospital = value
    }
  }

  formData.department = []
  selectedData.departmentList = []
  getDepartmentDataList()
}
function handleFocusHospital() {
  if (formData.area.length === 0) {
    return message.error('请先选择战区')
  }
}

// 科室 判断是否选择了战区、医院和科室，并根据情况更新 formData
// function handleDepartmentChange(value: string[]) {
//   if (formData.area.length === 0) {
//     formData.department = []
//     return message.error('请先选择战区')
//   }

//   if (formData.hospital.length === 0) {
//     formData.department = []
//     return message.error('请先选择医院')
//   }

//   if (value.length === 0) {
//     formData.department = []
//     return message.error('请选择科室')
//   }

//   // 更新 formData 的 groupField 和 department
//   // formData.groupField = value.length > 0 ? 'doctor' : 'department'
//   formData.department = value
// }
function handleDepartmentChange(value: string[]) {
  const allOption = '全部科室'
  if (formData.area.length === 0) {
    formData.department = []
    return message.error('请先选择战区')
  }
  if (formData.hospital.length === 0) {
    formData.department = []
    return message.error('请先选择医院')
  }

  if (value.includes(allOption)) {
    formData.department = [allOption]
  } else {
    if (value.length === selectedData.departmentList.length - 1) {
      formData.department = [allOption]
    } else {
      formData.department = value
    }
  }
}
function handleFocusDepartment() {
  if (formData.hospital.length === 0) {
    return message.error('请先选择医院')
  }
}

// 清空
function handleClear() {
  Object.assign(formData, {
    area: [],
    hospital: [],
    department: [],
    // hospitalType: [],
    // groupField: 'hospital',
    // order: '',
    // orderField: '',
    dates: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  })
  selectedData.hospitalList = []
  selectedData.departmentList = []
  // getList()
  // drawChart(formData);
  getOperationTypesData(formData);
  usersAactivityData(formData);
  getAreaDataList()
  // getHospitalDataList()


  const obj = {
    areas: formData.area,
    dates: formData.dates,
    departments: formData.department.includes('全部科室') ? [] : formData.department,
    hospitals: formData.hospital.includes('全部医院') ? [] : formData.hospital,
    value: '',
    dimension: '医院',
  };
  paramObj.value = obj; // 直接更新 paramObj
}
const paramObj = ref<any>()
const paramObjs=ref<any>({
  value: '',
  dimension: '医院',
})
// 查找
function handleSearch(value: any) {
  console.log(value);
  // let allHospital: any = []
  // formData.hospital = formData.hospital.includes('全部医院') ? allHospital : formData.hospital;
  // formData.department = formData.department.includes('全部科室') ? allHospital : formData.department

  // drawChart(formData);
  getOperationTypesData(formData);
  usersAactivityData(formData);
  //  传递修改后的参数
  const obj = {
    areas: formData.area.includes('全部战区') ? [] : formData.area,
    dates: formData.dates,
    departments: formData.department.includes('全部科室') ? [] : formData.department,
    hospitals: formData.hospital.includes('全部医院') ? [] : formData.hospital,
    value: '',
    dimension: '医院',
  };
  paramObj.value = obj; // 直接更新 paramObj
}

const getHospitalDataList = _.debounce(async function () {
  if (formData.area.length === 0) {
    return message.error('请先选择战区')
  }
  try {

    let params = {
      areas: formData.area.includes('全部战区') ? [] : formData.area
    }

    const res = await getHospitalData(params)
    if (res.code === 0) {
      selectedData.hospitalList = ['全部医院', ...res?.data]
    }
  } catch (err) {
    console.log(err, 'err')
  }
}, 300)

const getDepartmentDataList = _.debounce(async function () {
  if (formData.area.length === 0) {
    return message.error('请先选择战区')
  }
  if (formData.hospital.length === 0) {
    return message.error('请先选择医院')
  }
  try {
    let params = {
      areas: formData.area.includes('全部战区') ? [] : formData.area,
      hospitals: formData.hospital.includes('全部医院') ? [] : formData.hospital
    }
    console.log(params, 'params');
    const res = await getDepartmentData(params)
    if (res.code === 0) {
      selectedData.departmentList = ['全部科室', ...res?.data]
    }
  } catch (err) {
    console.log(err, 'err')
  }
}, 300)


const { proxy }: any = getCurrentInstance()
const userCount = ref<any>()
// 平台用户
const diseaseOptions = ref<any>([])
const xAxisData = ref([])
const yAxisData = ref([])
const isxAxisDataData = ref<any>(false)
const getinfectiousDiseaseSort = async (value: any) => {
  diseaseOptions.value = []

  // let obj = JSON.parse(JSON.stringify(value))

  // obj.area = obj.area.includes('全部战区') ? [] : obj.area;
  // obj.hospital = obj.hospital.includes('全部医院') ? [] : obj.hospital;
  // obj.department = obj.department.includes('全部科室') ? [] : obj.department
  const objs = {
    // from: obj.dates[0],
    // to: obj.dates[1],
    // departments: obj.department,
    // hospitals: obj.hospital,
    // area: value.area
  }
  xAxisData.value = []
  yAxisData.value = []
  await usersPlatform(objs).then((res: any) => {
    if (res.code == 0) {
      diseaseOptions.value = []
      userCount.value = res.data.total
      res.data.platformUserAnalysisDates.map((item: { cnt: any; date: any }) => {
        xAxisData.value.push(item.cnt)
        yAxisData.value.push(item.date)
      })

      if (xAxisData.value.length > 0 && yAxisData.value.length > 0) {
        isxAxisDataData.value = true
      } else {
        isxAxisDataData.value = false
      }
    } else {
      proxy.$message.error(res?.errorMsg)
    }
  })
}
// 平台用户量
const chartOpt = ref<any>({})
const drawChart = (rangDate: any) => {
  getinfectiousDiseaseSort(rangDate)

  console.log(yAxisData, 'yAxisData')

  chartOpt.value = {
    title: {
      textStyle: {
        //文字颜色
        color: '#07123C',
        fontWeight: 'bold',
        //字体系列
        fontFamily: 'sans-serif',
        //字体大小
        fontSize: 18
      }
    },
    color: ['#13A89B', '#2316AB', '#405BC8', '#CCF56A', '#14705E', '#00CFBE', '#098BEA', '#079A35', '#9DD962'],
    barWidth: 20,

    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: yAxisData,
      axisTick: {
        show: false
      },
      axisLabel: {
        // 轴文字
        show: true,
        color: '#A6AAB2',
        fontSize: 12,
        interval: 0,
        rotate: 20
      },
      axisLine: {
        lineStyle: {
          type: 'solid',
          color: '#E6E6E8',
          width: '1'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        // 轴文字
        show: true,
        color: '#A6AAB2',
        fontSize: 12
      }
      // max:99999//给y轴设置最大值
    },
    series: [
      {
        type: 'bar',
        data: xAxisData,
        // barGap:'80%',/*多个并排柱子设置柱子之间的间距*/
        // barCategoryGap:'50%',/*多个并排柱子设置柱子之间的间距*/
        itemStyle: {
          //柱状图上方显示数值
          normal: {
            color: '#405BC8',
            label: {
              show: true, //开启显示
              position: 'top', //在上方显示
              textStyle: {
                //数值样式
                color: '#A7ABB3',
                fontSize: 12
              }
            }
          }
        },

        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }
    ],
    dataZoom: [
      {
        type: 'slider', //给x轴设置滚动条
        show: false, //flase直接隐藏图形
        xAxisIndex: [0],
        bottom: 0,
        height: 10,
        showDetail: false,
        startValue: 0, //滚动条的起始位置
        endValue: 9 //滚动条的截止位置（按比例分割你的柱状图x轴长度）
      },
      {
        type: 'inside', //设置鼠标滚轮缩放
        show: false,
        xAxisIndex: [0],
        startValue: 0,
        endValue: 9
      }
    ]
  }
}

// 注册用户分布
const registeredUsersData = ref<any>()
const registeredUsers = ref<any>()
const registeredUsersCount = ref<any>()

const getRegisteredUsersData = async (value: any[]) => {
  if (!selectedDiseaseJob.value) {
    return
  }
  const obj = {
    from: value[0],
    to: value[1],
    infectiousName: selectedDiseaseJob.value,
    patientCharacteristics: '人员类别'
  }
  const res = await patientCharacter(obj)
  if (res.code == 0) {
    getArr.value = res.data
    getLastTypeOneJob.value = []
    res.data.information.map((item: any) => {
      getLastTypeOneJob.value.push({
        name: item.key,
        value: item.cnt
      })
    })
  } else {
    proxy.$message.error(res?.errorMsg)
  }
}

// 操作类型占比
const operationTypes = ref<any>([])
const operationTypesName = ref<any>()
const operationTypesCount = ref<any>()

const isShowOperationTypes = ref<any>(true)

const getOperationTypesData = async (value: any) => {
  let obj = JSON.parse(JSON.stringify(value))

  obj.area = obj.area.includes('全部战区') ? [] : obj.area;
  obj.hospital = obj.hospital.includes('全部医院') ? [] : obj.hospital;
  obj.department = obj.department.includes('全部科室') ? [] : obj.department
  const objs = {
    from: obj.dates[0],
    to: obj.dates[1],
    departments: obj.department,
    hospitals: obj.hospital,
    // appCode:'INF',
    areas: value.area
  }

  const res = await operationTypesData(objs)
  if (res.code == 0) {
    operationTypes.value = []
    if (res.data.total > 0) {
      isShowOperationTypes.value = true;
      operationTypesName.value = '操作总数'
      operationTypesCount.value = res.data.total

      res.data.platformUserAnalysisNames.map((item: any) => {
        operationTypes.value.push({
          name: item.key,
          value: item.cnt
        })
      })
    } else {
      isShowOperationTypes.value = false
    }
  } else {
    proxy.$message.error(res?.errorMsg)
  }
}

// 用户活跃度
interface DataItem {
  date: string;
  cnt: number;
}
const usersAactivityTypes = ref<DataItem[]>([])
const isShowUsersAactivity = ref<any>(true)

const usersAactivityData = async (value: any) => {
  let obj = JSON.parse(JSON.stringify(value))

  obj.area = obj.area.includes('全部战区') ? [] : obj.area;
  obj.hospital = obj.hospital.includes('全部医院') ? [] : obj.hospital;
  obj.department = obj.department.includes('全部科室') ? [] : obj.department
  const objs = {
    from: obj.dates[0],
    to: obj.dates[1],
    departments: obj.department,
    hospitals: obj.hospital,
    // appCode:'INF',
    areas: value.area
  }
  const res = await usersAactivity(objs)
  if (res.code == 0) {
    usersAactivityTypes.value = res.data;



    if (usersAactivityTypes.value.length > 0) {
      isShowUsersAactivity.value = true
    } else {
      isShowUsersAactivity.value = false
    }
  } else {
    proxy.$message.error(res?.errorMsg)
  }
}

// 生命周期钩子
onMounted(() => {
  formData.dates = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  console.log(formData, 99000);

  paramObj.value = formData
  drawChart(formData)
  getOperationTypesData(formData)
  usersAactivityData(formData);
  getAreaDataList()
  // getHospitalDataList()
})
</script>

<style lang="less" scoped>
.basic-wrapper {
  width: 100%;
  min-height: 100vh;
  position: relative;
  background-color: #f8fafc;
}

.main {
  position: fixed;
  right: 25px;
  top: 58px;
  height: 30px;
  z-index: 999;
  display: flex;
  justify-content: flex-end;
}


.filter-wrapper {
  width: 100%;
  position: relative;

  .active-type {
    position: fixed;
    right: 26px;
    top: 58px;
    z-index: 2;

    display: flex;
    justify-content: flex-end;

    .ant-radio-button-wrapper {
      width: 38px;
      height: 30px;
      align-content: center;
      text-align: center;
    }
  }

  .table-screen {
    width: 100%;
    background-color: #ffffff;
    border-radius: 12px;

  }

  .form-data-inline {
    width: 100%;
    display: inline-flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .ant-form-item {
      margin-bottom: 16px;
      margin-right: 24px;
    }

    .ant-form-item:nth-last-child(2) {
      margin-right: 0 !important;
      margin-bottom: 0 !important;
    }
  }

  .btn-export {
    width: 70px;
    height: 34px;
    display: flex;
    justify-content: flex-end;
    flex-direction: row;
    float: right;
    flex: 1;
  }
}

.drawer-content {
  width: 100%;
  height: 100%;
  padding: 0 24px;
  box-sizing: border-box;
  background: #f8fafc;

  .breadcrumb-container {
    width: 100%;
    align-content: center;
    height: 48px;
  }

  .chart-container {
    width: 100%;
    height: 380px;
    margin-bottom: 24px;
    border-radius: 12px;
    border: 0.5px solid #e2e8f0;
    background: #fff;
    padding: 16px 16px 0;
    box-sizing: border-box;
  }

  .table-container {
    width: 100%;
    height: 368px;
    padding: 24px;
    box-sizing: border-box;
    margin-bottom: 24px;
    border-radius: 12px;
    border: 0.5px solid #e2e8f0;
    background: #fff;

    overflow-y: auto;
  }
}

.pie-row {
  margin: 16px 0;
  background-color: #f8fafc;
  height: 400px;
}

.chart {
  height: 320px !important;
  width: 100%;
}

.typeCategory {
  border-radius: 8px;
  padding: 16px 24px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  // margin-bottom: 16px;
  width: 100%;
  height: 100%;
  position: relative;
  // border: 0.5px solid #e2e8f0;
  box-shadow: none;
}

.lines {
  position: relative;
}

.lines:after {
  content: "";
  display: block;
  width: 1px;
  height: 100%;
  background-color: #000;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.my-charts {
  width: 100%;
  // height: calc(100% - 40px);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .my-charts-box {
    width: 100%;
    height: 100%;
  }

  .no-data-available {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    color: #5e6580;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }

  .userSearchCounts {
    position: absolute;
    top: 2%;
    left: 0;
    margin-left: 50px;
    font-size: 16px;
    color: #07123C;
    font-weight: 600;
  }
}

p {
  font-size: 16px;
  font-weight: bold;
  color: #07123c;
  font-family: 'Noto Sans SC';
}

.card {
  border-radius: 8px;
  padding: 16px 24px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  // margin-bottom: 16px;
  height: 100%;
  position: relative;
  border: 0.5px solid #e2e8f0;
  box-shadow: none;

  p {
    display: inline-block;

  }
}



.iconDesc {
  // position: absolute;
  // top: 20px;
  // left: 300px;
  margin-left: 10px;
}

.selectType {
  position: absolute;
  top: 10px;
  right: 5%;
}

.topLeft {
  display: flex;
  flex-wrap: wrap;
  /* justify-content: space-between */
}

.selectTypeYear {
  border: none;
  color: #00b929;

  ::v-deep .ant-select-selector {
    border: none;
    color: #00b929;
  }
}

.topLeftTitle {
  margin-bottom: 8px;
  font-size: 16px;
  color: #07123c;
  font-weight: bold;
  font-family: 'Noto Sans SC';
  width: 100%;
}

.formsTopLeft {
  width: 100%;
}

.diseaseTypeSelectTopLeft {
  width: 50%;
}

.charts-top-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .charts-title {
    color: #07123c;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    margin-left: 22px;

    .title-tip {
      margin-left: 8px;
    }
  }

  .charts-btn-detail {
    display: inline-flex;
    align-items: center;
    justify-self: center;
    border-radius: 4px !important;
    border: 1px solid #e2e8f0;
    background: #fff;
    margin-right: 20px;

    .anticon-search {
      margin-bottom: 0;
    }
  }
}

.item-charts-list {
  width: 100%;
  height: calc(100% - 20px);
}

.chart-container-custom {
  height: 100%;
  margin-top: -25px;
}
</style>
