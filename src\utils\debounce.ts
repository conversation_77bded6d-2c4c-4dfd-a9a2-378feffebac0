export function debounce<T extends (...args: any[]) => any>(func: T, wait: number, immediate = false): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function (...args: Parameters<T>) {
    const context = this

    if (immediate && !timeout) {
      func.apply(context, args)
    }

    clearTimeout(timeout)

    timeout = setTimeout(() => {
      timeout = null
      if (!immediate) {
        func.apply(context, args)
      }
    }, wait)
  }
}
