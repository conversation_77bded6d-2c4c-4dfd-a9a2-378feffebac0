import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

// export const colors: string[] = ['#2316AB', '#0074D9', '#14705E', '#079A35', '#7474FF']
export const colors: string[] = ['#030092', '#009477', '#0096AE', '#298DFF', '#7474FF']

export function formatData(data, index, color = '#03ceda') {
  //index标识第几层
  return data.map((item) => {
    item.draggable = true
    item.value = item.id
    // item.collapsed = true

    // 设置节点大小
    if (index === 0 || index === 1) {
      item.label = {
        position: 'inside'
        //   rotate: 0,
        //   borderRadius: "50%",
      }
    }
    // 设置label大小
    switch (index) {
      case 0:
        item.symbolSize = 280
        // item.symbol = 'image:///src/assets/png/one.png'
        item.symbol = 'image://' + new URL(`/src/assets/png/one.png`, import.meta.url).href
        break
      case 1:
        item.symbolSize = 240
        // item.symbol = 'image:///src/assets/png/two.png'
        item.symbol = 'image://' + new URL(`/src/assets/png/two.png`, import.meta.url).href
        item.label = {
          // textBorderColor: '#fff', // 描边颜色
          // textBorderWidth: 2 //描边宽度
        }
        break
      case 2:
        item.symbolSize = 70
        // item.symbol = 'image:///src/assets/png/three.png'
        item.symbol = 'image://' + new URL(`/src/assets/png/three.png`, import.meta.url).href
        item.label = {
          color: '#ffffff',
          textBorderColor: colors[index], // 描边颜色
          textBorderWidth: 2 //描边宽度
        }
        break
      case 3:
        // item.symbol = 'image:///src/assets/png/four.png'
        item.symbol = 'image://' + new URL(`/src/assets/png/four.png`, import.meta.url).href
        item.symbolSize = 50
        item.label = {
          color: '#ffffff',
          textBorderColor: colors[index], // 描边颜色
          textBorderWidth: 2 //描边宽度
        }
        break
      default:
        item.symbolSize = 30
        break
    }

    // item.emphasis = {
    //   focus: 'descendant', // 与该点有关联的层级都高亮
    // }

    // 设置线条颜色
    item.lineStyle = {
      width: 2,
      color: colors[index],
      // color: {
      //   type: 'linear',
      //   x: 0,
      //   y: 0,
      //   x2: 1,
      //   y2: 1,
      //   colorStops: [
      //     {
      //       offset: 0,
      //       color: 'rgba(0, 255, 244, 0.2)' // 0% 处的颜色
      //       // color: color // 0% 处的颜色
      //     },
      //     {
      //       offset: 1,
      //       color: 'rgba(0, 255, 244, 1)' // 100% 处的颜色
      //     }
      //   ],
      //   global: false
      // },
      curveness: 0.2 //弧度
    }

    if (item.children?.length > 0) {
      //存在子节点
      //计算出颜色
      // color = colors[Math.floor(Math.random() * 9) <= 0 ? 0 : Math.floor(Math.random() * 9)]
      color = colors[index]
      // item.itemStyle = {
      //   borderColor: color,
      //   color: color
      // }
      // item.itemStyle = {
      //   color: colors[index],
      //   borderColor: color
      // }
      item.children = formatData(item.children, index + 1, color)
    } else {
      //不存在
      item.itemStyle = {
        // color: 'transparent',
        color: '#ffffff',
        borderColor: '#7474FF',
        borderWidth: 3
      }
      item.label = {
        show: true,
        // color: '#7474FF'
        color: '#fff',
        textBorderColor: colors[index], // 描边颜色
        textBorderWidth: 2 //描边宽度
      }
    }
    return item
  })
}

/**
 * data link 的数据
 * node 树结构的数据
 */
type NodeIdentifier = string | number
interface LinkData {
  source: NodeIdentifier
  target: NodeIdentifier
  [key: string]: any // 允许其他可配置的未知属性
}

export function formatLinkData(data: LinkData[], node: any) {
  // id 到 name 的映射
  const idToNameMap = {}

  node.forEach((i) => nodeMap(i, idToNameMap))
  function nodeMap(nodeChild, idMap) {
    idMap[nodeChild.id] = nodeChild.name
    if (nodeChild.children && nodeChild.children.length > 0) {
      nodeChild.children.forEach((k) => nodeMap(k, idMap))
    }
  }

  // 转换 data 数组
  const transformedLinks: LinkData[] = data.map((link) => ({
    source: idToNameMap[link.source],
    target: idToNameMap[link.target]
  }))

  return transformedLinks
}

// 下载pdf
export async function convertToPDF(document: Document) {
  const doc = new jsPDF()
  const canvas = await html2canvas(document.body)
  const imgData = canvas.toDataURL('image/png')
  const imgProps = doc.getImageProperties(imgData)
  const pdfWidth = doc.internal.pageSize.getWidth()
  const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width
  doc.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight)

  // 实现模糊搜索功能
  const searchInput = document.createElement('input')
  searchInput.type = 'text'
  searchInput.placeholder = '输入搜索内容'
  document.body.appendChild(searchInput) // 将input添加到新打开页面的body中

  searchInput.addEventListener('input', () => {
    const searchText = searchInput.value.toLowerCase()
    const pageElements = doc.internal.pages.map((page) => page.canvas).flat()
    pageElements.forEach((page: HTMLCanvasElement) => {
      const pageContent = page.getContext('2d')!.getTextContent()!.toLowerCase()
      const isMatch = pageContent.includes(searchText)
      page.style.display = isMatch ? 'block' : 'hidden'
    })
  })

  // 实现分页功能
  const totalPages = doc.internal.getNumberOfPages()
  const paginationDiv = document.createElement('div')
  paginationDiv.style.textAlign = 'center'
  for (let i = 1; i <= totalPages; i++) {
    const pageButton = document.createElement('button')
    pageButton.textContent = `${i}`
    pageButton.addEventListener('click', () => {
      doc.setPage(i)
      const pageCanvas = doc.internal.pages[i - 1].canvas
      pageCanvas.style.display = 'block'
      for (let j = 1; j <= totalPages; j++) {
        if (j !== i) {
          doc.internal.pages[j - 1].canvas.style.display = 'hidden'
        }
      }
    })
    paginationDiv.appendChild(pageButton)
  }
  document.body.appendChild(paginationDiv)

  // 实现缩放功能
  const zoomInButton = document.createElement('button')
  zoomInButton.textContent = '放大'
  zoomInButton.addEventListener('click', () => {
    const currentScale = doc.internal.scale
    doc.internal.scale = currentScale * 1.2
    doc.output('pdf')
  })
  const zoomOutButton = document.createElement('button')
  zoomOutButton.textContent = '缩小'
  zoomOutButton.addEventListener('click', () => {
    const currentScale = doc.internal.scale
    doc.internal.scale = currentScale / 1.2
    doc.output('pdf')
  })
  document.body.appendChild(zoomInButton)
  document.body.appendChild(zoomOutButton)

  doc.save('output.pdf')
}
