// @src/store/user.ts
import { defineStore } from 'pinia'
import { logout } from '@/api/login'
import { getAuthorizations, getPermissions, getUsers } from '@/api/user'
import { computed, ref, type Ref } from 'vue'
import _ from 'lodash'
import { removeCookie, setCookie } from '@/utils/cookie'
import { operationLog } from '@/api/log'

interface TokenInfo {
  token: string
  tokenExpired?: string | number
  [key: string]: any
}

interface AuthorizationsParams {
  appCode: 'ALT'
  userId: number | string
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 登录token
    const token: Ref<string> = ref('')
    const tokenExpired: Ref<string | number> = ref('')
    // 目前是固定的账号密码
    const VALID_USERNAME = 'admin'
    const VALID_PASSWORD = '123456'
    // 用户信息
    const userInfo = ref({})
    // 权限数据
    const permList = ref([])

    // getters
    const getUserInfo = computed(() => userInfo.value)
    const getPermList = computed(() => permList.value)

    function SAVE_TOKEN(data: TokenInfo) {
      token.value = data.token
      // tokenExpired.value = data.tokenExpired
    }

    function SET_INFO(user: any) {
      userInfo.value = user
    }

    // 设置权限数据
    function setPermList(authority: any) {
      permList.value = authority
      console.log('setPermList', permList.value)
    }

    async function remove(category: string = '登入登出') {
      await operationLogs(category)
      await logout()

      // localStorage.clear()
      localStorage.removeItem('token')
      localStorage.removeItem('expiresAt')
      localStorage.removeItem('userInfo')
      sessionStorage.clear()
      setCookie('X-YITU-USER-TOKEN', '')
      removeCookie('X-YITU-USER-TOKEN')
      SET_INFO({})
      SAVE_TOKEN({ token: '' })
      location.reload()
    }

    // 操作日志
    interface OperationLogParams {
      type: string
      ip: string | number
    }
    const baseIP = import.meta.env.BASE_IP
    const operationLogs = async (category: string = '登入登出', options: OperationLogParams = { type: '平台管理', ip: baseIP }) => {
      let params = {
        category,
        ...options
      }
      await operationLog(params)
        .then((res) => {
          if (res?.code == 0) {
            console.log(`${options.type}-->${options.category}日志记录成功:`, res)
          }
        })
        .catch((err) => {
          console.log(err, 'err')
        })
    }

    async function getAuthorizationsList() {
      if (!userInfo.value?.id && !userInfo.value?.userId) {
        return
      }
      try {
        let params: AuthorizationsParams = {
          appCode: 'ALT',
          userId: userInfo.value?.id || userInfo.value?.userId || ''
        }
        let res = await getAuthorizations(params).catch((err: any) => console.log(err, '接口请求失败-->err'))
        if (res?.code == 200) {
          // 1. 合并所有的 permissionDTOS
          const allPermissions = res.data?.rolePermissionDTOS?.flatMap((role) => role.permissionDTOS)

          // 2. 使用 lodash 的 _.uniqBy 来去重，基于权限的 code 字段
          const uniquePermissions = _.uniqBy(allPermissions, 'code')

          // 提取用户所有的权限的 `code`
          const permissionCodes = uniquePermissions?.map((permission) => permission.code)

          setPermList(permissionCodes)
        }
      } catch (err) {
        console.log('正常-->err', err)
      }
    }

    function getPermissionsList() {
      let params = {
        appCode: 'ALT'
      }
      getPermissions(params)
        .then((res: any) => {
          console.log('getPermissionsList', res)
          if (res.code == 200) {
            setPermList(res.data)
          }
        })
        .catch((err: any) => {
          console.log(err)
        })
    }

    function getUserData() {
      let userId = userInfo.value?.userId
      getUsers(userId)
        .then((res: any) => {
          console.log('getUserData', res)
        })
        .catch((err: any) => {
          console.log('err', err)
        })
    }

    return {
      // state
      token,
      tokenExpired,
      userInfo,
      permList,
      // methods
      remove,
      SET_INFO,
      SAVE_TOKEN,
      setPermList,
      //  getters
      getUserInfo,
      getPermList,
      VALID_USERNAME,
      VALID_PASSWORD,
      // actions
      getPermissionsList,
      getAuthorizationsList,
      getUserData,
      // 埋点日志
      operationLogs
    }
  },
  {
    persist: {
      key: 'user',
      pick: ['userInfo', 'token']
    }
  }
)
