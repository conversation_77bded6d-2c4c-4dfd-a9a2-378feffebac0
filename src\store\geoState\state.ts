import { defineStore } from 'pinia'

export const useSharedStore = defineStore('sharedStore', {
  state: () => ({
    leftForArea:'',
    storeInfectiousType: '',
    leftForCityProvince: '',
    leftForCityDimension: '',
    leftForUniName: '',
    leftForUniDimension: '',
    rightForCityProvince: '',
    backCurrentLevel: '',
    rightForCityName:'',
    leftcurrentDimension:null,

    // 获取点击的展区下省份
    getAreasProvince:[],
    // 获取点击的省份下的市
    getProvincesCity:[],
  }),

  actions: {
    updatestoreInfectiousType(store: any, value: string) {
      store.storeInfectiousType = value
    },
    
    updateleftForCityProvince(store: any, value: string) {
      store.leftForCityProvince = value
    },
    updateleftForArea(store: any, value: string) {
      store.leftForArea = value
    },
    updateleftForCityDimension(store: any, value: string) {
      store.leftForCityDimension = value
    },
    updateleftForUniDimension(store: any, value: string) {
      store.leftForUniDimension = value
    },
    updateleftForUniName(store: any, value: string) {
      store.leftForUniName = value
    },

    updaterightForCityProvince(store: any, value: string) {
      store.rightForCityProvince = value
    },
    updaterightForCityName(store: any, value: string) {
      store.rightForCityName = value
    },
    updateBackCurrentLevel(store: any, value: string) {
      store.backCurrentLevel = value
    },
    updateGetAreasProvince(store: any, value: string) {
      store.getAreasProvince = value
    },
    updateGetProvincesCity(store: any, value: string) {
      store.getProvincesCity = value
    },
    // 左侧柱形图当前层级
    updateGetleftcurrentDimension(store: any, value: boolean) {
      store.leftcurrentDimension = value
    },
    
  }
})
