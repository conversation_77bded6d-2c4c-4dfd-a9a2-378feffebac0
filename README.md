# Vue 3 + TypeScript + Vite + pinia + Ant Design Vue


项目中事件总线处理兄弟组件传参，可以采用 `mitt` 组件实现
```ts
npm install --save mitt

import mitt, { Emitter } from 'mitt';

type Events = {
  foo: string;
  bar?: number;
};

const emitter: Emitter<Events> = mitt<Events>();

emitter.on('foo', (e) => {}); // 'e' has inferred type 'string'

emitter.emit('foo', 42)

type Events = {
  foo: string;
  bar?: number;
};
```
https://www.npmjs.com/package/mitt

This template should help get you started developing with Vue 3 and TypeScript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about the recommended Project Setup and IDE Support in the [Vue Docs TypeScript Guide](https://vuejs.org/guide/typescript/overview.html#project-setup).
