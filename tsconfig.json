{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "compilerOptions": {
    "baseUrl": "./",
    "paths": {
      "@": ["src"],
      "@/*": ["src/*"]
    }
  }
  // "exclude": [
  //   "node_modules"
  // ]
  //   "compilerOptions": {
  //     "target": "ESNext",
  //     "module": "ESNext",
  //     "strict": true,
  //     "jsx": "preserve",
  //     "moduleResolution": "Node",
  //     "skipLibCheck": true,
  //     "esModuleInterop": true,
  //     "allowSyntheticDefaultImports": true,
  //     "sourceMap": true,
  //     "baseUrl": ".",
  //     "types": ["vue"]
  //   },
  //   "include": [
  //     "src/**/*.ts",
  //     "src/**/*.tsx",
  //     "src/**/*.vue"
  // , "src/config/mapOption.js", "src/config/weatherOption.js", "src/utils/resize.js"  ],
  // "exclude": [
  //   "node_modules"
  // ]
}
