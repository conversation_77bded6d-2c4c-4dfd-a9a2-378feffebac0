server {
    resolver kube-dns.kube-system.svc.cluster.local valid=5s;
    listen 80 default_server;
    listen [::]:80 default_server;
    add_header X-Frame-Options SAMEORIGIN;
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers *;
    add_header X-Frame-Options SAMEORIGIN;

    root /root/ams-front-web;
    
    # Add index.php to the list if you are using PHP
    index index.html index.htm index.nginx-debian.html;
    server_name _;
    add_header Cache-Control no-cache;

    location /api/ {
        proxy_set_header    X-Forwarded-Host    $host;
        proxy_set_header    X-Forwarded-Server  $host;
        proxy_set_header    X-Real-IP           $remote_addr;
        proxy_set_header    X-Forwarded-For     $proxy_add_x_forwarded_for;
        proxy_set_header    X-Forwarded-Proto   $scheme;
        proxy_pass http://xnyy-alt:8080/;
    }
    
    location / {
            alias  /root/ams-front-web/;
            index index.html;
            try_files $uri $uri/ /index.html;
                  error_page 404 /index.html;
    }
}
