export default {
  'primary-color': 'var(--primary-color)',
  'primary-bg-color': 'var(--primary-color)',
  'primary-text-color': 'var(--primary-text-color)',
  'primary-active-bg-color': 'var(--primary-active-bg-color)',
  'primary-active-text-color': 'var(--primary-active-text-color)',
  'primary-dark-color': 'var(--primary-dark-color)',
  'primary-light-color': 'var(--primary-light-color)',
  'primary-layout-bg-color': 'var(--primary-bg-color)',
  'primary-topbar-bg-color': 'var(--primary-topbar-bg-color)',

  'normal-bg-color': 'var(--normal-bg-color)',
  'normal-border-color': 'var(--normal-border-color)',
  'normal-text-color': 'var(--normal-text-color)',

  'active-bg-color': 'var(--active-bg-color)',
  'active-border-color': 'var(--active-border-color)',

  'neutral-bg-color': 'var(--neutral-bg-color)',

  'disable-bg-color': 'var(--disable-bg-color)',
  'disable-border-color': 'var(--disable-border-color)',
  'disable-text-color': 'var(--disable-text-color)',

  'divide-bg-color': 'var(--divide-bg-color)',

  'table-row-bg': 'var(--primary-table-bg-color)',
  'table-active-row-bg': 'var(--primary-table-active-bg-color)',

  'error-color': 'var(--primary-text-end-color)',

  'primary-text-finish-color': 'var(--primary-text-finish-color)'
}
