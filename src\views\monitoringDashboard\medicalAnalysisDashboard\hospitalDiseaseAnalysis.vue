<template>
    <div
      class="containerThree"
      :style="{ width, height }"
    >
      <!-- 返回按钮 -->
      <!-- type="primary" -->
      <a-button
        v-if="showBackButton"
        @click="handleReturn"
        class="back-button"
      >
        <template #icon>
          <LeftOutlined />
        </template>
        返回
      </a-button>
      <div class="selectText">
        <span class="textTitle">
          
          <span v-if=!showBackButton>高原病患者就诊医院分布</span>
          <span v-if="showBackButton">{{ diseaseOptionsType }} / 科室分布</span>
        </span>
      </div>
      <a-select
                style="width: 180px"
                v-model:value="selectedDiseaseType"
                :options="diseaseOptions"
                class="selectType"
                @change="handleselectedDiseaseType"
              >
              </a-select>
      <!-- 饼图容器 -->
      <div
        class="chart-styles"
        v-if="isData == false"
      >
        <div
          id="drilldownPieChart"
          class="chart"
        ></div>
      </div>
      <div
        class="chart-styles no-data-available"
        v-else
      >
        <svg-icon
          name="noDataAvailable"
          width="121"
          height="130"
        ></svg-icon>
        <div>暂无数据</div>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, onMounted, watch, getCurrentInstance, nextTick,onBeforeUnmount } from 'vue'
  import { LeftOutlined } from '@ant-design/icons-vue'
  import * as echarts from 'echarts'
  import { getHospitalDistribution,getDisease } from './index.api.ts'
  const { proxy }: any = getCurrentInstance()
  // 定义饼图数据类型
  interface ChartData {
    name: string
    value: number
  }
  const props = defineProps({
    getMomentRangTimes: {
      type: Array,
      required: true // option 是必须的
    },
    option: {
      type: Array,
      required: true // option 是必须的
    },
    width: {
      type: String,
      default: '100%' // 默认宽度
    },
    height: {
      type: String,
      default: '100%' // 默认高度
    },
    up: {
      type: Function,
      required: false
    }
  })
  const selectedDiseaseType=ref<any>('全部');
  const diseaseOptions=ref<any>();
  const drillHospitals=ref<any>();
  const handleselectedDiseaseType=async (value:any)=>{
    console.log(value,4444)
    selectedDiseaseType.value=value;
    getSortInfectiousTypeInfectiousDisease(dateRangeTime.value)
  }

  const dateRangeTime = ref<any>(props.getMomentRangTimes)
  const diseaseOptionsType = ref<any>()
  console.log(dateRangeTime.value, 'op')
  watch(
    () => props.getMomentRangTimes,
    (newOption: any[]) => {
      // console.log(newOption, 'chuanzhi ')
      dateRangeTime.value = newOption
      getSortInfectiousTypeInfectiousDisease(dateRangeTime.value)
      // initChart(dateRangeTime.value)
    },
    { deep: true } // 深度监听
  )
  // 饼图状态
  const isDrilledDown = ref(false) // 是否处于下钻状态
  const showBackButton = ref(false) // 返回按钮的显示控制
  const currentTitle = ref('父级分类') // 当前标题
  const getArr = ref([])
  const getType = ref([])
  // 饼图数据
  const chartData = ref<ChartData[]>([])
  
  const getSortInfectiousTypeInfectiousDisease = async (value: any[]) => {
    let obj = {
      from: value[0] || '',
      to: value[1] || '',
      disease: selectedDiseaseType.value == '全部' ? '' : selectedDiseaseType.value,
      hospital:drillHospitals.value
    }
    chartData.value = []
    getHospitalDistribution(obj).then((res: any) => {
      if (res.code == 0) {
        // chartData.value=[]
        let countNum = 0
        res.data.altDiseaseDistribution.map((item: { key: string; cnt: any }) => {

          chartData.value.push({
            name: item.key,
            value: item.cnt
          })
        })
        getArr.value = res.data
        console.log(chartData.value, 'chartData.value1111')
        let valueCount = 0
        chartData.value.forEach((item) => {
          valueCount = item.value + valueCount
        })
        console.log(valueCount, 'valueCount2222')
        if (valueCount == 0) {
          isData.value = true
        } else {
          isData.value = false
        }
        nextTick(() => {
          initChart(value)
        })
      } else {
        proxy.$message.error(res?.errorMsg)
      }
    })
  }

    // 折线下拉数据
    async function getHospitalsData(value:any) {

    let params = {
      from: value[0] || '',
      to: value[1] || ''
    }
    diseaseOptions.value=[{
        label:'全部',
        value:'全部'
    }]
    await getDisease(params)
      .then((res) => {
        if (res.code == 0) {
            // diseaseOptions.value = res?.data ?? []
            res.data.map(item=>{
                diseaseOptions.value.push({
                    label:item,
                    value:item
                })
            })
        }
      })
      .catch((err) => {
        console.log(err, 'err')
      })
  }
  // 保存父级数据
  const parentData = ref<ChartData[]>([])
  
  let chartInstance: echarts.ECharts
  const isData = ref(true)
  // 初始化饼图
  const initChart = (dateRangeTime: any[]) => {
    const chartDom = document.getElementById('drilldownPieChart')

    if (!chartDom) return
    chartInstance = echarts.init(chartDom)
  
    updateChart()
  
    // 监听饼图点击事件
    chartInstance.on('click', async (params) => {
      // 如果已下钻到一级子类，不再响应点击
      if (isDrilledDown.value) return
  
      diseaseOptionsType.value = params.name
      // 记录父级数据
      parentData.value = [...chartData.value]
      drillHospitals.value=params.name;
      getSortInfectiousTypeInfectiousDisease(dateRangeTime);
      // 更新状态
      isDrilledDown.value = true
      showBackButton.value = true
      currentTitle.value = `${diseaseOptionsType.value} 的子分类`
  
      updateChart()
    })
  }
  const selectedThree = ref<any>([])
  // 更新饼图数据
  const updateChart = () => {
    console.log(chartData.value, 'chartData.value')
    const option = {
      color: ['#13A89B', '#2316AB', '#405BC8', '#CCF56A', '#14705E', '#00CFBE', '#098BEA', '#079A35', '#9DD962'],
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c}例'
      },
      legend: {
        // x: 'center',
        // y: 'bottom',
        left:'center',
        bottom: '5%',
        itemHeight: 16,
        itemWidth: 16,
        itemGap: 15,
        icon: 'rect', // 设置图例的形状
        type: 'scroll', // 数据过多时，分页显示
        selected: selectedThree//这里默认显示数组中前十个，如果不设置，则所有的数据都会显示在图表上
      },
    //   grid: {
    //     left: '3%',
    //     right: '4%',
    //     bottom: '4%',
    //     containLabel: true
    //   },
      series: [
        {
          type: 'pie',
  
          // data: chartData.value,
          data: chartData.value.map((item) => ({
            ...item,
            label: {
              show: item.value > 0 // 数据值为 0 时不显示标签
            },
            labelLine: {
              show: item.value > 0 // 数据值为 0 时不显示指引线
            }
          })),
          label: {
            show: true,
            formatter: '{b}: {c}例'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 10,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true,
            length: 10, // 第一段指导线 长度
            length2: 10 // 第二段指导线 长度
          },
          // name: name,
          minAngle: 5, //最小角度
          radius: ['43%', '61%'],
          center: ['50%', '43%'],
          avoidLabelOverlap: true
        }
      ]
    }
    chartInstance.setOption(option)
  }
  
  // 返回父级数据
  const handleReturn = () => {
    chartData.value = parentData.value // 恢复父级数据
    isDrilledDown.value = false // 重置下钻状态
    showBackButton.value = false // 隐藏返回按钮
    currentTitle.value = '父级分类';
    drillHospitals.value=''
    getSortInfectiousTypeInfectiousDisease(dateRangeTime.value)
    updateChart()
  }
  // 处理窗口大小变化时，重新调整图表尺寸
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }
  onMounted(() => {
    // initChart(dateRangeTime.value)
    getSortInfectiousTypeInfectiousDisease(dateRangeTime.value)
    getHospitalsData(dateRangeTime.value)
  })
  onMounted(() => {
   
    window.addEventListener('resize', handleResize)
  })
  
  // 在组件卸载时销毁图表，并移除 resize 事件监听
  onBeforeUnmount(() => {
    if (chartInstance) {
      chartInstance.dispose()
    }
    window.removeEventListener('resize', handleResize)
  })
  </script>
  
  <style scoped>
  .containerThree {
    /* width: 100%;
      height: 100%;
      margin: 0 auto; */
    /* position: relative; */
    width: 100%;
    height: 100%;
    position: relative;
  }
  .selectType{
    position:absolute;
    top:0;
    right:20px;
  }
  .chart {
    /* height: 400px; */
    width: 100%;
    height: 100%;
  }
  .selectText{
    margin-left: 8px;
  }
  .textTitle {
    font-size: 16px;
    font-weight: bold;
    color: #07123c;
    font-family: 'Noto Sans SC';
    line-height: 30px;
  }
  
  .back-button {
    float: left;
    margin-bottom: 10px;
    margin-right: 20px;
    margin-left: 8px;
    display: flex;
    padding: 5px 12px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
  }
  
  .chart-styles {
    width: 100%;
    /* height: calc(100% - 20px); */
    height: 100% ;
    /* padding: 10px; */
    box-sizing: border-box;
  }
  .no-data-available {
    width: 100%;
    /* height: 100%; */
    height: calc(100% - 60px);
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    color: #5e6580;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
  
  </style>
  