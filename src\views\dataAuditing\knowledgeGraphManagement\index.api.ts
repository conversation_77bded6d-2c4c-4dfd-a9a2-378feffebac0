import request from '@/utils/request.ts'

// 字典
export function getDicList(params) {
  return request({
    url: '/visitDict/listAll',
    method: 'get',
    params
  })
}

// 分页查询
export function getListPage(params) {
  return request({
    url: '/altKnowledgeGraphAudit/listPage',
    method: 'get',
    params
  })
}

// 修改
export function updatePage(params) {
  return request({
    url: '/altKnowledgeGraphAudit/update',
    method: 'post',
    data: params
  })
}

// 获取知识图谱表
export function getKnowledgeGraphTable(params: any) {
  return request({
    url: '/altKnowledgeGraph/listPage',
    method: 'get',
    params: params
  })
}

// 获取指南分页
export function getKnowledgeGuidePage(params: any) {
  return request({
    url: '/altKnowledgeGuide/listPage',
    method: 'get',
    params: params
  })
}

// 新增数据
export function addKnowledgeGuide(params: any) {
  return request({
    url: '/altKnowledgeGraphAudit/add',
    method: 'post',
    data: params
  })
}

// 删除数据
export function deleteKnowledgeGuide(params: any) {
  return request({
    url: '/altKnowledgeGraphAudit/deleteById',
    method: 'delete',
    params: params
  })
}
