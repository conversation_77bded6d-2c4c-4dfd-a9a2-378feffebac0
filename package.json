{"name": "pestilence-start", "private": true, "version": "1.0.0", "lint-staged": {"*.{js,jsx,ts,tsx,vue,md,html,css,less,json,yml,yaml}": ["prettier --write"]}, "scripts": {"dev": "vite", "build": "node --max_old_space_size=16384  node_modules/vite/bin/vite.js build", "build:prod": "node --max_old_space_size=16384  node_modules/vite/bin/vite.js build --mode production", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueuse/core": "^11.2.0", "ant-design-vue": "4.2.6", "axios": "^1.7.7", "dayjs": "^1.11.13", "docxtemplater": "^3.54.1", "docxtemplater-image-module-free": "^1.1.1", "echarts": "5.5.1", "file": "^0.2.2", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "leaflet": "^1.9.4", "lodash": "^4.17.21", "md5": "^2.3.0", "nprogress": "^0.2.0", "os": "^0.1.2", "pdfjs-dist": "2.16.105", "pinia": "^2.2.8", "pinia-plugin-persistedstate": "^4.2.0", "pizzip": "^3.1.7", "qs": "^6.14.0", "vue": "^3.5.12", "vue-request": "^2.0.4", "vue-router": "^4.4.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.14", "@types/lodash": "^4.17.13", "@types/node": "^22.9.0", "@types/vue": "^2.0.0", "@vitejs/plugin-vue": "^5.1.5", "@vitejs/plugin-vue-jsx": "^4.1.0", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "autoprefixer": "^8.0.0", "fast-glob": "^3.3.2", "husky": "^9.1.6", "js-cookie": "^3.0.5", "less": "^4.2.0", "lint-staged": "15.2.7", "postcss": "^8.4.49", "postcss-preset-env": "^10.1.0", "prettier": "^3.3.3", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.14", "typescript": "~5.6.2", "unplugin-auto-import": "^0.19.0", "vite": "5.4.10", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.1.8"}, "resolutions": {"esbuild": "npm:esbuild-wasm@latest"}}