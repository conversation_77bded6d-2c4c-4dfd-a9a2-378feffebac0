<template>
  <div class="wrapper clearfix">
    <!-- 时间筛选 -->
    <div class="time-filter">
      <a-form :model="formData" name="time_related_controls">
        <a-form-item name="range-picker" label="统计时间范围">
          <a-range-picker v-model:value="formData.time" @change="handleChangeDate" :presets="presetTimeRange()"
            value-format="YYYY-MM-DD" :allowClear="false" />
          <!-- :disabled-date="disabledStartDate" -->
        </a-form-item>
      </a-form>
    </div>
    <!-- 图表内容 -->
    <div class="wrapper-content">
      <!-- 折线图/柱状图 -->
      <div class="top-charts">
        <!-- 折线图 -->
        <div class="charts-styles-box">
          <lineTrend :loading="lineLoading" chartId="chartContainer9" :option="lineOptionsData" title="高原病就诊趋势"
            v-model:modelValue="formData.selectedHospital" :selectOptions="lineDataList"
            @change="handleSelectedHospital" />
        </div>
        <!-- 柱状图 -->
        <div class="charts-styles-box">
          <lineTrend :loading="barLoading" chartId="barChartContainer99" :option="barChartOptionsData" title="高原病就诊量排序"
            :isShowSelect="false" />
        </div>
        <!-- 饼图 -->
        <!-- 高原病患者就诊医院分布 -->

        <!-- 高原病患者就诊科室分布 -->
        <!-- <div class="charts-styles-box">
          <lineTrend :loading="departmentLoading" ref="pieChartContainer22" chartId="pieChartContainer22"
            :option="pieChartOptionsData.departmentOptions" title="高原病患者就诊科室分布"
            v-model:modelValue="formData.selectedDepartment" :selectOptions="pathogenyList"
            @change="handleSelectedDepartment" />
        </div> -->
      </div>
    </div>
    <div class="charts-styles-box">
      <!-- <lineTrend :loading="hospitalLoading" ref="pieChartContainer12" chartId="pieChartContainer12"
            :option="pieChartOptionsData.hospitalOptions" title="高原病患者就诊医院及科室分布"
            v-model:modelValue="formData.selectedPathogeny" :selectOptions="departmentList"
            @change="handleSelectedPathogeny" /> -->
      <hospitalDisease :getMomentRangTimes="timeValue" />
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, toRaw, useTemplateRef, nextTick, onBeforeMount } from 'vue'
import { presetTimeRange } from '@/utils/utils'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
import lineTrend from '@/components/ECharts/lineTrend.vue'
import { chartOptions, barChartOptions, pieChartOptions } from './index.config.ts'
import { getTrending, getHospitals, getAmount, getHospitalDistribution, getDepartmentDistribution, getDisease } from './index.api.ts'
import { color } from 'echarts'
import { operationLog } from '@/api/log'
import hospitalDisease from './hospitalDisease.vue'

const disabledStartDate = (current: dayjs.Dayjs) => {
  return current.isBefore(dayjs('2022-01-01'))
}

// 时间选择
const formData = reactive({
  time: [],
  selectedHospital: '全部',
  selectedPathogeny: '全部',
  selectedDepartment: '全部'
})

// 时间选择
function handleChangeDate(value: any) {
  formData.time = value;

  timeValue.value = value
  clearData()
  query()
}

// 折线图数据 start
const lineDataList = ref([])
const brokenLineDataList = ref([])
const lineLoading = ref(false)
const lineOptionsData = reactive({
  ...chartOptions,
  legend: {
    ...chartOptions.legend,
    selected: []
  },
  xAxis: [
    {
      ...chartOptions.xAxis[0],
      data: []
    }
  ],
  series: []
})
// 折线图数据请求
async function getLineTrendingData() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  lineLoading.value = true
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || '',
    hospital: data.selectedHospital == '全部' ? '' : data.selectedHospital
  }
  await getTrending(params)
    .then((res) => {
      if (res.code == 0) {
        // 方案二
        // // 时间线
        // let dates = res?.data?.map((item) => item.date) ?? [];
        // // 提取所有独特的疾病名称
        // let diseases = Array.from(new Set(res?.data?.flatMap((item) => item.diseases)));
        // // 初始化每个疾病的数据序列
        // const diseaseData = diseases.reduce((acc, disease) => {
        //   acc[disease] = dates.map((date) => {
        //     const dayData = res?.data?.find((item) => item.date === date);
        //     if (dayData) {
        //       const index = dayData.diseases.indexOf(disease);
        //       return index !== -1 ? dayData.cnt[index] : 0;
        //     }
        //     return 0;
        //   });
        //   return acc;
        // }, {});

        // brokenLineDataList.value = diseases.map((disease) => ({
        //   name: disease,
        //   type: "line",
        //   symbol: "none", //去掉折线图中的节点
        //   smooth: true, //true 为平滑曲线，false为直线
        //   stack: "Total",
        //   label: {
        //     show: true,
        //     position: "top",
        //   },
        //   areaStyle: {},
        //   emphasis: {
        //     focus: "series",
        //   },
        //   data: diseaseData[disease],
        // }));

        // Object.assign(lineOptionsData, {
        //   ...lineOptionsData,
        //   legend: {
        //     ...lineOptionsData.legend,
        //     selected: diseases,
        //   },
        //   xAxis: [
        //     {
        //       ...lineOptionsData.xAxis[0],
        //       data: dates,
        //     },
        //   ],
        //   series: brokenLineDataList.value,
        // });

        // 方案二
        // 这种性能更好，方便后期维护
        // 1. 提取所有日期和疾病
        const dates = res?.data?.map((item) => item.date) ?? []
        const diseases = Array.from(new Set(res?.data?.flatMap((item) => item.diseases)))

        // 2. 创建日期-疾病映射
        const diseaseMap = new Map()
        res?.data?.forEach((item) => {
          item.diseases.forEach((disease, index) => {
            const key = `${item.date}_${disease}`
            diseaseMap.set(key, item.cnt[index]) // 映射日期_疾病 -> 对应计数
          })
        })

        // 3. 构建每种疾病的时间序列数据
        brokenLineDataList.value = diseases.map((disease) => {
          const data = dates.map((date) => {
            const key = `${date}_${disease}`
            return diseaseMap.get(key) || 0 // 如果没有数据则默认返回 0
          })
          return {
            name: disease,
            type: 'line',
            symbol: 'none',
            smooth: true,
            stack: 'Total',
            label: {
              show: true,
              position: 'top'
            },
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data
          }
        })

        // 4. 更新图表配置
        Object.assign(lineOptionsData, {
          ...lineOptionsData,
          legend: {
            ...lineOptionsData.legend,
            selected: diseases
          },
          xAxis: [
            {
              ...lineOptionsData.xAxis[0],
              data: dates // 设置 X 轴数据
            }
          ],
          series: brokenLineDataList.value // 设置折线图数据
        })
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    }).finally(() => {
      lineLoading.value = false
    })
}
// 折线下拉数据
async function getHospitalsData() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || ''
  }
  await getHospitals(params)
    .then((res) => {
      if (res.code == 0) {
        lineDataList.value = res?.data ?? []
        pathogenyList.value = res?.data ?? []
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
// 折线-医院下拉
function handleSelectedHospital(value: any) {
  getLineTrendingData()
}
// end

// 柱状图数据 start
type ChartData = {
  type: string
  name: string
  value?: number
  xAxis?: number
  yAxis?: number
}

interface ChartSeries {
  name: string
  type: string
  data: number[]
  label?: any
}
interface AmountData {
  hospital?: string
  inpatientCnt?: number
  outpatientCnt?: number
  totalVisitCnt?: number
}
const barsData = ref<ChartSeries[]>([])
const barLoading = ref(false)
const barChartOptionsData = reactive({
  ...barChartOptions,
  legend: {
    ...barChartOptions.legend,
    data: ['门急诊量', '住院量']
  },
  xAxis: {
    ...barChartOptions.xAxis,
    data: []
  },
  series: [],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (params) {
      const filtered = params.filter(item => item.value !== 0)
      if (filtered.length === 0) return ''
      const date = filtered[0].axisValue
      const lines = filtered.map(item => `${item.marker}${item.seriesName}: ${item.value}`)
      return [date, ...lines].join('<br/>')
    }
  },
})

async function getAmountData() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  barLoading.value = true
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || ''
  }
  await getAmount(params)
    .then((res) => {
      if (res.code == 0) {
        res.data.sort(function (a: { totalVisitCnt: number }, b: { totalVisitCnt: number }) {
          return b.totalVisitCnt - a.totalVisitCnt
        })
        // 门诊急诊量
        const outpatientCnt = res?.data?.map((item: AmountData) => item.outpatientCnt)
        // 住院量
        const inpatientCnt = res?.data?.map((item: AmountData) => item.inpatientCnt)
        // 总就诊量
        const totalVisitCnt = res?.data?.map((item: AmountData) => item.totalVisitCnt)
        // 提取所有独特的疾病名称
        const hospital = Array.from(new Set(res?.data?.flatMap((item) => item.hospital)))

        const series: ChartSeries[] = [
          {
            name: '门急诊量',
            type: 'bar',
            data: outpatientCnt.length > 0 ? outpatientCnt : [],
            label: {
              color: '#5E6580',
              show: outpatientCnt.some((item) => item > 0),
              position: 'top'
            }
          },
          {
            name: '住院量',
            type: 'bar',
            data: inpatientCnt.length > 0 ? inpatientCnt : [],
            label: {
              color: '#5E6580',
              show: inpatientCnt.some((item) => item > 0),
              position: 'top'
            }
          }
        ]

        barsData.value = series

        Object.assign(barChartOptionsData, {
          ...barChartOptionsData,
          xAxis: {
            ...barChartOptionsData.xAxis,
            data: hospital
          },
          series: outpatientCnt.length == 0 && inpatientCnt.length == 0 ? [] : series
        })
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    }).finally(() => {
      barLoading.value = false
    })
}
// end

// 饼图数据 start
const pathogenyList = ref([]) // 医院
const departmentList = ref([]) //科室
const pieChartContainer12 = useTemplateRef('pieChartContainer12')
const pieChartContainer22 = useTemplateRef('pieChartContainer22')
const departmentLoading = ref(false)
const hospitalLoading = ref(false)
const pieChartOptionsData = reactive({
  departmentOptions: {
    ...pieChartOptions,

    legend: {
      ...pieChartOptions.legend,
      selected: []
    },
    series: [
      {
        ...pieChartOptions.series[0],
        // labelLayout: function (params) {
        //   const isLeft =
        //     params.labelRect.x < pieChartContainer22.value?.chartInstance?.getWidth() / 2;
        //   const points = params.labelLinePoints;
        //   points[2][0] = isLeft
        //     ? params.labelRect.x
        //     : params.labelRect.x + params.labelRect.width;
        //   return {
        //     labelLinePoints: points,
        //   };
        // },
        data: [],
        name: '高原病患者就诊科室分布'
      }
    ]
  },
  hospitalOptions: {
    ...pieChartOptions,
    legend: {
      ...pieChartOptions.legend,
      selected: []
    },
    series: [
      {
        ...pieChartOptions.series[0],
        // labelLayout: function (params) {
        //   const isLeft =
        //     params.labelRect.x < pieChartContainer12.value?.chartInstance?.getWidth() / 2;
        //   const points = params.labelLinePoints;
        //   points[2][0] = isLeft
        //     ? params.labelRect.x
        //     : params.labelRect.x + params.labelRect.width;
        //   return {
        //     labelLinePoints: points,
        //   };
        // },
        data: [],
        name: '高原病患者就诊医院及科室分布'
      }
    ]
  }
})
// 医院饼图数据请求
async function getHospitalDistributionData() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  hospitalLoading.value = true
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || '',
    disease: data.selectedPathogeny == '全部' ? '' : data.selectedPathogeny
  }
  await getHospitalDistribution(params)
    .then((res) => {
      if (res.code == 0) {
        let newData =
          res.data.altDiseaseDistribution.map((item: any) => {
            return {
              value: item.cnt,
              name: item.key,
              label: {
                show: item.cnt > 0 // 数据值为 0 时不显示标签
              },
              labelLine: {
                show: item.cnt > 0 // 数据值为 0 时不显示指引线
              }
            }
          }) ?? []
        pieChartOptionsData.hospitalOptions = {
          ...pieChartOptionsData.hospitalOptions,
          series:
            res?.data?.altDiseaseDistribution.length > 0
              ? [
                {
                  ...pieChartOptionsData.hospitalOptions.series[0],
                  data: newData,
                  name: '高原病患者就诊医院及科室分布'
                }
              ]
              : []
        }
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    }).finally(() => {
      hospitalLoading.value = false
    })
}

// 医院下拉
function handleSelectedPathogeny(value: any) {
  getHospitalDistributionData()
}

// 科室饼图请求
async function getDepartmentDistributionData() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  departmentLoading.value = true
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || '',
    hospital: data.selectedDepartment == '全部' ? '' : data.selectedDepartment
  }
  await getDepartmentDistribution(params)
    .then((res) => {
      if (res.code == 0) {
        let newData =
          res?.data?.map((item) => {
            return {
              value: item.cnt,
              name: item.key,
              label: {
                show: item.cnt > 0 // 数据值为 0 时不显示标签
              },
              labelLine: {
                show: item.cnt > 0 // 数据值为 0 时不显示指引线
              }
            }
          }) ?? []
        pieChartOptionsData.departmentOptions = {
          ...pieChartOptionsData.departmentOptions,
          series:
            res?.data?.length > 0
              ? [
                {
                  ...pieChartOptionsData.departmentOptions.series[0],
                  data: newData,
                  name: '高原病患者就诊科室分布'
                }
              ]
              : []
        }
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    }).finally(() => {
      departmentLoading.value = false
    })
}

// 科室下拉请求
async function getDepartmentSelectData() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || ''
  }
  await getDisease(params)
    .then((res) => {
      if (res.code == 0) {
        departmentList.value = res?.data ?? []
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

// 科室下拉
function handleSelectedDepartment(value: any) {
  getDepartmentDistributionData()
}
// end

// 初始化数据
function init() {
  // 初始化筛选数据
  Object.assign(formData, {
    time: [],
    selectedHospital: '全部',
    selectedPathogeny: '全部',
    selectedDepartment: '全部'
  })
  clearData()
}

// 清空数据
function clearData() {
  // 折线图数据
  lineDataList.value = []
  brokenLineDataList.value = []
  Object.assign(lineOptionsData, {
    ...chartOptions,
    legend: {
      ...chartOptions.legend,
      selected: []
    },
    toolbox: {},
    xAxis: [
      {
        ...chartOptions.xAxis[0],
        data: []
      }
    ],
    series: []
  })
  // 柱状图数据
  barsData.value = []
  Object.assign(barChartOptionsData, {
    ...barChartOptions,
    legend: {
      data: ['门急诊量', '住院量']
    },
    xAxis: {
      ...barChartOptions.xAxis,
      data: []
    },
    series: []
  })

  // 饼图
  pathogenyList.value = []
  departmentList.value = []
  pieChartOptionsData.hospitalOptions = {
    ...pieChartOptions,
    legend: {
      ...pieChartOptions.legend,
      selected: []
    },
    series: [
      {
        ...pieChartOptions.series[0],
        data: [],
        name: '高原病患者就诊医院及科室分布'
      }
    ]
  }
  pieChartOptionsData.departmentOptions = {
    ...pieChartOptions,
    legend: {
      ...pieChartOptions.legend,
      selected: []
    },
    series: [
      {
        ...pieChartOptions.series[0],
        data: [],
        name: '高原病患者就诊科室分布'
      }
    ]
  }
}

async function query() {
  await getHospitalsData()
  await getDepartmentSelectData()
  await getLineTrendingData()
  await getAmountData()
  // await getHospitalDistributionData()
  // await getDepartmentDistributionData()
}
const timeValue = ref<any[]>()
onBeforeMount(() => {
  formData.time = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];

  timeValue.value = formData.time;
  console.log(timeValue.value, 'timeValue')
  query()

})
// onMounted(() => {
//   formData.time = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
//   query()
// })

const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '高原病就诊分析仪表盘',
    ip: baseIP,
    type: '高原病监测分析'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
onMounted(() => {
  operationLogs()
})
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background-color: #f8fafc;
  display: flex;
  flex-direction: column;
}

.time-filter {
  position: fixed;
  right: 30px;
  top: 58px;
  z-index: 999;
  height: 30px;
  display: flex;
  justify-content: flex-end;
}

.wrapper-content {
  width: 100%;
  background-color: #f8fafc;
  display: flex;
  flex-direction: column;

  .top-charts {
    width: 100%;
    height: auto;
    //  height: 390px;
    position: relative;

    display: grid;
    grid-template-columns: repeat(2, minmax(200px, 1fr));
    gap: 16px;

    .charts-styles-box {
      // width: calc(50% - 10px);
      // height: 100%;
      width: 100%;
      height: 390px;
      padding: 10px;
      box-sizing: border-box;

      padding: 10px;
      background: #ffffff;
      border-radius: 12px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
  }
}

.charts-styles-box {
  // width: calc(50% - 10px);
  // height: 100%;
  width: 100%;
  height: 390px;
  padding: 10px;
  margin-top: 16px;
  box-sizing: border-box;

  padding: 10px;
  background: #ffffff;
  border-radius: 12px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
</style>
