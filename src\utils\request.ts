import axios from 'axios'
import { message } from 'ant-design-vue'
import { config } from '@/config/index'
import { setCookie, removeCookie } from './cookie'

// 处理页面来源
let isRedirecting = false // 防止多次跳转
let isShowingError = false // 控制错误提示只展示一次

// 添加类型定义
type HttpErrorCode = 401 | 403 | 404 | 500 | 'default'
// 展示错误信息的函数，控制只提示一次
const showErrorMessage = (msg: string) => {
  if (!isShowingError) {
    isShowingError = true
    message.error(msg)
    setTimeout(() => {
      isShowingError = false // 重置标志位，允许下一次提示
    }, 3000) // 错误提示的持续时间，
  }
}

// 通用错误处理函数
const urlError = ['/404', '/403']
const urlParams = new URLSearchParams(window.location.search)
const pageSource = urlParams.get('source')||''
const parentOrigin: string = urlParams.get('parentOrigin')||''
const handleRedirect = (msg: string, redirectUrl: string) => {
  console.log('handleRedirect:', pageSource)
  // 如果是特殊疾病库，跳转到404页面
  if (pageSource == 'specialDiseaseBank') {
    setTimeout(() => {
      location.href = import.meta.env.VITE_BASE_URL + `/404?source=${pageSource}`
      isRedirecting = false
      // 向父页面发送消息
      // window.parent.postMessage({ type: 'error', data: msg }, parentOrigin)
      // 子页面监听父页面的回复消息
      // window.addEventListener(
      //   'message',
      //   function (event) {
      //     if (event.origin !== parentOrigin) {
      //       return
      //     }
      //     console.log('Message from parent:', event.data)
      //   },
      //   false
      // )
    }, 500)
    return
  }

  // 如果不是特殊疾病库，跳转到 redirectUrl
  if (!isRedirecting) {
    isRedirecting = true
    if (urlError.includes(redirectUrl)) {
      setTimeout(() => {
        location.href = import.meta.env.VITE_BASE_URL + redirectUrl
        isRedirecting = false
      }, 500)
      return
    }

    localStorage.removeItem('token')
    localStorage.removeItem('expiresAt')
    localStorage.removeItem('userInfo')
    // localStorage.clear()
    sessionStorage.clear()
    setCookie('X-YITU-USER-TOKEN', '')
    removeCookie('X-YITU-USER-TOKEN')
    showErrorMessage(msg)
    setTimeout(() => {
      isRedirecting = false
      window.location.href = redirectUrl
    }, 500)
    return
  }
}

let redirectUrl = `${import.meta.env.MODE == 'development' ? 'http://10.10.5.117' : window.location.origin}/akkare-platform/login?service=${config.APP_CODE}&redirectTo=${encodeURIComponent(config.REDIRECT_URI)}`
// 定义 HTTP 状态码处理函数
const httpErrCode: Record<number | 'default', () => void> = {
  400: () => handleRedirect('错误请求', '/404'),
  401: () => handleRedirect('登录失效，请重新登录', redirectUrl),
  403: () => handleRedirect('拒绝访问', '/403'),
  404: () => handleRedirect('资源未找到', '/404'),
  405: () => handleRedirect('请求方法未允许', '/404'),
  408: () => handleRedirect('请求超时', '/404'),
  500: () => handleRedirect('服务器异常', redirectUrl),
  501: () => handleRedirect('网络未实现', redirectUrl),
  502: () => handleRedirect('网络错误', redirectUrl),
  503: () => handleRedirect('服务不可用', redirectUrl),
  504: () => handleRedirect('网络超时', redirectUrl),
  505: () => handleRedirect('http版本不支持该请求', redirectUrl),
  99012: () => handleRedirect('登录失效，请重新登录', redirectUrl),
  default: () => showErrorMessage('请求失败，请稍后再试')
}

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL + '/api',
  timeout: 300000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 添加 token
    config.headers['App-Code'] = 'ALT' // 添加 token
    const token = localStorage.getItem('token') || ''
    // config.headers['Authorization'] = token // 添加 token
    if (token) setCookie('X-YITU-USER-TOKEN', token)

    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

/**
 * @param {string} 4001 新增用户其他平台有相同账号的 code，做的单独处理
 */
const errCode = ['4001']
// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const contentType = response.headers['content-type'] || ''
    const isFileStream = contentType.includes('application/octet-stream') || contentType.includes('application/pdf') || contentType.includes('application/zip')

    if (isFileStream) {
      // 如果是文件流，直接返回 response
      return response
    }

    // 处理 blob 数据流
    if (response.data instanceof Blob) {
      return response
    }

    const res = response.data
    if (res.code == 0 || res.code == 200 || errCode.includes(res.code)) {
      isShowingError = false
      return response.data
    }
    
    // 调用对应的 HTTP 状态码处理函数
    httpErrCode[Number(res.code)]()
    return Promise.reject(res.errorMsg || '请求失败，请稍后再试')
  },
  (error) => {
    console.log('响应错误-error:', error)
    if (error.response) {
      const { status } = error.response

      // 调用对应的 HTTP 状态码处理函数
      ;(httpErrCode[status as HttpErrorCode] || httpErrCode.default)()
    } else if (error.code === 'ECONNABORTED') {
      showErrorMessage('请求超时，请稍后再试')
    } else {
      showErrorMessage(error.message || error.errorMsg || '系统错误')
    }

    return Promise.reject(error || '请求失败，请稍后再试')
  }
)

export default service
