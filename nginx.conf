user root;
worker_processes auto;
pid /run/nginx.pid;

events {
  worker_connections 768;
  # multi_accept on;
}

http {

  server_tokens off;
  ##
  # Basic Settings
  ##
  add_header X-Frame-Options SAMEORIGIN;
        client_max_body_size 10240m;
  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  keepalive_timeout 65;
  types_hash_max_size 2048;
  # server_tokens off;

  # server_names_hash_bucket_size 64;
  # server_name_in_redirect off;

  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  ##
  # SSL Settings
  ##

  ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
  ssl_prefer_server_ciphers on;

  ##
  # Logging Settings
  ##

  access_log /var/log/nginx/access.log;
  error_log /var/log/nginx/error.log;

  ##
  # Gzip Settings
  ##
  etag on;
        expires 7d;
  gzip on;
  gzip_disable "msie6";

  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_buffers 16 8k;
  gzip_http_version 1.1;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript application/octet-stream;

  ##
  # Virtual Host Configs
  ##


map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}


  include /etc/nginx/conf.d/*.conf;
  ##include /etc/nginx/sites-enabled/*;
}

stream {
          include /etc/nginx/conf.d/*.cnf;
}
