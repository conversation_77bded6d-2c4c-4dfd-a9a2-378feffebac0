import request from '../../utils/request.ts'


// 核心指标
export function coreIndicator(query: any) {
  return request({
    url: '/alt/gis/coreIndicator',
    method: 'get',
    params: query
  })
}

// 获取指定维度区域发病量
export function allDimensionIndicator(data: any) {
  return request({
    url: '/alt/gis/dimensionIndicator',
    method: 'post',
    data
  })
}

export function belongWith(query: any) {
  return request({
    url: '/alt/gis/belongWith',
    method: 'get',
    params: query
  })
}
// 展区下的传染病指标
export function allAreasIndicators(query: any) {
  return request({
    url: '/gis/allAreasIndicators',
    method: 'get',
    params: query
  })
}


// 展区下的省一级
export function allProvincesIndicators(query: any) {
  return request({
    url: '/gis/allProvincesIndicators',
    method: 'get',
    params: query
  })
}
// 省下的市一级

export function allCitiesIndicators(query: any) {
  return request({
    url: '/gis/allCitiesIndicators',
    method: 'get',
    params: query
  })
}

// 市下的机构一级数据

export function allPatientUnitsIndicators(query: any) {
  return request({
    url: '/gis/allPatientUnitsIndicators',
    method: 'get',
    params: query
  })
}



// 全国地图发病分布
export function allProvincesDistribution(query: any) {
  return request({
    url: '/gis/allProvincesDistribution',
    method: 'get',
    params: query
  })
}
// 维度指标
export function dimensionIndicator(query: any) {
  return request({
    url: '/gis/dimensionIndicator',
    method: 'get',
    params: query
  })
}
//省下所有市  发病量

export function allCitiesDistribution(query: any) {
  return request({
    url: '/gis/allCitiesDistribution',
    method: 'get',
    params: query
  })
}
//市下所有区  发病量

export function allPatientUnitsDistribution(query: any) {
  return request({
    url: '/gis/allPatientUnitsDistribution',
    method: 'get',
    params: query
  })
}
