@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 不要直接用以下变量 以下变量为主题相关 请使用data-theme中的 */
  --color-light-background: #ffffff;
  --color-light-border: #f0f0f0;
  --color-light-text: #333333;

  --color-light-neutral-background: #fafafa;

  --color-light-disable-background: #f5f5f5;
  --color-light-disable-border: rgba(240, 240, 240, 1);
  --color-light-disable-text: rgba(0, 0, 0, 0.45);
  --color-light-divide: rgba(0, 0, 0, 0.06);
  --scorll-bar-color: rgba(0, 0, 0, 0.3);

  /* 以下变量为暗黑模式 */
  --color-dark-background: #333333;
  --color-dark-border: #0f0f0f;
  --color-dark-text: #ffffff;
  --color-dark-bar-color: rgba(255, 255, 255, 0.3);

  --color-dark-neutral-background: #383838;

  --color-dark-disable-background: #0a0a0a;
  --color-dark-disable-border: rgba(#0f0f0f, 1);
  --color-dark-disable-text: rgba(#ffffff, 0.45);
  --color-dark-divide: rgba(255, 255, 255, 0.06);
  /* 以下变量是品牌色可以直接使用 */
  --neutral-bg-color: var(--color-light-neutral-background);
  --normal-bg-color: var(--color-light-background);
  --normal-border-color: var(--color-light-border);
  --normal-text-color: var(--color-light-text);
  --disable-bg-color: var(--color-light-disable-background);
  --disable-border-color: var(--color-light-disable-border);
  --disable-text-color: var(--color-light-disable-text);
  --divide-bg-color: var(--color-light-divide);

  --primary-color: #209e85;
  --primary-bg-color: #f0f5f4;
  --primary-dark-color: #1c8b74;
  --primary-light-color: #59dec2;
  --primary-text-color: #ffffff;
  --primary-text-pendding-color: #faad14;
  --primary-text-end-color: #ff4d4f;
  --primary-text-finish-color: #52c41a;

  --primary-active-bg-color: #e8fff7;
  --primary-active-text-color: #209e85;
  --primary-table-bg-color: #b5ecd9;
  --primary-table-active-bg-color: #60c5aa;

  --primary-topbar-bg-color: #209e85;
  --select-bg-color: #eefaf6;
  --scroll-bar-bg: var(--scorll-bar-color);
  --separate-bar-color: #eef3f2;

  /* layout */
  --sidebar-height: 100vh;
  --sidebar-width: 16rem;
  --bg-color: #f5f5f5;
  --header-height: 48px;
  --logo-height: 80px;
  --sidebar-collapsed-width: 60px;
}

[data-theme='dark'] {
  --neutral-bg-color: var(--color-dark-neutral-background);
  --normal-bg-color: var(--color-dark-background);
  --normal-border-color: var(--color-dark-border);
  --normal-text-color: var(--color-dark-text);
  --disable-bg-color: var(--color-dark-disable-background);
  --disable-border-color: var(--color-dark-disable-border);
  --disable-text-color: var(--color-dark-disable-text);
  --divide-bg-color: var(--color-dark-divide);
  --scroll-bar-bg: var(--color-dark-bar-color);
}

*:focus-visible {
  outline: none;
}

::-webkit-scrollbar {
  width: 8px;
  /* 滚动条宽度 */
  height: 8px;
  /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: transparent;
  /* 滚动条轨道背景色 */
  border-radius: 10px;
  /* 圆角 */
}

::-webkit-scrollbar-thumb {
  /* background: var(--primary-color); 滚动条颜色 */
  background: var(--scroll-bar-bg);
  /* 滚动条颜色 肖羽更改 */
  border-radius: 10px;
  /* 圆角 */
  cursor: pointer;
}

html,
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

@media print {

  *,
  *::before,
  *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }

  @page {
    margin: 0;
    size: 1100px 1556px;
  }

  a:not(.btn) {
    text-decoration: underline;
  }

  abbr[title]::after {
    content: ' ('attr(title) ')';
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }

  body {
    -webkit-print-color-adjust: exact;
  }
}

@layer utilities {
  .spin-button-none {

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }

    -moz-appearance: textfield;
  }
}

.letter-text {
  letter-spacing: -1px;
}

.one-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.two-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
}

.three-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  display: -webkit-box;
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
}

/*伪元素是行内元素 正常浏览器清除浮动方法*/
.clearfix::after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/*ie6清除浮动的方式 *号只有IE6-IE7执行，其他浏览器不执行*/
.clearfix {
  zoom: 1;
}

/* search */
/* .anticon-search {
  margin-bottom: 7px;
} */

/* button */
.ant-btn {
  border-radius: 4px !important;
  -webkit-border-radius: 4px !important;
  -moz-border-radius: 4px !important;
  -ms-border-radius: 4px !important;
  -o-border-radius: 4px !important;
}

.ant-btn>span {
  display: inline-flex;
}

/* card */
.ant-card {
  box-shadow: none;
}

/* Modal */
.reject-modal .ant-modal .ant-modal-content {
  border-radius: 10px !important;
}


.ant-modal-confirm-body {
  display: flex;
  flex-direction: column;
  align-items: normal !important;
}

.ant-modal-confirm-title {
  padding: 10px 15px;
}

.ant-modal-confirm-content {
  width: 100%;
  max-width: 100% !important;
}

.ant-modal-confirm-btns {
  padding: 10px 15px;
}

.ant-modal-content {
  border-radius: 10px !important;
  overflow: hidden;
  -webkit-border-radius: 10px !important;
  -moz-border-radius: 10px !important;
  -ms-border-radius: 10px !important;
  -o-border-radius: 10px !important;
}

.ant-input-affix-wrapper {
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
}

/* 日期选择器 选择后的时间范围内 背景色 */
:where(.css-dev-only-do-not-override-cceck0).ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
  background: #e9f5f3;
}

:where(.css-dev-only-do-not-override-cceck0).ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before, :where(.css-dev-only-do-not-override-cceck0).ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
  background: #e9f5f3;
}