// stores/regionStore.ts
import { defineStore } from 'pinia';

export const useRegionStore = defineStore('regionStore', {
  state: () => ({
    currentLevel: 'region',  // 当前层级，'region' 为分区层级
    previousLevel: '',       // 上一个层级
    selectedRegion: '',      // 当前选中的分区
    selectedProvinces: [] as string[],  // 当前分区下的省份
  }),
  actions: {
    setCurrentLevel(store: any,level: string) {
      store.previousLevel = store.currentLevel; // 保存当前层级为上一个层级
      store.currentLevel = level;               // 更新为新层级
    },
    setSelectedRegion(store: any,region: string, provinces: string[]) {
      store.selectedRegion = region
      store.selectedProvinces = provinces;
    },
    resetToPreviousLevel(store: any) {
      store.currentLevel = store.previousLevel;  // 恢复到上一个层级
    },
  },
});