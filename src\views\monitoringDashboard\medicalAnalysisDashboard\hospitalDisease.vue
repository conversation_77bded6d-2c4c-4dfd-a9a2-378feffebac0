<template>
    <div class="containerThree" :style="{ width, height }">
      <!-- 返回按钮 -->
      <a-button v-if="showBackButton" @click="handleReturn" class="back-button">
        <template #icon>
          <LeftOutlined />
        </template>
        返回
      </a-button>
      
      <!-- 标题 -->
      <div class="selectText">
        <span class="textTitle">
          <span v-if="!showBackButton">高原病患者就诊医院分布</span>
          <span v-if="showBackButton">{{ diseaseOptionsType }} / 科室分布</span>
        </span>
      </div>
  
      <!-- 选择器 -->
      <a-select
        style="width: 180px"
        v-model:value="selectedDiseaseType"
        :options="diseaseOptions"
        class="selectType"
        @change="handleselectedDiseaseType"
      />
  
      <!-- 柱状图容器 -->
      <div class="chart-styles" v-if="!isData">
        <div id="drilldownBarChart" class="chart"></div>
      </div>
      
      <!-- 无数据展示 -->
      <div class="chart-styles no-data-available" v-else>
        <svg-icon name="noDataAvailable" width="121" height="130"></svg-icon>
        <div>暂无数据</div>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, onMounted, watch, getCurrentInstance, nextTick, onBeforeUnmount } from "vue";
  import { LeftOutlined } from "@ant-design/icons-vue";
  import * as echarts from "echarts";
  import { getHospitalDistribution, getDisease } from "./index.api.ts";
  
  const { proxy }: any = getCurrentInstance();
  
  // 定义数据类型
  interface ChartData {
    name: string;
    value: number;
  }
  
  // Props
  const props = defineProps({
    getMomentRangTimes: { type: Array, required: true },
    width: { type: String, default: "100%" },
    height: { type: String, default: "100%" }
  });
  
  // 变量定义
  const selectedDiseaseType = ref<any>("全部");
  const diseaseOptions = ref<any>([{ label: "全部", value: "全部" }]);
  const drillHospitals = ref<any>();
  const dateRangeTime = ref<any>(props.getMomentRangTimes);
  const diseaseOptionsType = ref<any>();
  
  const chartData = ref<ChartData[]>([]);
  const parentData = ref<ChartData[]>([]);
  
  const isDrilledDown = ref(false); // 下钻状态
  const showBackButton = ref(false); // 返回按钮
  const isData = ref(true); // 是否有数据
  
  let chartInstance: echarts.ECharts;
  
  // 选择器事件
  const handleselectedDiseaseType = async (value: any) => {
    selectedDiseaseType.value = value;
    getSortInfectiousTypeInfectiousDisease(dateRangeTime.value);
  };
  
  // 监听日期变化
  watch(
    () => props.getMomentRangTimes,
    (newVal: any[]) => {
      dateRangeTime.value = newVal;
      getSortInfectiousTypeInfectiousDisease(dateRangeTime.value);
      getHospitalsData(dateRangeTime.value);
    },
    { deep: true }
  );
  
  // 获取数据
  const getSortInfectiousTypeInfectiousDisease = async (value: any[]) => {
    let params = {
      from: value[0] || "",
      to: value[1] || "",
      disease: selectedDiseaseType.value === "全部" ? "" : selectedDiseaseType.value,
      hospital: drillHospitals.value
    };
  
    chartData.value = [];
    getHospitalDistribution(params).then((res: any) => {
      if (res.code === 0) {
        if(res.data.altDiseaseDistribution&&res.data.altDiseaseDistribution.length>0){
            res.data.altDiseaseDistribution.sort(function (a: { cnt: number }, b: { cnt: number }) {
            return b.cnt - a.cnt
            })
            chartData.value = res.data.altDiseaseDistribution.map((item: { key: string; cnt: any }) => ({
                name: item.key,
                value: item.cnt
                }));
        }else{
            chartData.value=[]
        }
       
        
  
        // 判断是否有数据
        isData.value = chartData.value.reduce((sum, item) => sum + item.value, 0) === 0;
  
        // 渲染图表
        nextTick(() => {
          initChart();
        });
      } else {
        proxy.$message.error(res?.errorMsg);
      }
    });
  };
  
  // 初始化柱状图
  const initChart = () => {
    const chartDom = document.getElementById("drilldownBarChart");
    if (!chartDom) return;
  
    chartInstance = echarts.init(chartDom);
  
    const option = {
    //   tooltip: { trigger: "axis" },
    //   xAxis: { type: "category", data: chartData.value.map((item) => item.name) },
    //   yAxis: { type: "value" },
    //   series: [
    //     {
    //       type: "bar",
    //       data: chartData.value.map((item) => item.value),
    //       itemStyle: { color: "#13A89B" }
    //     }
    //   ]
    title: {
      textStyle: {
        //文字颜色
        color: '#07123C',
        fontWeight: 'bold',
        //字体系列
        fontFamily: 'sans-serif',
        //字体大小
        fontSize: 18
      }
    },
    color: ['#13A89B', '#2316AB', '#405BC8', '#CCF56A', '#14705E', '#00CFBE', '#098BEA', '#079A35', '#9DD962'],
    barWidth: 20,

    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map((item) => item.name),
      axisTick: {
        show: false
      },
      axisLabel: {
        // 轴文字
        show: true,
        color: '#A6AAB2',
        fontSize: 12,
        interval: 0,
        rotate: 20
      },
      axisLine: {
        lineStyle: {
          type: 'solid',
          color: '#E6E6E8',
          width: '1'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        // 轴文字
        show: true,
        color: '#A6AAB2',
        fontSize: 12
      }
      // max:99999//给y轴设置最大值
    },
    series: [
      {
        type: 'bar',
        data: chartData.value.map((item) => item.value),
        // barGap:'80%',/*多个并排柱子设置柱子之间的间距*/
        // barCategoryGap:'50%',/*多个并排柱子设置柱子之间的间距*/
        itemStyle: {
          //柱状图上方显示数值
          normal: {
            color: '#405BC8',
            label: {
              show: true, //开启显示
              position: 'top', //在上方显示
              textStyle: {
                //数值样式
                color: '#A7ABB3',
                fontSize: 12
              }
            }
          }
        },

        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }
    ],
    dataZoom: [
      {
        type: 'slider', //给x轴设置滚动条
        show: false, //flase直接隐藏图形
        xAxisIndex: [0],
        bottom: 0,
        height: 10,
        showDetail: false,
        startValue: 0, //滚动条的起始位置
        endValue: 9 //滚动条的截止位置（按比例分割你的柱状图x轴长度）
      },
      {
        type: 'inside', //设置鼠标滚轮缩放
        show: false,
        xAxisIndex: [0],
        startValue: 0,
        endValue: 9
      }
    ]
    };
  
    chartInstance.setOption(option);
  
    // 点击柱状图事件
    chartInstance.on("click", async (params) => {
      if (isDrilledDown.value) return;
  
      diseaseOptionsType.value = params.name;
      parentData.value = [...chartData.value];
      drillHospitals.value = params.name;
  
      getSortInfectiousTypeInfectiousDisease(dateRangeTime.value);
      isDrilledDown.value = true;
      showBackButton.value = true;
    });
  };
  
  // 返回按钮
  const handleReturn = () => {
    chartData.value = parentData.value;
    isDrilledDown.value = false;
    showBackButton.value = false;
    drillHospitals.value = "";
    getSortInfectiousTypeInfectiousDisease(dateRangeTime.value);
  };
  
  // 处理窗口变化
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };
  
  // 获取疾病下拉数据
  const getHospitalsData = async (value: any) => {
    let params = { from: value[0] || "", to: value[1] || "" };
    // diseaseOptions.value = [{ label: "全部", value: "全部" }];
  
    await getDisease(params)
      .then((res) => {
        if (res.code === 0) {
          res.data.forEach((item:any) => {
            diseaseOptions.value.push({ label: item, value: item });
          });

          console.log(diseaseOptions.value,'diseaseOptions.value')
        }
      })
      .catch((err) => {
        console.log(err, "err");
      });
  };
  
  // 组件挂载
  onMounted(() => {
    getSortInfectiousTypeInfectiousDisease(dateRangeTime.value);
    getHospitalsData(dateRangeTime.value);
    window.addEventListener("resize", handleResize);
  });
  
  // 组件卸载
  onBeforeUnmount(() => {
    if (chartInstance) {
      chartInstance.dispose();
    }
    window.removeEventListener("resize", handleResize);
  });
  </script>
  
  <style scoped>
  .containerThree {
    width: 100%;
    height: 100%;
    position: relative;
  }
  
  .selectType {
    position: absolute;
    top: 0;
    right: 20px;
  }
  
  .chart {
    width: 100%;
    height: 100%;
  }
  
  .selectText {
    margin-left: 28px;
    /* float: left; */
  }
  
  .textTitle {
    font-size: 16px;
    font-weight: bold;
    color: #07123c;
  }
  
  .back-button {
    float: left;
    margin:0 16px;
    /* padding: 5px 12px; */
    border-radius: 4px;
  }
  
  .chart-styles {
    width: 100%;
    height: 100%;
  }
  
  .no-data-available {
    width: 100%;
    height: calc(100% - 60px);
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    color: #5e6580;
  }
  </style>
  