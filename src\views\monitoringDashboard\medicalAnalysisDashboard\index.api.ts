import request from '@/utils/request.ts'

// 获取折线图
export function getTrending(query: any) {
  return request({
    url: '/altVisitDashBoard/trending',
    method: 'get',
    params: query
  })
}

// 获取下拉数据
export function getHospitals(query: any) {
  return request({
    url: '/altVisitDashBoard/hospitals',
    method: 'get',
    params: query
  })
}

// 获取柱状图
export function getAmount(query: any) {
  return request({
    url: '/altVisitDashBoard/amount',
    method: 'get',
    params: query
  })
}

// 获取饼图
// 医院分布
export function getHospitalDistribution(query: any) {
  return request({
    url: '/altVisitDashBoard/hospital/distribution',
    method: 'get',
    params: query
  })
}
// 科室分布
export function getDepartmentDistribution(query: any) {
  return request({
    url: '/altVisitDashBoard/department/distribution',
    method: 'get',
    params: query
  })
}

// 获取下拉数据
export function getDisease(query: any) {
  return request({
    url: '/altVisitDashBoard/diseases',
    method: 'get',
    params: query
  })
}
