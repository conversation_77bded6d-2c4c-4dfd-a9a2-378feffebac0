<template>
  <div style="">
    <a-card title="角色详情" :bordered="false" :bodyStyle="{ padding: '24px', minHeight: 'calc(100vh - 180px)' }">
      <template #extra>
        <a-button style="float: right" @click="backListButton"> 返回 </a-button>
        <a-button style="float: right;margin-right: 15px;" @click="handleEditOrSave" v-if="state.disabled">
          编辑
        </a-button>
        <a-button style="float: right;margin-right: 15px;" type="primary" @click="handleEditOrSave"
          v-if="!state.disabled">
          保存
        </a-button>
      </template>
      <table-list ref="myTable" :tableData="state.tableList" :tableProps="state.tableProps" :total="state.total"
        :isPagination="false">
        <template #status="{ record }">
          <a-checkbox :name="record.name" :value="record.code" v-model:checked="record.checked"
            :disabled="state.disabled" @change="handleChange"></a-checkbox>
        </template>
      </table-list>
    </a-card>
  </div>
</template>

<script lang='ts' setup>
  import { onMounted, reactive } from 'vue'
  import TableList from "@/components/TableList/index.vue";
  import { getRolesPermission, setRolePermissions, getPermission } from './index.api'
  import { useRouter, useRoute } from 'vue-router'
  import _ from 'lodash'
  import { message } from 'ant-design-vue'
  const router = useRouter()
  const route = useRoute()

  // data
  const state = reactive({
    pageNation: {
      pageNum: 1,
      pageSize: 10
    },
    rolesPermission: [],
    tableList: [],
    tableProps: [
      {
        id: 1,
        title: "产品",
        dataIndex: "appCode",
        key: "appCode",
        ellipsis: true,
        width: 10,
        maxWidth: 10,
        customCell: (_, index) => {
          return {
            style: { verticalAlign: 'top' },
            rowSpan: index === 0 ? state.tableList?.length : 0,
          }
        },
      },
      {
        id: 2,
        title: "已启用",
        dataIndex: "status",
        key: "status",
        ellipsis: true,
        slot: 'status',
        hideTooltip: true,
        width: 20,
        maxWidth: 20,
      },
      {
        id: 3,
        title: "功能权限",
        dataIndex: "name",
        key: "name",
        ellipsis: true,
      },
      {
        id: 4,
        title: "描述",
        dataIndex: "description",
        key: "description",
        ellipsis: true,
      },
    ],
    total: 0,
    disabled: true,
    selected: [],

  })

  const backListButton = () => {
    router.go(-1);
  };

  const handleEditOrSave = async () => {
    if (state.disabled) {
      state.selected = []
      state.disabled = false
    } else {
      console.log(state.selected)
      let newArr = _.uniq(state.selected)
      let query = route.query
      let params = {
        appCode: '',
        roleCode: '',
        ...query,
      }
      await setRolePermissions(params, newArr)

      message.success('保存成功！');
      state.selected = []
      state.disabled = true
    }
  }

  const handleChange = (checked: Event) => {
    state.selected = state.tableList.filter((item) => item.checked).map((item) => item.code)
  }

  async function getPageList() {
    const params = {
      appCode: "ALT",
      roleCode: '',
      ...route.query
    };

    try {
      const res = await getRolesPermission(params);
      if (res.code == 200 && res.data && res.data.length > 0) {
        state.rolesPermission = res.data.map(item => ({
          ...item,
          checked: true,
          disabled: false,
        }));
      } else {
        state.rolesPermission = []; // 没有数据时，直接赋空数组
      }
    } catch (err) {
      console.error('获取角色权限失败:', err);
    }
  }

  async function getPermissionList() {
    const params = { appCode: "ALT" };

    try {
      const res = await getPermission(params);
      if (res?.code == 200 && res.data && res.data.length > 0) {
        state.tableList = res.data.map(itemData => {
          // 从 rolesPermission 中查找匹配的权限项
          const permission = state.rolesPermission.find(p => p.code == itemData.code);
          return {
            ...itemData,
            checked: !!permission, // 如果权限项存在，checked 为 true，否则为 false
          };
        });
      } else {
        state.tableList = []; // 没有数据时，直接赋空数组
      }
    } catch (err) {
      console.error('获取权限列表失败:', err);
    }
  }

  async function query() {
    await getPageList()
    await getPermissionList()
  }

  onMounted(() => {
    query()
  })
</script>

<style scoped lang='less'></style>