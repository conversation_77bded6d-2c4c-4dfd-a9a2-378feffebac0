import Docxtemplater from 'docxtemplater' // 文件模板修改工具
import PizZ<PERSON> from 'pizzip' // 文件读取工具
import PizZipUtils from 'pizzip/utils/index.js'
// @ts-ignore
import ImageModule from 'docxtemplater-image-module-free' // 免费转换图片工具
// @ts-ignore
import { saveAs } from 'file-saver' // 下载工具
import dayjs from 'dayjs' // 日期处理工具
import { message } from 'ant-design-vue'

interface ExportWordOptions {
  url: string
  filename: string
  obj: any // 如果你知道 obj 的具体类型，可以在这里替换 any
}

/**
 * @param {string} url 模板文件路径
 * @param {string} filename 下载文件名
 * @param {any} data 模板变量对象
 */
class ExportWord {
  public url: string
  public fileName: string = `导出日期${dayjs().format('YYYY-MM-DD')}+路君疫情动态.docx`
  public data: any
  public docContent: Docxtemplater | null = null

  constructor(option: ExportWordOptions) {
    this.url = option.url
    this.fileName = option.filename
    this.data = option.obj
  }

  // initWord
  initWord(_callback: Function) {
    let _this = this
    _this.loadFile(_this.url, (error: Error | null, _content) => {
      _this
        .setPizZip(error, _content)
        .then(() => {
          _callback(_this)
        })
        .catch(() => {
          // alert('加载模板文件失败！')
          message.error('加载模板文件失败！')
        })
    })
  }

  // 读取并获得模板文件的二进制内容
  loadFile(url: string, callback: (error: any, content: any) => void) {
    PizZipUtils.getBinaryContent(url, callback)
  }

  // 创建一个JSZip实例，内容为模板的内容
  setPizZip(error: Error | null, _content: any) {
    let _this = this
    if (error) {
      throw error
    }

    const imageOpts = {
      centered: false,
      getImage: function (tagValue, tagName) {
        return new Promise(function (resolve, reject) {
          PizZipUtils.getBinaryContent(tagValue, function (error, content) {
            if (error) {
              return reject(error)
            }
            return resolve(content)
          })
        })
      },
      getSize: (img: HTMLImageElement, tagValue: string, tagName: string) => {
        return new Promise(function (resolve, reject) {
          const image = new Image()
          image.src = tagValue
          image.onload = function () {
            let width = image.width / 2 || 200
            let height = image.height / 2 || 200
            resolve([width, height])
          }
          image.onerror = function (e) {
            console.log('img, tagValue, tagName : ', img, tagValue, tagName)
            // alert('An error occurred while loading ' + tagValue)
            message.error('An error occurred while loading' + tagValue)
            reject(e)
          }
        })
      }
    }

    let imageModule = new ImageModule(imageOpts)
    const zip: PizZip = new PizZip(_content)
    _this.docContent = new Docxtemplater(zip, {
      modules: [imageModule],
      paragraphLoop: true,
      linebreaks: true
    })

    return _this.setTemplateContent(_this.data)
  }

  // 设置新模板内容
  setTemplateContent(_obj: any) {
    try {
      // 用模板变量的值替换所有模板变量
      return this.docContent!.renderAsync(_obj)
    } catch (error: any) {
      function replaceErrors(key: any, value: any) {
        if (value instanceof Error) {
          return Object.getOwnPropertyNames(value).reduce(function (error: any, key: string) {
            error[key] = value[key as keyof Error]
            return error
          }, {})
        }
        return value
      }
      console.log(
        JSON.stringify(
          {
            error: error
          },
          replaceErrors
        )
      )

      if (error.properties && error.properties.errors instanceof Array) {
        const errorMessages = error.properties.errors
          .map(function (error: any) {
            return error.properties.explanation
          })
          .join('\n')
        console.log('errorMessages', errorMessages)
        // errorMessages is a humanly readable message looking like this : 'The tag beginning with "foobar" is unopened'
      }
      throw error
    }
  }

  // 下载文件
  downloadFile() {
    let _this = this
    const out = _this.docContent!.getZip().generate({
      type: 'blob',
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
    saveAs(out, _this.fileName)
  }
}

export default ExportWord

const base64DataURLToArrayBuffer = (dataURL) => {
  const base64Regex = /^data:image\/(png|jpg|svg|svg\+xml);base64,/
  if (!base64Regex.test(dataURL)) {
    return false
  }
  const stringBase64 = dataURL.replace(base64Regex, '')
  let binaryString
  if (typeof window !== 'undefined') {
    binaryString = window.atob(stringBase64)
  } else {
    binaryString = new Buffer(stringBase64, 'base64').toString('binary')
  }
  const len = binaryString.length
  const bytes = new Uint8Array(len)
  for (let i = 0; i < len; i++) {
    const ascii = binaryString.charCodeAt(i)
    bytes[i] = ascii
  }
  return bytes.buffer
}
