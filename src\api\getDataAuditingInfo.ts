import request from '@/utils/request.ts'

// 字典
export function getDicList(params: { type: string }) {
  return request({
    url: '/visitDict/listAll',
    method: 'get',
    params
  })
}

// 分页查询
export function getListPage(params: { status: string; description: string; name: string; current: number; size: number }) {
  return request({
    url: '/altKnowledgeGuide/listPage',
    method: 'get',
    params
  })
}
// 下载文件
export function getPdfFile(query: any) {
  return request({
    url: '/file/download',
    method: 'get',
    params: query,
    // headers: {
    //   Accept: 'application/octet-stream', // 请求文件流
    // },
    responseType: 'blob'
  })
}
// 修改
export function updatePage(params: any) {
  return request({
    url: '/altKnowledgeGuide/update',
    method: 'post',
    data: params
  })
}

// 获取知识库树形数据
export function getKnowledgeNodeTree(params: any) {
  return request({
    url: '/altKnowledgeNode/selectTreeStructure',
    method: 'get',
    params: params
  })
}

// 新增数据
export function addKnowledgeGuide(params: any) {
  return request({
    url: '/altKnowledgeGuide/add',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
