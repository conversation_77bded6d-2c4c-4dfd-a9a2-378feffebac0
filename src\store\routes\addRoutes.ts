const pages = import.meta.glob('@/views/**/**.vue')

import router from '@/router/index'
import type { RouteRecord } from 'vue-router'
import { useUserStore } from './user'
import { toRaw } from 'vue'

export interface LoginInfo {
  id?: Number
  title: String
  icon: String
  path?: String
  name: String
  component: String
  children?: []
}

// 提取判断是否有权限的逻辑
interface PermissionCheckResult {
  permissionCodes: string[]
  permissions: string | undefined
  hidden: boolean | undefined
}
function hasPermission(options: PermissionCheckResult): boolean {
  const { permissionCodes, permissions, hidden } = options
  // 如果菜单有权限并且是隐藏的，则直接返回hidden字段
  if (permissions && hidden) {
    return hidden ? false : true;
  }

  // 如果菜单有权限要求，判断用户是否有该权限
  if (permissions) {
    return permissionCodes.includes(permissions);
  }

  // 如果没有权限要求，则判断 hidden 字段，如果为 true 则没有权限
  return hidden ? false : true;
}

// 提取更新菜单项meta的逻辑
function updateMenuMeta(menu: any, hasPermission: boolean): void {
  menu.meta = {
    ...menu.meta,
    hidden: !hasPermission,  // 如果没有权限，则隐藏该菜单
  };
}

// 提取添加子路由的逻辑
function addChildRoute(child: any, menu: any): void {
  router.addRoute('index', {
    ...child,
    name: child?.name,
    path: child?.path,
    meta: {
      ...child?.meta,
      icon: child?.meta?.icon,
      title: child?.meta?.title,
      hidden: child?.meta?.hidden || false,
      fatherTitle: menu?.meta?.title,
      fatherPath: menu?.children[0]?.path,
      permissions: menu?.meta?.permissions,
    },
    // component: child?.component,  // 采用懒加载方案
    component: pages[`/src/views/${child?.component}.vue`],//采用动态路由方案
  });
}

// 主函数优化
export function addRoute(list: any[]) {
  if (list.length == 0) return [];

  const userStore = useUserStore();
  const permissionCodes = toRaw(userStore.permList) || [];

  list.forEach((menu: { children: any[] }) => {
    let menuOptions = { permissionCodes: permissionCodes, permissions: menu?.meta?.permissions, hidden: menu?.meta?.hidden }
    const menuHasPermission = hasPermission(menuOptions);
    updateMenuMeta(menu, menuHasPermission);

    if (menu?.children) {
      menu.children.forEach((child: any) => {
        let options = { permissionCodes: permissionCodes, permissions: child?.meta?.permissions, hidden: child?.meta?.hidden }
        const hasPermissionForChild = hasPermission(options);
        updateMenuMeta(child, hasPermissionForChild);

        // 如果子菜单有权限，才添加路由
        if (!child?.component) {
          return;
        }
        addChildRoute(child, menu);

        // 递归处理子菜单
        if (child?.children && child?.children?.length > 0) {
          addRoute(child?.children);
        }
      });
    }
  });

  // console.log(router.getRoutes());
}

export function removeRoute() {
  router.getRoutes().forEach((v: RouteRecord) => {
    if (v.meta.dynamic) {
      router.removeRoute(v.name as string)
    }
  })
}
