<template>
  <div class="my-charts">
    <div
      class="my-charts-box"
      v-if="isData"
    >
      <MixBasicChart
        :option="options"
        @up="handleClick"
      />
    </div>
    <div
      class="no-data-available"
      v-else
    >
      <svg-icon
        name="noDataAvailable"
        width="121"
        height="130"
      ></svg-icon>
      <div>暂无数据</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import MixBasicChart from '@/components/charts/MixBasicChart.vue'
import { defineProps, ref, watch } from 'vue'

const options = ref<any>({})
const selectedThree = ref<any>([])

const { data, name,count } = defineProps({
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  name: {
    type: String,
    default: ''
  },
  count:{
    type: String,
    default: ''
  },
})

const initCharts = (dataList: any[]) => {
  options.value = {
    title: {
      text: name,
      // 副标题
      subtext: count,
      // 主副标题间距
      itemGap: 40,
      x: 'center',
      y: 'center',
      top: '280',
      // 主标题样式
      textStyle: {
        fontSize: '20',
        color: 'black'
      },
      // 副标题样式
      subtextStyle: {
        fontSize: '20',
        fontWeight: '800',
        color: 'black'
      }
    },
    color: ['#13A89B', '#2316AB', '#405BC8', '#CCF56A', '#14705E', '#00CFBE', '#098BEA', '#079A35', '#9DD962'],
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c}'
    },
    legend: {
      // orient: 'vertical',
      x: 'center',
      y: 'bottom',
      type: 'scroll', // 数据过多时，分页显示
      selected: selectedThree.value //这里默认显示数组中前十个，如果不设置，则所有的数据都会显示在图表上
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '4%',
      containLabel: true
    },
    series: [
      {
        type: 'pie',
        data: dataList.map((item) => ({
          ...item,
          label: {
            show: item.value > 0 // 数据值为 0 时不显示标签
          },
          labelLine: {
            show: item.value > 0 // 数据值为 0 时不显示指引线
          }
        })),
        label: {
          show: true,
          formatter: '{b}: {c}'
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 10,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true,
          length: 10, // 第一段指导线 长度
          length2: 10 // 第二段指导线 长度
        },
        name: name,
        center: ['50%', '45%'],
        radius: ['50%', '70%'],
        avoidLabelOverlap: false
      }
    ]
  }
}
const isData = ref(true)
watch(
  () => data,
  (newVal) => {
    isData.value = newVal?.some((item: any) => item.value > 0)
    initCharts(newVal)
  },
  { deep: true, immediate: true }
)

const handleClick = (e: any) => {
  console.log(e, '点击的数据')
}
</script>

<style lang="less" scoped>
.my-charts {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .my-charts-box {
    width: 100%;
    height: 100%;
  }

  .no-data-available {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    color: #5e6580;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
}
</style>
