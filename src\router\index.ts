import { createRouter, createWebHistory } from 'vue-router'
import { Logger } from '@/utils/consoleLog'
// 引入 nprogress 相关方法
import { close, start } from '@/utils/nprogress'
import { config, setConfig } from '@/config/index'
import { useUserStore } from '@/store/routes/user'
import { message } from 'ant-design-vue'
import { toRaw } from 'vue'

// 专病库专用页面
const aircraftCarrie = [
  {
    path: '/aircraftCarrie',
    name: 'aircraftCarrie',
    redirect: '/aircraftCarrie/monitorReport',
    meta: {
      title: '四合一',
      icon: '',
      hidden: true,
      notDetect: true // 不检测登录状态
    },
    children: [
      {
        path: '/aircraftCarrie/monitorReport',
        name: 'aircraftCarrieMonitorReport',
        component: () => import('@/views//aircraftCarrie/monitorReport/index.vue'),
        meta: {
          title: '监测报告',
          icon: '',
          hidden: true,
          notDetect: true // 不检测登录状态
        }
      },
      {
        path: '/aircraftCarrie/earlyWarningAnalysis',
        name: 'aircraftCarrieEarlyWarningAnalysis',
        component: () => import('@/views//aircraftCarrie/earlyWarningAnalysis/index.vue'),
        meta: {
          title: '疾病预警分析',
          icon: '',
          hidden: true,
          notDetect: true // 不检测登录状态
        }
      },
      {
        path: '/aircraftCarrie/trendAnalysis',
        name: 'aircraftCarrieTrendAnalysis',
        component: () => import('@/views//aircraftCarrie/trendAnalysis/index.vue'),
        meta: {
          title: '疾病趋势分析',
          icon: '',
          hidden: true,
          notDetect: true // 不检测登录状态
        }
      },
      {
        path: '/aircraftCarrie/knowledgeChart',
        name: 'aircraftCarrieKnowledgeChart',
        meta: {
          title: '知识图谱',
          icon: '',
          hidden: true,
          notDetect: true // 不检测登录状态
        }
      }
    ]
  }
]

export const routes = [
  {
    path: '/',
    name: '',
    redirect: '/home',
    meta: {
      hidden: true
    }
  },
  {
    path: '/home',
    name: 'index',
    // component: Layout,
    component: () => import('@/components/Layout/index.vue'),
    redirect: '/contagiousDiseaseDashboard/GeographicInfoAnalysis',
    meta: {
      title: '首页',
      icon: 'vue',
      hidden: true
    },
    children: [
      {
        path: '/contagiousDiseaseDashboard/GeographicInfoAnalysis',
        name: 'GeographicInfoAnalysis',
        meta: {
          title: '高原病发病地理信息',
          icon: 'geographicInformation',
          permissions: 'ALT_GIS_MAP'
        },
        component: () => import('@/views/contagiousDiseaseDashboard/GeographicInfoAnalysis.vue')
      }
    ]
  },
  ...aircraftCarrie,
  // {
  //   path: '/Login',
  //   name: 'login',
  //   component: () => import('@/views/login/login.vue'),
  //   meta: {
  //     notDetect: false // 不检测登录状态
  //   }
  // },

  {
    path: '/404',
    component: () => import('@/views/error/404.vue'),
    meta: {
      notDetect: true
    }
  },
  {
    path: '/403',
    component: () => import('@/views/error/403.vue'),
    meta: {
      notDetect: false,
      hidden: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    meta: {
      notDetect: true
    }
  }
]

export const mockRoutes = [
  {
    path: '/contagiousDiseaseDashboard',
    name: 'contagiousDiseaseDashboard',
    component: '',
    meta: {
      title: '高原病发病地理信息',
      icon: 'geographicInformation',
      permissions: 'ALT_GIS_MAP'
    },
    children: [
      {
        path: '/contagiousDiseaseDashboard/GeographicInfoAnalysis',
        name: 'GeographicInfoAnalysis',
        meta: {
          title: '高原病发病地理信息',
          icon: 'geographicInformation',
          permissions: 'ALT_GIS_MAP'
        },
        component: 'contagiousDiseaseDashboard/GeographicInfoAnalysis'
      }
    ]
  },

  {
    path: '/monitoringDashboard',
    name: 'monitoringDashboard',
    component: '',
    meta: {
      title: '高原病监测分析',
      icon: 'instrumentPanel',
      permissions: 'ALT_DASHBOARD'
    },
    children: [
      {
        path: '/monitoringDashboard/diseaseAnalysisDashboard',
        name: 'diseaseAnalysisDashboard',
        meta: {
          title: '高原病发病分析',
          icon: 'diseaseAnalysisDashboard',
          permissions: 'ALT_DASHBOARD_DISEASE'
        },
        component: 'monitoringDashboard/diseaseAnalysisDashboard/index'
      },
      {
        path: '/monitoringDashboard/medicalAnalysisDashboard',
        name: 'medicalAnalysisDashboard',
        meta: {
          title: '高原病就诊分析',
          icon: 'medicalAnalysisDashboard',
          permissions: 'ALT_DASHBOARD_VISIT'
        },
        component: 'monitoringDashboard/medicalAnalysisDashboard/index'
      }
      // {
      //   path: '/monitoringDashboard/monitoringReports',
      //   name: 'monitoringReports',
      //   meta: {
      //     title: '高原病监测报告',
      //     icon: 'monitoringReports',
      //     // permissions: 'ALT_DASHBOARD_VISIT'
      //   },
      //   component: 'monitoringDashboard/monitoringReports/index'
      // }
    ]
  },
  {
    path: '/monitoringReports',
    name: 'monitoringReports',
    component: '',
    meta: {
      title: '高原病监测报告',
      icon: 'testReport',
      permissions: 'ALT_REPORT'
    },
    children: [
      {
        path: '/monitoringReports',
        name: 'monitoringReports',
        meta: {
          title: '高原病监测报告',
          icon: 'testReport',
          permissions: 'ALT_REPORT'
        },
        component: 'monitoringReports/index'
      }
    ]
  },
  {
    path: '/patientManagement',
    name: 'patientManagement',
    component: '',
    meta: {
      title: '高原病患者管理',
      icon: 'dataManagement',
      permissions: 'ALT_PATIENT'
    },
    children: [
      {
        path: '/patientManagement',
        name: 'patientManagement',
        meta: {
          title: '高原病患者管理',
          icon: 'dataManagement',
          permissions: 'ALT_PATIENT'
        },
        component: 'patientManagement/index'
      }
    ]
  },
  {
    path: '/knowledgeBase',
    name: 'knowledgeBase',
    meta: {
      title: '高原病知识库',
      icon: 'knowledgeBase',
      permissions: 'ALT_KNOWLEDGE'
    },
    component: '',
    children: [
      {
        path: '/knowledgeBase/knowledgeGraph',
        name: 'InfectiousDiseaseKnowledgeGraph',
        meta: {
          title: '高原病知识图谱',
          icon: 'InfectiousDiseaseKnowledgeGraph',
          permissions: 'ALT_KNOWLEDGE_GRAPH'
        },
        component: 'knowledgeBase/knowledgeGraph/index'
      },
      {
        path: '/knowledgeBase/knowledgeBaseList',
        name: 'KnowledgeBaseList',
        meta: {
          title: '高原病知识库列表',
          icon: 'KnowledgeBaseList',
          permissions: 'ALT_KNOWLEDGE_GUIDE'
        },
        component: 'knowledgeBase/knowledgeBaseList/index'
      },
      {
        path: '/knowledgeBase/knowledgeBaseAlt',
        name: 'knowledgeBaseAlt',
        meta: {
          title: '高原病病情预警分析',
          permissions: 'ALT_DASHBOARD_ALERT',
          icon: ''
        },
        component: 'knowledgeBase/knowledgeBaseAlt/index'
      },
      {
        path: '/knowledgeBase/IncidenceTrends',
        name: 'IncidenceTrends',
        meta: {
          title: '高原病发病趋势预测',
          permissions: 'ALT_TREND_PREDICTION',
          icon: ''
        },
        component: 'knowledgeBase/IncidenceTrends/index'
      }

      // {
      //   path: '/knowledgeBase/knowledgeStructure',
      //   name: 'knowledgeStructure',
      //   meta: {
      //     title: '高原病知识结构',
      //     // permissions: "INF_KNOWLEDGE_GUIDE",
      //     icon: ''
      //   },
      //   component: 'knowledgeBase/knowledgeStructure/index'
      // },
    ]
  },

  {
    path: '/dataAuditing',
    name: 'dataAuditing',
    meta: {
      title: '高原病数据审核管理',
      icon: 'auditManagement',
      permissions: 'ALT_AUDIT'
    },
    component: '',
    children: [
      // {
      //   path: '/dataAuditing/omissionAudit',
      //   name: 'omissionAudit',
      //   meta: {
      //     title: '高原病漏报审核',
      //     icon: 'omissionAudit',
      //     permissions: ""
      //   },
      //   component: 'dataAuditing/omissionAudit/index'
      // },
      // {
      //   path: '/dataAuditing/knowledgeBaseManagement',
      //   name: 'knowledgeBaseManagement',
      //   meta: {
      //     title: '高原病及公卫知识库管理',
      //     icon: 'knowledgeBaseManagement',
      //     permissions: ""
      //   },
      //   component: 'dataAuditing/knowledgeBaseManagement/index'
      // },

      {
        path: '/dataAuditing/knowledgeBaseManegement',
        name: 'KnowledgeBaseManegement',
        meta: {
          title: '高原病知识库管理',
          icon: 'KnowledgeBaseManegement',
          permissions: 'ALT_AUDIT_KNOWLEDGE_GUIDE'
        },
        component: 'dataAuditing/knowledgeBaseManegement/index'
      },
      {
        path: '/dataAuditing/knowledgeGraphManagement',
        name: 'knowledgeGraphManagement',
        meta: {
          title: '高原病知识图谱管理',
          icon: 'knowledgeGraphManagement',
          permissions: 'ALT_AUDIT_KNOWLEDGE_GRAPH'
        },
        component: 'dataAuditing/knowledgeGraphManagement/index'
      }
    ]
  },
  {
    path: '/platformManagement',
    name: 'platformManagement',
    meta: {
      title: '平台管理',
      icon: 'platformManagement',
      permissions: 'ALT_PLATFORM_MANAGEMENT'
    },
    component: '',
    children: [
      {
        path: '/platformManagement/platformUserAnalysis',
        name: 'platformUserAnalysis',
        meta: {
          title: '平台用户分析',
          icon: 'platformUserAnalysis',
          permissions: 'ALT_PLATFORM_USER_ANALYSIS'
        },
        component: 'platformManagement/platformUserAnalysis/index'
      },
      {
        path: '/platformManagement/userManagement',
        name: 'userManagement',
        meta: {
          title: '用户管理',
          icon: 'userManagement',
          permissions: 'ALT_USER_MANAGEMENT'
        },
        // component: () => import('@/views/platformManagement/userManagement/index.vue')
        component: 'platformManagement/userManagement/index'
      },
      {
        path: '/platformManagement/roleManagement',
        name: 'roleManagement',
        meta: {
          title: '角色管理',
          icon: 'roleManagement',
          permissions: 'ALT_ROLE_MANAGEMENT'
        },
        // component: () => import('@/views/platformManagement/roleManagement/index.vue')
        component: 'platformManagement/roleManagement/index'
      },
      {
        path: '/platformManagement/roleManagement/detail',
        name: 'roleManagementDetail',
        meta: {
          title: '角色详情',
          icon: 'roleManagementDetail',
          hidden: true
        },
        // component: () => import('@/views/platformManagement/roleManagement/detail.vue')
        component: 'platformManagement/roleManagement/detail'
      },
      {
        path: '/platformManagement/permissionsManagement',
        name: 'permissionsManagement',
        meta: {
          title: '数据权限管理',
          icon: 'permissionsManagement',
          permissions: 'ALT_DATA_MANAGEMENT'
        },
        component: 'platformManagement/permissionsManagement/index'
      },

      {
        path: '/platformManagement/operationLogs',
        name: 'operationLogs',
        meta: {
          title: '日志审计管理',
          icon: 'operationLogs',
          permissions: 'ALT_OPERATION_LOG'
        },
        component: 'platformManagement/operationLogs/index'
      },
      {
        path: '/platformManagement/monitoring',
        name: 'monitoring',
        meta: {
          title: '运维监控',
          icon: 'monitoring',
          permissions: 'ALT_MONITOR'
        },
        component: 'platformManagement/monitoring/index'
      },
      {
        path: '/platformManagement/personalCenter',
        name: 'personalCenter',
        meta: {
          title: '个人中心',
          icon: 'personalCenter',
          permissions: 'ALT_MY_CENTER',
          hidden: true
        },
        // component: () => import('@/views/platformManagement/personalCenter/index.vue')
        component: 'platformManagement/personalCenter/index'
      }
    ]
  }
]

// 路由白名单
const whiteList = [
  '/404',
  '/:pathMatch(.*)*',
  '/aircraftCarrie',
  '/aircraftCarrie/monitorReport',
  '/aircraftCarrie/earlyWarningAnalysis',
  '/aircraftCarrie/trendAnalysis',
  '/aircraftCarrie/knowledgeChart'
]

const router = createRouter({
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
  history: createWebHistory(import.meta.env.VITE_BASE_URL + '/'),
  routes
})

router.beforeEach((to, from, next) => {
  // 开启进度条
  start()
  // const meanStore = useMeanStore()
  let user = useUserStore()
  const userPermissions = toRaw(user.permList) // 从store获取用户权限
  const { token, service, expiresAt } = localStorage // 从localStorage获取token

  // 白名单路由或无需权限验证的路由，直接放行
  if (to.meta.notDetect || whiteList.includes(to.path)) {
    return next()
  }
  // 统一处理重定向登录
  const redirectToLogin = (msg: string) => {
    message.warn(msg)
    Logger.warn(msg)

    setTimeout(() => {
      user.remove()
      const redirectUrl = `${import.meta.env.MODE == 'development' ? 'http://***********' : window.location.origin}/akkare-platform/login?service=${config.APP_CODE}&redirectTo=${encodeURIComponent(config.REDIRECT_URI)}`
      window.location.href = redirectUrl
    }, 500)
  }

  // 处理MAR服务的特殊配置
  if (!token && to.path !== '/login' && service === 'ALT') {
    setConfig({
      APP_CODE: service,
      REDIRECT_URI: import.meta.env.MODE == 'production' ? `${window.location.origin}/ams-front/home?service=${service}` : `http://localhost:3004/?service=ALT`
    })
  }

  // 统一处理登录状态检查
  if (!token && to.path !== '/login') {
    redirectToLogin('您还未登录, 请登录')
    return
  }

  // 判断token是否过期
  if (!expiresAt || (expiresAt && expiresAt < Date.now() - 3000)) {
    redirectToLogin('登录已过期, 请重新登录')
    return
  }

  // 如果当前路由需要权限校验
  if (to.meta.permissions) {
    const requiredPermission = to.meta.permissions

    // 如果用户有权限，放行
    if (userPermissions.includes(requiredPermission)) {
      Logger.info('您有权限访问该页面')
      return next()
    }

    // 没有权限，跳转到 403 或显示警告
    message.warn('您没有权限访问该页面')
    Logger.warn('您没有权限访问该页面')
    return next('/403')
  }

  // 无权限要求的页面，正常放行
  return next()
})

router.afterEach(() => {
  // 关闭进度条
  close()
})
export default router
