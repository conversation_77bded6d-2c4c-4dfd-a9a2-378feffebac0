// @src/store/menus.ts
import { defineStore } from 'pinia'
// @ts-ignore
import { reactive, computed } from 'vue'

interface State {
  pdfDataUrl: string | null // 存储 Base64 编码的 PDF 数据
  checkedValues: { [tabKey: string]: string[] }
}

export const usePdfStore = defineStore('usePdf', () => {
  let state: State = reactive({
    pdfDataUrl: null,
    checkedValues: {}
  })

  const pdfDataUrl = computed(() => state.pdfDataUrl)
  const getCheckedValues = computed(() => (tabKey: string) => state.checkedValues[tabKey] || [])

  function setPdfDataUrl(dataUrl: string) {
    state.pdfDataUrl = dataUrl
  }

  function setCheckedValues(payload: { tabKey: string; checkedValues: string[] }) {
    state.checkedValues[payload.tabKey] = payload.checkedValues
  }

  function updateCheckboxState(payload: { tabKey: string; checkboxValue: string; isChecked: boolean }) {
    const { tabKey, checkboxValue, isChecked } = payload
    const checkedArray = state.checkedValues[tabKey] || []
    if (isChecked) {
      if (!checkedArray.includes(checkboxValue)) {
        checkedArray.push(checkboxValue)
      }
    } else {
      const index = checkedArray.indexOf(checkboxValue)
      if (index > -1) {
        checkedArray.splice(index, 1)
      }
    }
    state.checkedValues[tabKey] = checkedArray
  }

  async function fetchAndStorePdf(url: string) {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error('Network response was not ok')
      }
      const blob = await response.blob()
      const reader = new FileReader()
      reader.onloadend = () => {
        const dataUrl = reader.result as string
        setPdfDataUrl(dataUrl)
      }
      reader.readAsDataURL(blob)
    } catch (error) {
      console.error('Error fetching PDF:', error)
    }
  }

  return {
    state,
    setPdfDataUrl,
    pdfDataUrl,
    getCheckedValues,
    setCheckedValues,
    updateCheckboxState,
    fetchAndStorePdf
  }
})
