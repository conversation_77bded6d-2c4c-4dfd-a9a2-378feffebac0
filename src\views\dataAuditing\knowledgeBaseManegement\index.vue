<template>
  <div class="wrapper">
    <a-card
      title="高原病知识库管理"
      :bordered="false"
      :bodyStyle="{ padding: '24px', minHeight: 'calc(100vh - 180px)' }"
    >
      <div class="clearfix">
        <a-form
          :model="formData"
          layout="inline"
          class="form-data-inline clearfix"
        >
          <a-form-item label="状态">
            <a-select
              style="width: 240px"
              mode="multiple"
              v-model:value="formData.status"
              placeholder="请选择"
            >
              <a-select-option
                :value="item.code"
                v-for="item in statusList"
                :key="item.id"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="指南名称">
            <a-input
              style="width: 300px"
              v-model:value="formData.name"
              placeholder="请输入指南名称"
            />
          </a-form-item>
          <!-- <a-form-item label="指南简述">
            <a-input style="width: 300px;" v-model:value="formData.description" placeholder="请输入指南简述" />
          </a-form-item> -->

          <div class="float-right flex flex-1 flex-col w-70px btn-export">
            <a-button
              @click="handleClear"
              class="letter-text"
              style="margin-right: 16px"
              >清空</a-button
            >
            <a-button
              type="primary"
              class="letter-text"
              @click="handleSearch"
              >搜索</a-button
            >
          </div>
        </a-form>
      </div>

      <!-- <div class="flex justify-end upload-btn">
        <a-button type="primary" @click="handleUploadGuide">上传指南</a-button>
      </div> -->
      <div class="table">
        <table-list
          ref="myTable"
          :tableData="tableList"
          :tableProps="tableProps"
          :total="total"
          :attachButtons="attachButtons"
          @changePage="handleChangePage"
        >
          >
          <template #status="{ record }">
            <div>
              <a-tag
                style="border-radius: 4px"
                :color="getTagColor(record.status)"
              >
                {{ statusEnum[record.status] }}
              </a-tag>
              <a-tooltip v-if="record.status == 4">
                <template #title>
                  <div>
                    {{ record.reason }}
                  </div>
                </template>
                <InfoCircleOutlined
                  :style="{
                    color: '#FF4D4F',
                    fontSize: '16px'
                  }"
                />
              </a-tooltip>
            </div>
          </template>
        </table-list>
      </div>
    </a-card>

    <div>
      <a-drawer
        title="上传指南"
        width="450"
        :open="isOpenDrawer"
        @close="onClose"
        :closable="false"
        :footer-style="{ textAlign: 'right' }"
      >
        <template #extra>
          <span
            @click="onClose"
            style="cursor: pointer"
          >
            <CloseOutlined />
          </span>
        </template>
        <a-form
          ref="formRef"
          :model="drawerFormData"
          :rules="rules"
          :label-col="labelCol"
        >
          <a-form-item
            label="指南名称"
            name="name"
          >
            <a-input
              style="width: 100%"
              v-model:value="drawerFormData.name"
            />
          </a-form-item>
          <a-form-item
            label="指南简述"
            name="description"
          >
            <a-textarea
              style="width: 100% !important"
              v-model:value="drawerFormData.description"
            />
          </a-form-item>
          <a-form-item
            label="指南分类"
            name="nodeId"
          >
            <a-tree-select
              ref="selectTreeRef"
              v-model:value="drawerFormData.nodeId"
              v-model:searchValue="searchValue"
              show-search
              style="width: 100%"
              :dropdown-style="{ maxWidth: '100%', maxHeight: '500px', overflow: 'auto' }"
              placeholder="请选择"
              allow-clear
              tree-default-expand-all
              tree-line
              :tree-data="treeData"
              :fieldNames="propsLabel"
              tree-node-filter-prop="name"
            >
            </a-tree-select>
          </a-form-item>
          <a-form-item
            label="上传文件"
            name="file"
            extra="支持pdf格式"
          >
            <!--  accept= ".rar,.zip,.pdf,.jpg,.png,.docx" -->
            <a-upload
              accept=".pdf"
              :file-list="drawerFormData.file"
              :before-upload="beforeUpload"
              @remove="handleRemove"
              list-type="picture"
            >
              <a-button>
                <template #icon>
                  <UploadOutlined />
                </template>
                点击上传
              </a-button>
            </a-upload>
          </a-form-item>
        </a-form>
        <template #footer>
          <a-button
            style="margin-right: 20px"
            @click="onClose"
            >取消</a-button
          >
          <a-button
            type="primary"
            @click="onSubmit"
            >确定</a-button
          >
        </template>
      </a-drawer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, useTemplateRef, toRaw } from 'vue'
import type { UnwrapRef } from 'vue'
import TableList from '@/components/TableList/index.vue'
import { saveAs } from 'file-saver'
import _ from 'lodash'
import { Modal, message, type UploadProps } from 'ant-design-vue'
import { InfoCircleOutlined, UploadOutlined, CloseOutlined } from '@ant-design/icons-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { convertToPDF, statusEnum, rejectPopUpWindow } from './index.config'
import { getDicList, getListPage, getPdfFile, updatePage, getKnowledgeNodeTree, addKnowledgeGuide } from '@/api/getDataAuditingInfo'
import { operationLog } from '@/api/log'

let formData = reactive({
  description: '',
  status: [],
  name: ''
})
const tableList = ref([])
const tableProps = ref([
  {
    id: 1,
    title: '指南名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true
  },
  {
    id: 2,
    title: '指南简述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    id: 3,
    title: '指南分类',
    dataIndex: 'nodeName',
    key: 'nodeName',
    ellipsis: true
  },
  {
    id: 4,
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    ellipsis: true,
    slot: 'status',
    width: 30,
    hideTooltip: true
  },
  {
    id: 5,
    title: '更新者',
    dataIndex: 'creator',
    key: 'creator',
    width: 40,
    ellipsis: true
  },
  {
    id: 6,
    title: '更新时间',
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    width: 40,
    ellipsis: true
  },
  {
    id: 99,
    title: '操作',
    dataIndex: 'operation',
    slot: 'operation',
    width: 40,
    buttons: [
      {
        name: '查看',
        onclick: ({ record }) => {
          viewFile(record.storagePath)
        }
      },
      {
        name: '通过',
        onclick: ({ record }) => {
          let params = {
            status: 3,
            id: record.id
          }
          handleEditData(params)
        },
        show: ({ record }) => record.status == 2
      },
      {
        name: '删除',
        onclick: ({ record }) => {
          let params = {
            status: 1,
            id: record.id
          }
          handleEditData(params)
        },
        show: ({ record }) => record.status == 3
      },
      {
        name: '驳回',
        onclick: ({ record }) => {
          const { vnode, reason } = rejectPopUpWindow()
          Modal.confirm({
            title: '你确定要驳回该数据?',
            content: vnode,
            icon: null,
            wrapClassName: 'reject-modal',
            bodyStyle: {
              padding: '0'
            },
            onOk() {
              if (!reason.value) {
                return message.error('驳回理由不能为空')
              }
              let params = {
                reason: reason.value,
                status: 4,
                id: record.id
              }
              handleEditData(params)
            },
            onCancel() {
              console.log('Cancel')
            }
          })
        },
        show: ({ record }) => record.status == 2
      }
    ] // 0 正常  1 已删除 2 待审核 3 已审核 4 已驳回
  }
])
const statusList = ref([])
const total = ref(0)
const pagiNation = reactive({
  current: 1,
  size: 10
})
const attachButtons = ref([
  {
    name: '上传指南',
    onclick: () => {
      handleUploadGuide()
    }
  }
])

// 搜索事件
function handleSearch() {
  pagiNation.current = 1
  getList()
}

// 清空事件
function handleClear() {
  let data = {
    status: [],
    name: ''
  }
  pagiNation.current = 1
  pagiNation.size = 10
  formData = Object.assign(formData, data)
  getList()
}

// 分页list
async function getList() {
  let params = {
    ...pagiNation,
    ...formData,
    status: formData.status && formData.status.length > 0 ? formData.status.join(',') : ''
  }

  getListPage(params)
    .then((res) => {
      if (res.code == 0) {
        tableList.value = res?.data?.records ?? []
        total.value = res?.data?.total ?? 0
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

// 分页事件
function handleChangePage({ pageNum, pageSize }) {
  pagiNation.current = pageNum
  pagiNation.size = pageSize
  getList()
}

async function getDicListData() {
  let params = {
    type: 'guideStatus'
  }
  await getDicList(params)
    .then((res) => {
      if (res.code == 0) {
        statusList.value = res?.data ?? []
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
// 通过/驳回/删除
function handleEditData<T>(params: T) {
  updatePage(params)
    .then((res) => {
      if (res.code == 0) {
        console.log('OK')
        message.success('操作成功')
        getList()
        operationLogs()
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

const viewFile = async (key: string) => {
  await getPdfFile({ path: key }).then((res: any) => {
    if (res?.status == 200) {
      const file = new Blob([res.data], { type: 'application/pdf' })
      saveAs(file, key)
      // 打开新页面并加载文件
      const newWindow = window.open('', '_blank')
      if (newWindow) {
        newWindow.onload = function () {
          console.log('新窗口已成功加载文件')
        }
        newWindow.location.href = URL.createObjectURL(file)
        // 转换为PDF并添加功能
        convertToPDF(newWindow.document)
      }
    } else {
      proxy.$message.error(res?.errorMsg)
    }
  })
}

const getTagColor = (status: number) => {
  switch (status) {
    case 4:
    case 1:
      return 'red'
    case 3:
      return 'green'
    case 2:
      return 'blue'
    default:
      return '' // 可以根据需要返回默认颜色
  }
}

// 上传表单
interface FormState {
  name: string
  description: string
  nodeId: number | null
  file: File[]
}
let drawerFormData: UnwrapRef<FormState> = reactive({
  name: '',
  description: '',
  nodeId: null,
  file: []
})
const isOpenDrawer = ref(false)
const labelCol = { span: 5 }
const wrapperCol = { span: 14 }
const propsLabel = {
  label: 'name',
  value: 'id'
}
const selectedTypeTreeKeys = ref([])
const treeData = ref<any[]>([])
const searchValue = ref('')
const formRef = useTemplateRef('formRef')
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请填写指南名称', trigger: 'blur' },
    { min: 0, max: 50, message: '指南名称最多不超过50个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请填写指南描述', trigger: 'blur' },
    { min: 0, max: 150, message: '指南描述最多不超过150个字符', trigger: 'blur' }
  ],
  nodeId: [{ required: true, message: '请选择指南分类', trigger: 'change' }],
  file: [{ required: true, message: '请上传文件', trigger: 'change' }]
}
function handleUploadGuide() {
  isOpenDrawer.value = true
}

const beforeUpload: UploadProps['beforeUpload'] = (_file, _options) => {
  const file = _file
  const options = _options

  // 获取文件后缀如 pdf 等
  const fileType = file?.name.split('.')
  const fileDate = fileType.slice(-1)

  // 将从父组件拿到的 accept 类型转为数组，类似['.pdf']
  const docsArr = ['pdf']

  if (!docsArr?.includes(fileDate[0])) {
    message.error(`仅支持文件格式：${docsArr.join(',')}格式附件!`)
    return false
  }

  // if (file.size / 1024 / 1024 > 100) {
  //   message.error("文件大小不能超过100兆");
  //   return false;
  // }

  drawerFormData.file = [file]
  return false
}

const handleRemove: UploadProps['onRemove'] = (file) => {
  drawerFormData.file = []
}

const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      let data = toRaw(drawerFormData)
      let params = {
        ...data,
        file: data.file[0]
      }

      addKnowledgeGuide(params)
        .then((res) => {
          if (res.code == 0) {
            message.success('操作成功')
            onClose()
            getList()
            operationLogsUpdate()
          }
        })
        .catch((err) => {
          console.log(err, 'err')
        })
      console.log('values', params)
    })
    .catch((error) => {
      console.log('error', error)
    })
}

const onClose = () => {
  drawerFormData = Object.assign(drawerFormData, {
    name: '',
    description: '',
    nodeId: null,
    file: []
  })
  searchValue.value = ''
  isOpenDrawer.value = false
}

async function getKnowledgeTreeList(params: string) {
  await getKnowledgeNodeTree({ keyword: params })
    .then((res: any) => {
      if (res?.code == 0) {
        treeData.value = formatTreeData(res.data)
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

function formatTreeData(arr) {
  const deep = (data) => {
    return data.map((item) => {
      const formattedItem = {
        ...item,
        children: item.children && item.children.length > 0 ? deep(item.children) : [],
        selectable: !(item.children && item.children.length > 0)
      }
      return formattedItem
    })
  }

  return deep(arr)
}

onMounted(() => {
  getDicListData()
  getKnowledgeTreeList('')
  getList()
})

const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '审核高原病知识库',
    ip: baseIP,
    type: '高原病数据审核'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
const operationLogsUpdate = async () => {
  const obj = {
    category: '更新高原病知识库',
    ip: baseIP,
    type: '高原病数据审核'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  // display: flex;
  // flex-direction: column;
  // padding: 0 10px;
}

:deep(.ant-form-inline .ant-form-item) {
  margin-bottom: 24px;
  margin-right: 24px;
}

.form-data-inline {
  width: 100%;
  display: inline-flex;
}

.btn-export {
  display: flex;
  justify-content: flex-end;
  flex-direction: row;
  margin-bottom: 24px;
}

.wrapper-title {
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9e9e9;
}

.table {
  width: 100%;
}

.upload-btn {
  margin-bottom: 16px;
}

:deep(.ant-modal .ant-modal-content) {
  border-radius: 8px !important;
}

:deep(.ant-tag) {
  margin-inline-end: 4px;
}
</style>
