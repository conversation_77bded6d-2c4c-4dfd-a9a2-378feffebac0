import { useStorage } from '@vueuse/core'
import { theme as ATheme } from 'ant-design-vue'
import { reactive, ref } from 'vue'

export enum THEME {
  LIGHT = 'light',
  DARK = 'dark'
}

const lightBase = '#333'
const darkBase = '#ffffff'
const antdTheme = reactive({
  token: {
    borderRadius: 2,
    colorPrimary: '#209f85',
    colorBgBase: darkBase
  },
  algorithm: ATheme.defaultAlgorithm
})
const theme = useStorage<THEME>('dw-ris-theme', THEME.LIGHT)
const setTheme = (nextTheme: THEME) => {
  theme.value = nextTheme
  antdTheme.algorithm = nextTheme === THEME.LIGHT ? ATheme.defaultAlgorithm : ATheme.darkAlgorithm
  antdTheme.token.colorBgBase = nextTheme === THEME.LIGHT ? darkBase : lightBase
  document.documentElement.setAttribute('data-theme', nextTheme)
}

export const useTheme = () => {
  return {
    theme,
    antdTheme,
    setTheme
  }
}
