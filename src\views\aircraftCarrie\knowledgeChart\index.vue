<template>
  <div class="card flex-1 wrapper">
    <div class="search-box">
      <a-input-group compact class="input-group-styles">
        <a-select class="select-styles" :loading="loading" allowClear show-search :default-active-first-option="false"
          :show-arrow="false" style="width: 300px" v-model:value="searchName" :filter-option="false"
          :not-found-content="null" placeholder="请输入疾病名称" @select="handleChange" @search="handleSearch">
          <a-select-option :value="item" v-for="item in selectList" :key="item">{{ item }}</a-select-option>
        </a-select>
        <a-button class="btn-styles" @click="handleSearchList">
          <template #icon>
            <SearchOutlined />
          </template>
        </a-button>
      </a-input-group>
    </div>
    <div class="chart-box">
      <div ref="myChartRef" id="chart-container" class="chart-container"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, onMounted, useTemplateRef } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import _ from 'lodash'
import { useRouter } from 'vue-router'
import { getKnowledgeTree, guideSearchGraph, getAltKnowledgeGuide, getPdfFile } from './index.api.ts'
import { saveAs } from 'file-saver'
import { formatData, convertToPDF } from './index.config.ts'
import { SearchOutlined } from '@ant-design/icons-vue'
import { operationLog } from '@/api/log'

const { proxy }: any = getCurrentInstance()
// 图表实例
let chartDom: HTMLElement | null = null
let myChart: ECharts | null = null
let options = {}

// 搜索条件
const searchName = ref<string[]>([])
const selectList = ref<string[]>([])
const loading = ref(false)
// 远程搜索
const handleSearch = _.debounce(function (value) {
  loading.value = true
  let params = {
    value: value
  }
  guideSearchGraph(params)
    .then((res) => {
      if (res?.code == 0) {
        selectList.value = res?.data ?? []
      }
    })
    .catch((err) => {
      console.log('err', err)
    })
    .finally(() => {
      loading.value = false
    })
}, 200)

// 下拉框选择事件
const handleChange = (value: string) => {
  searchName.value = [value]
}

interface Categories {
  name: string
}
let dataCategories: Categories[] = []
const changeNodes = ref([])
const dataLink = ref([])
const allNode = ref({})
const defaultValue = ref([])
const lineCount = ref(0)

// 确认搜索
const handleSearchList = async () => {
  myChart?.showLoading()
  let params = {
    name: searchName.value.length > 0 ? searchName.value : defaultValue.value
  }
  await getKnowledgeTree(params)
    .then((res) => {
      if (res?.code == 0) {
        allNode.value = res.data
        // data
        changeNodes.value = res?.data?.nodes.length > 0 ? res?.data?.nodes : []
        // 分类
        dataCategories = res?.data?.categories.length > 0 ? res?.data?.categories.map((item: Categories) => ({ name: item.name })) : []
        // 线条
        dataLink.value = res?.data?.links.length > 0 ? res?.data?.links : []
      } else {
        proxy.$message.error(res?.errorMsg)
      }
    })
    .catch((err) => {
      console.log('err', err)
    })
    .finally(() => {
      // myChart?.hideLoading()
      initECharts('')
    })
}

const myChartRef = useTemplateRef<HTMLElement>('myChartRef')
async function initECharts(type: string) {
  if (type) {
    await handleSearchList()
  }
  if (!allNode.value && lineCount.value < 5) {
    lineCount.value += 1
    defaultValue.value = ['高原病']
    console.log(defaultValue.value, 'defaultValue')
    await handleSearchList()
    return
  }
  // 图表实例
  chartDom = document.getElementById('chart-container')
  myChart = echarts.init(chartDom, null, {
    renderer: 'canvas',
    useDirtyRect: false
  })
  myChart?.showLoading()

  options = {
    animationDurationUpdate: 750,
    animationEasingUpdate: 'quinticInOut',
    type: 'tree',
    backgroundColor: '#EEFAF9',
    // backgroundColor: new echarts.graphic.RadialGradient(0.3, 0.3, 0.8, [
    //   {
    //     offset: 0,
    //     color: '#f7f8fa'
    //   },
    //   {
    //     offset: 1,
    //     color: '#cdd0d5'
    //   }
    // ]),
    selectedMode: false,
    dataZoom: {
      type: 'inside' //放大缩小x轴数值
    },
    xAxis: {
      show: false,
      type: 'value'
    },
    yAxis: {
      show: false,
      type: 'value'
    },
    toolbox: {
      show: true,
      feature: {
        dataView: {
          // 查看
          show: true,
          readOnly: true
        },
        restore: {
          // 恢复
          show: true
        },
        saveAsImage: {
          // 保存图片
          show: true
        }
      }
    },
    tooltip: {
      //提示框
      trigger: 'item',
      triggerOn: 'mousemove',
      backgroundColor: 'rgba(1,70,86,1)',
      borderColor: 'rgba(0,246,255,1)',
      color: '#ffffff',
      borderWidth: 0.5,
      textStyle: {
        fontSize: 10,
        color: '#ffffff'
      }
    },
    emphasis: {
      focus: 'descendant', // 与该点有关联的层级都高亮
      lineStyle: {
        width: 8,
        overflow: 'breakAll'
      }
    },
    label: {
      show: true,
      color: '#333333',
      fontSize: 15
      // formatter: '\n'
    },
    series: [
      {
        type: 'tree',
        hoverAnimation: true, //hover样式
        // legendHoverLink: true //是否启用图例 hover(悬停) 时的联动高亮。
        data: formatData(changeNodes.value, 0),
        categories: dataCategories,
        links: dataLink.value,
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        layout: 'radial', // 设置为径向布局
        // orient: 'LR',
        symbol: 'circle',
        symbolSize: 30,
        // nodePadding: 20,
        nodePadding: [50, 120], // nodePadding的数组值为[10, 20]，表示第一层节点与第二层节点之间的水平间距为10，垂直间距为20。
        nodeGap: 100,
        expandAndCollapse: true, //子树折叠和展开的交互，默认打开
        initialTreeDepth: 2, //初始树的深度
        roam: true, //是否开启鼠标缩放和平移漫游。scale/move/true
        nodeScaleRatio: 0.6, //鼠标漫游缩放时节点的相应缩放比例，当设为0时节点不随着鼠标的缩放而缩放
        // coordinateSystem: null, //坐标系可选
        // xAxisIndex: 0, //x轴坐标 有多种坐标系轴坐标选项
        // yAxisIndex: 0, //y轴坐标
        draggable: true, //节点是否可拖拽，只在使用力引导布局的时候有用。
        force: {
          //力引导图基本配置
          repulsion: 36, //节点之间的斥力因子。支持数组表达斥力范围，值越大斥力越大。
          friction: 0.6,
          layoutAnimation: true,
          gravity: 0.05, //节点受到的向中心的引力因子。该值越大节点越往中心点靠拢。
          edgeLength: [10, 200] //边的两个节点之间的距离
        },
        focusNodeAdjacency: true, //是否在鼠标移到节点上时突出显示其邻接节点
        itemStyle: {
          borderWidth: 1
        },
        lineStyle: {
          width: 2,
          curveness: 0.5
        },
        edgeLabel: {
          show: true,
          fontSize: 14,
          formatter: '{c}'
        },
        tooltip: {
          extraCssText: 'max-width:200px; white-space:pre-wrap;font-size:14px;line-height:1.5;',
          formatter: function (params) {
            return `<div>${params.data.name}</div><div>${params.data.nodeSynonym || ''}</div>`
          }
        },
        label: {
          //标签样式
          color: '#ffffff',
          textBorderColor: '#07123C', // 描边颜色
          textBorderWidth: 2, //描边宽度
          fontSize: 12,
          fontFamily: 'SourceHanSansCN',
          position: 'inside',
          rotate: 0,
          verticalAlign: 'middle',
          align: 'center',
          width: 180, //指定宽度，
          // height: 100,
          lineHeight: 20,
          padding: [4, 4, 4, 4],
          overflow: 'break',
          formatter: function (params) {
            let text = params.data.name
            // let length = text.length;
            let maxLineLength = 15 // 每行最多显示的字符数
            // let lineCount = Math.ceil(length / maxLineLength); // 计算需要几行
            let lineCount = 2 // 计算需要几行
            let lines = []
            for (let i = 0; i < lineCount; i++) {
              let line = text?.substr(i * maxLineLength, maxLineLength)
              if (line?.length >= 15) {
                line += '...'
              }
              lines.push(line)
            }
            return lines.join('\n')
          }
        },
        emphasis: {
          focus: 'descendant', // 与该点有关联的层级都高亮
          lineStyle: {
            width: 8,
            color: '#ff991f'
          }
        }
      }
    ]
  }

  myChart.off('click')
  myChart.off('mouseover')
  options && myChart.setOption(options)
  myChart.hideLoading()
  myChart.on('click', function (params) {
    console.log(params, 'params')
    if (params.seriesType == 'tree') {
      if (params.data.level == 5) {
        // 调用接口 - 下载 PDF
        const { guideId, isJumpable } = params.data
        if (guideId && isJumpable === 1) {
          getAltKnowledgeGuide({ id: guideId })
            .then((res) => {
              if (res?.code === 0) {
                viewFile(res?.data.storagePath)
              }
            })
            .catch((err) => {
              console.log('err', err)
            })
        }
      } else {
        let data = JSON.parse(JSON.stringify(formatData(changeNodes.value, 0)))
        const curData = params.data
        let paths = []

        // 查找目标节点的路径
        function toggleNode(id, nodes, path, found) {
          if (!nodes || nodes.length === 0 || found) return

          for (let node of nodes) {
            if (node.level === 2) path = [] // 遇到第二层节点，重置路径

            path.push(node.id)

            if (node.id === id) {
              found = true
              paths = [...path] // 找到目标节点，保存路径
              return
            } else if (node.children) {
              toggleNode(id, node.children, path, found)
              found = false // 继续查找;
            }

            if (!found) {
              path.pop() // 回溯
            }
          }
        }

        // 更新节点状态，递归展开或隐藏节点
        function changeClose(id, nodes, isShowChild = false) {
          if (!nodes || nodes.length === 0) return nodes

          return nodes
            .map((node) => {
              const isTargetNode = paths.includes(node.id)
              if (isTargetNode) {
                node.collapsed = false
                if (node.id === id && node.children) {
                  // 展开当前节点及其子节点
                  node.children.forEach((child) => (child.collapsed = false))
                  isShowChild = true
                }
                if (node.children) {
                  node.children = changeClose(id, node.children, isShowChild)
                  isShowChild = false // 重置标记
                }
              } else {
                // 对未匹配的节点递归处理
                if (node.children) {
                  node.children = changeClose(id, node.children, false)
                }
                if (node.level !== 1) {
                  node = isShowChild ? node : null // 非第一层节点，根据标记决定是否隐藏
                }
              }
              return node
            })
            .filter(Boolean) // 去除 null 节点
        }

        // 查找目标节点的路径
        toggleNode(curData.id, data, [], false)

        // 更新节点状态
        data = changeClose(curData.id, data, false)

        // 更新图表数据
        options.series[0].data = data ?? []

        options.series[0].layout = params.data?.level == 1 ? 'radial' : 'orthogonal' // 使用正交布局
        options.series[0].top = 'middle'
        options.series[0].left = 'center'

        myChart.setOption(options)
      }
    }
  })

  // let currentDataIndexs: any[] = []
  // // 节点鼠标移入事件
  // myChart.on('mouseover', _.debounce(function (params) {
  //   // 取消当前节点的高点，顶替默认事件
  //   myChart.dispatchAction({
  //     type: 'downplay',
  //     dataIndex: params.dataIndex
  //   })

  //   currentDataIndexs.push(params.dataIndex)
  //   let newArr = params.treeAncestors.map(item => item.dataIndex)
  //   currentDataIndexs = _.uniq([...newArr, ...currentDataIndexs])

  //   // 高亮点击已保存的相关节点的连线，防止上一步取消了已保存节点的高亮
  //   myChart.dispatchAction({
  //     type: 'highlight',
  //     dataIndex: currentDataIndexs
  //   })
  // }, 200));

  // // 节点鼠标移出事件
  // myChart.on('mouseout', function (params) {
  //   // 取消当前节点的高点，顶替默认事件
  //   myChart.dispatchAction({
  //     type: 'downplay',
  //     dataIndex: currentDataIndexs
  //   })
  //   currentDataIndexs = []
  // });

  window.addEventListener(
    'resize',
    _.debounce(() => {
      myChart.resize({
        animation: {
          duration: 200
        }
      })
    }, 200)
  )
}

const viewFile = async (key: string) => {
  await getPdfFile({ path: key }).then((res) => {
    if (res?.status == 200) {
      const file = new Blob([res.data], { type: 'application/pdf' })
      saveAs(file, key)
      // 打开新页面并加载文件
      const newWindow = window.open('', '_blank')
      if (newWindow) {
        newWindow.onload = function () {
          console.log('新窗口已成功加载文件')
        }
        newWindow.location.href = URL.createObjectURL(file)
        // 转换为PDF并添加功能
        convertToPDF(newWindow.document)
      }
    } else {
      proxy.$message.error(res?.errorMsg)
    }
  })
}

onMounted(() => {
  // handleSearch('')
  initECharts('init')
})

const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '高原病知识图谱',
    ip: baseIP,
    type: '高原病知识库'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
</script>
<style lang="less" scoped>
.chart-container {
  width: 100%;
  height: 100%;
  // padding: 100px;
  margin: 0 auto;
  font-family:
    Avenir,
    Helvetica,
    Arial,
    sans - serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  background-image: url();
}

#chart-container {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}

.search-box {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

.chart-box {
  width: 100%;
  height: 100vh;
}

:deep(.select-styles .ant-select-selector) {
  border-radius: 4px 0 0 4px !important;
}

:deep(.input-group-styles .btn-styles) {
  border-radius: 0px 4px 4px 0 !important;
}
</style>
