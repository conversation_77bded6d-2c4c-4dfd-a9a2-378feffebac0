<template>
  <a-layout-header class="header">
    <div class="header-item">
      <a-dropdown>
        <div class="h-full text-primary-text-color flex gap-2 items-center cursor-pointer">
          <img class="rounded-full h-8 w-8" src="/src/assets/png/head.png" alt="" />
          <div class="text-sm">{{ userInfo.username }}</div>
        </div>
        <template #overlay>
          <a-menu>
            <!-- <a-menu-item @click="handleSwitchTheme">切换主题</a-menu-item> -->
            <a-menu-item @click="handlePersonalData">个人中心</a-menu-item>
            <a-menu-item @click="handleCommand">退出登录</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </a-layout-header>
</template>

<script setup lang="ts">
import { h, ref } from "vue";
const { theme, setTheme } = useTheme();
import { THEME, useTheme } from "@/utils/use-theme";
import { message, Modal } from "ant-design-vue";
import { useUserStore } from "@/store/routes/user";
import { useRouter } from "vue-router";
  import { setConfig } from '@/config/index'

const userStore = useUserStore();
const userInfo = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo') || '') : userStore.userInfo;
const router = useRouter();

const handleCommand = () => {
  Modal.confirm({
    title: "提示",
    content: h(
      "div",
      {
        class: "input-container",
        style: {
          width: "100%",
          display: "flex",
          padding: "20px 15px 30px",
          borderTop: "1px solid #f0f0f0",
        },
      },
      "是否确认退出登录？"
    ),
    icon: null,
    wrapClassName: "reject-modal",
    bodyStyle: {
      padding: "0",
    },
    onOk: () => {
       let service = localStorage.getItem("service");
        if (service && service =='ALT') {
          setConfig({
            APP_CODE: service,
            REDIRECT_URI: import.meta.env.MODE == 'production' ? `${window.location.origin}/ams-front/home?service=${service}` : 'http://localhost:3004/?service=ALT'
          })
        }

      userStore.remove();
    },
  });
};

const handleSwitchTheme = () => {
  setTheme(theme.value === THEME.LIGHT ? THEME.DARK : THEME.LIGHT);
};

const handlePersonalData = () => {
  console.log(userStore.userInfo);
  let userId = userStore.userInfo.id || JSON.parse(localStorage.getItem('userInfo')|| '').id;
  router.push({
    path: "/platformManagement/personalCenter",
    query:{
      userId: userId
    }
  });
};
</script>

<style lang="less" scoped>
.header {
  background-color: #fff;
  padding: 0;
  height: var(--header-height, 48px);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-bottom: 1px solid rgba(5, 5, 5, 0.06);
}

[data-theme="dark"] .header {
  background-color: #555555;
}

.header-item {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 30px;
}

.text-sm {
  color: var(--color-light-text);
}
</style>
