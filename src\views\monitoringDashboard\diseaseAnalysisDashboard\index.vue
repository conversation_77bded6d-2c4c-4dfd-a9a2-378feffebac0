<template>
  <div class="wrapper clearfix">
    <!-- 时间筛选 -->
    <div class="time-filter">
      <a-form :model="formData" name="time_related_controls">
        <a-form-item name="range-picker" label="统计时间范围">
          <a-range-picker v-model:value="formData.time" @change="handleChangeDate" :presets="presetTimeRange()"
            value-format="YYYY-MM-DD" :allowClear="false" />
          <!-- :disabled-date="disabledStartDate" -->
        </a-form-item>
      </a-form>
    </div>
    <!-- 图表内容 -->
    <div class="wrapper-content">
      <!-- 折线图/柱状图 -->
      <div class="top-charts">
        <!-- 折线图 -->
        <div class="charts-styles-box">
          <lineTrend :loading="lineLoading" :isShowSelect="false" chartId="chartContainer" :option="lineOptionsData"
            title="高原病发病趋势" />
        </div>
        <!-- 柱状图 -->
        <div class="charts-styles-box">
          <lineTrend :loading="barLoading" :isShowSelect="false" chartId="barChartContainer"
            :option="barChartOptionsData" title="高原病发病排序" />
        </div>
        <!-- 饼图 -->
        <!-- 高原病患者就诊医院分布 -->
        <!-- 
        <div class="charts-styles-box">
          <lineTrend
            chartId="pieChartContainer1"
            :option="pieChartOptionsDataHospital"
            title="高原病患者就诊医院分布"
          />
        </div>
        -->
        <!-- 高原病患者籍贯省份分布 -->
        <div class="charts-styles-box">
          <lineTrend :loading="provinceLoading" ref="pieChartContainer2" chartId="pieChartContainer2"
            v-model="formData.selectedProvince" :option="pieChartOptionsData.provinceOptions" title="高原病患者区域分布"
            @change="handleSelectProvince" :selectOptions="diseaseOptions" />
        </div>
        <!-- 高原病患者性别分布 -->
        <div class="charts-styles-box">
          <lineTrend :loading="sexLoading" ref="pieChartContainer3" chartId="pieChartContainer3"
            v-model="formData.selectedSex" :option="pieChartOptionsData.sexOptions" title="高原病患者性别分布"
            @change="handleSelectSex" :selectOptions="diseaseOptions" />
        </div>
        <!-- 高原病患者确诊年龄组分布 -->
        <div class="charts-styles-box">
          <lineTrend :loading="ageLoading" ref="pieChartContainer4" chartId="pieChartContainer4"
            v-model="formData.selectedAge" :option="pieChartOptionsData.ageOptions" title="高原病患者确诊年龄组分布"
            @change="handleSelectAge" :selectOptions="diseaseOptions" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRaw, onMounted, useTemplateRef } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { presetTimeRange } from '@/utils/utils'
import lineTrend from '@/components/ECharts/lineTrend.vue'
import { chartOptions, barChartOptions, pieChartOptions } from './index.config.ts'
import { getAltDiseaseDashBoardDeginnrt, getAltDiseaseDashBoardAmount, getAltDiseaseDashBoardDistribution, getAltDiseaseDashBoardDisease } from './index.api'
import { operationLog } from '@/api/log'

const disabledStartDate = (current: dayjs.Dayjs) => {
  return current.isBefore(dayjs('2022-01-01'))
}

// 时间选择
const formData = reactive({
  time: [],
  selectedProvince: '全部',
  selectedSex: '全部',
  selectedAge: '全部'
})

const lineLoading = ref(false)
// 折线图数据
const brokenLineDataList = ref([])
const lineOptionsData = reactive({
  ...chartOptions,
  legend: {
    ...chartOptions.legend,
    selected: []
  },
  toolbox: {},
  xAxis: [
    {
      ...chartOptions.xAxis[0],
      data: []
    }
  ],
  series: [],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (params) {
      const filtered = params.filter(item => item.value !== 0)
      if (filtered.length === 0) return ''
      const date = filtered[0].axisValue
      const lines = filtered.map(item => `${item.marker}${item.seriesName}: ${item.value}`)
      return [date, ...lines].join('<br/>')
    }
  },
})

const barLoading = ref(false)
// 柱状图数据
const barChartDataList = ref([])
const barChartOptionsData = reactive({
  ...barChartOptions,
  xAxis: {
    ...barChartOptions.xAxis,
    data: []
  },
  series: []
})

// 饼图数据
const diseaseOptions = ref([])
const pieData = reactive({
  pieProvinceData: [] as any[], //  1.1 省份
  pieSexData: [] as any[], //  1.2 性别
  pieAgeData: [] as any[] // 1.3 年龄
})
// 省
const pieChartContainer2 = useTemplateRef('pieChartContainer2')
// 性别
const pieChartContainer3 = useTemplateRef('pieChartContainer3')
// 年龄
const pieChartContainer4 = useTemplateRef('pieChartContainer4')
const provinceLoading = ref(false)
const sexLoading = ref(false)
const ageLoading = ref(false)
const pieChartOptionsData = reactive({
  provinceOptions: {
    ...pieChartOptions,
    legend: {
      ...pieChartOptions.legend,
      selected: []
    },
    toolbox: {},
    series: []
  },
  sexOptions: {
    ...pieChartOptions,
    legend: {
      ...pieChartOptions.legend,
      selected: []
    },
    toolbox: {},
    series: []
  },
  ageOptions: {
    ...pieChartOptions,
    legend: {
      ...pieChartOptions.legend,
      selected: []
    },
    toolbox: {},
    series: []
  }
})

function handleChangeDate(value: any) {
  formData.time = value
  clearData()
  query()
}

// 获取折线图数据
async function getAltBrokenLine() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  lineLoading.value = true
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || ''
  }
  await getAltDiseaseDashBoardDeginnrt(params)
    .then((res) => {
      if (res.code == 0) {
        // 方案一
        // // 时间线
        // let dates = res?.data?.map((item) => item.date) ?? [];
        // // 提取所有独特的疾病名称
        // let diseases = Array.from(new Set(res?.data?.flatMap((item) => item.diseases)));
        // // 初始化每个疾病的数据序列
        // const diseaseData = diseases.reduce((acc, disease) => {
        //   acc[disease] = dates.map((date) => {
        //     const dayData = res?.data?.find((item) => item.date === date);
        //     if (dayData) {
        //       const index = dayData.diseases.indexOf(disease);
        //       return index !== -1 ? dayData.cnt[index] : 0;
        //     }
        //     return 0;
        //   });
        //   return acc;
        // }, {});

        // brokenLineDataList.value = diseases.map((disease) => ({
        //   name: disease,
        //   type: "line",
        //   symbol: "none", //去掉折线图中的节点
        //   smooth: true, //true 为平滑曲线，false为直线
        //   stack: "Total",
        //   label: {
        //     show: true,
        //     position: "top",
        //   },
        //   areaStyle: {},
        //   emphasis: {
        //     focus: "series",
        //   },
        //   data: diseaseData[disease],
        // }));

        // Object.assign(lineOptionsData, {
        //   ...lineOptionsData,
        //   legend: {
        //     ...lineOptionsData.legend,
        //     selected: diseases,
        //   },
        //   xAxis: [
        //     {
        //       ...lineOptionsData.xAxis[0],
        //       data: dates,
        //     },
        //   ],
        //   series: brokenLineDataList.value,
        // });

        // 方案二
        // 这种性能更好，方便后期维护
        // 1. 提取所有日期和疾病
        const dates = res?.data?.map((item) => item.date) ?? []
        const diseases = Array.from(new Set(res?.data?.flatMap((item) => item.diseases)))

        // 2. 创建日期-疾病映射
        const diseaseMap = new Map()
        res?.data?.forEach((item) => {
          item.diseases.forEach((disease, index) => {
            const key = `${item.date}_${disease}`
            diseaseMap.set(key, item.cnt[index]) // 映射日期_疾病 -> 对应计数
          })
        })

        // 3. 构建每种疾病的时间序列数据
        brokenLineDataList.value = diseases.map((disease) => {
          const data = dates.map((date) => {
            const key = `${date}_${disease}`
            return diseaseMap.get(key) || 0 // 如果没有数据则默认返回 0
          })
          return {
            name: disease,
            type: 'line',
            symbol: 'none',
            smooth: true,
            stack: 'Total',
            label: {
              show: true,
              position: 'top'
            },
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data
          }
        })

        // 4. 更新图表配置
        Object.assign(lineOptionsData, {
          ...lineOptionsData,
          legend: {
            ...lineOptionsData.legend,
            selected: diseases
          },
          xAxis: [
            {
              ...lineOptionsData.xAxis[0],
              data: dates // 设置 X 轴数据
            }
          ],
          series: brokenLineDataList.value // 设置折线图数据
        })
        // console.log(lineOptionsData, "lineOptionsData");
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    }).finally(() => {
      lineLoading.value = false
    })
}

// 柱状图
async function getAltBrokenBarChart() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  barLoading.value = true
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || ''
  }
  getAltDiseaseDashBoardAmount(params)
    .then((res) => {
      if (res.code == 0) {
        const { data: resData } = res || {}
        const xAxisData = resData?.map((item) => item.key) ?? []
        const datas = resData?.map((item) => item.cnt) ?? []

        let dataArr = {
          type: 'bar',
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(1, 110, 255, 0.05)'
          },
          normal: {
            color: '#405BC8',
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#A7ABB3',
                fontSize: 12
              }
            }
          },
          label: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#A7ABB3',
              fontSize: 12
            }
          },
          data: datas
        }

        barChartDataList.value = resData?.length > 0 ? [dataArr] : []

        Object.assign(barChartOptionsData, {
          ...barChartOptionsData,
          xAxis: {
            ...barChartOptionsData.xAxis,
            data: xAxisData
          },
          series: barChartDataList.value
        })
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    }).finally(() => {
      barLoading.value = false
    })
}

// 提取请求参数构建函数
function buildParams(formData, patientCharacteristics = '', disease = '') {
  const data = toRaw(formData)
  if (data.time?.length === 0) {
    message.error('请选择时间范围')
    return null
  }
  return {
    disease: disease == '全部' ? '' : disease,
    patientCharacteristics,
    from: data.time[0] || '',
    to: data.time[1] || ''
  }
}

interface PieChartOptions {
  data?: any[]
  resData?: any[]
  name?: string
  type?: string
  chartRef?: any
  isLabel?: boolean
  isLabelLine?: boolean
  label?: any
  labelLine?: any
  isLabelLayout?: boolean
  option?: any
}
// 提取数据处理函数
function processData<T extends PieChartOptions>(options: T) {
  const { resData, isLabel = true, label = {}, isLabelLine = true, labelLine = {}, option } = options
  return (
    resData?.map((item) => ({
      ...item,
      ...option,
      name: item.key,
      value: item.cnt,
      label: {
        show: item.cnt > 0 && isLabel,
        ...label
      },
      labelLine: {
        show: item.cnt > 0 && isLabelLine,
        ...labelLine
      }
    })) ?? []
  )
}

// 提取更新图表数据的函数
function updateChartOptions<T extends PieChartOptions>(options: T) {
  const { data, name, type, chartRef, label, labelLine, isLabel = true, isLabelLine = true, option } = options
  let isCheck = data?.some((item) => item.value !== 0)
  let dataArr = [
    {
      ...pieChartOptions.series[0],
      ...option,
      label: isLabel && label ? label : pieChartOptions.series[0].label,
      labelLine: isLabelLine && labelLine ? labelLine : pieChartOptions.series[0].labelLine,
      labelLayout: function (params) {
        // if (!chartRef) return params.labelLinePoints;
        // const isLeft = params.labelRect.x < chartRef?.chartInstance?.getWidth() / 2;
        // const points = params.labelLinePoints;
        // points[2][0] = isLeft
        //   ? params.labelRect.x
        //   : params.labelRect.x + params.labelRect.width;
        return {
          // labelLinePoints: points,
          hideOverlap: false
        }
      },
      data,
      name
    }
  ]

  switch (type) {
    case 'province':
      delete dataArr[0].label
      Object.assign(pieChartOptionsData.provinceOptions, {
        ...pieChartOptionsData.provinceOptions,
        series: isCheck ? dataArr : []
      })
      break
    case 'sex':
      Object.assign(pieChartOptionsData.sexOptions, {
        ...pieChartOptionsData.sexOptions,
        series: isCheck ? dataArr : []
      })
      break
    case 'age':
      Object.assign(pieChartOptionsData.ageOptions, {
        ...pieChartOptionsData.ageOptions,
        series: isCheck ? dataArr : []
      })
      break
    default:
      Object.assign(pieChartOptionsData, {
        ...pieChartOptionsData,
        ageOptions: {
          ...pieChartOptionsData.ageOptions,
          series: []
        },
        sexOptions: {
          ...pieChartOptionsData.sexOptions,
          series: []
        },
        provinceOptions: {
          ...pieChartOptionsData.provinceOptions,
          series: []
        }
      })
      break
  }
}

// 省份
async function getAltBrokenProvincePieChart() {
  let params = buildParams(formData, '省份', formData.selectedProvince)
  if (!params) return
  provinceLoading.value = true
  await getAltDiseaseDashBoardDistribution(params)
    .then((res) => {
      if (res.code === 0) {
        pieData.pieProvinceData = processData({
          resData: res.data?.filter((item) => item.cnt > 0),
          label: {
            formatter: function (params) {
              return `${params.name}:${params.value}例`
            }
          }
        })

        let options = {
          data: pieData.pieProvinceData,
          name: '高原病患者就诊医院分布',
          type: 'province',
          chartRef: pieChartContainer2.value,
          option: {
            minAngle: 5,
            avoidLabelOverlap: true
          }
        }
        updateChartOptions(options)
      }
    })
    .catch((err) => {
      console.error(err)
    }).finally(() => {
      provinceLoading.value = false
    })
}

// 性别
async function getAltBrokenSexPieChart() {
  let params = buildParams(formData, '性别', formData.selectedSex)
  if (!params) return
  sexLoading.value = true
  await getAltDiseaseDashBoardDistribution(params)
    .then((res) => {
      if (res.code === 0) {
        pieData.pieSexData = processData({
          resData: res.data?.filter((item) => item.cnt > 0)
        })
        let options = {
          data: pieData.pieSexData,
          name: '高原病患者性别分布',
          type: 'sex',
          chartRef: pieChartContainer3.value
        }
        updateChartOptions(options)
      }
    })
    .catch((err) => {
      console.error(err)
    }).finally(() => {
      sexLoading.value = false
    })
}

// 年龄
async function getAltBrokenAgePieChart() {
  let params = buildParams(formData, '年龄组', formData.selectedAge)
  if (!params) return
  ageLoading.value = true
  await getAltDiseaseDashBoardDistribution(params)
    .then((res) => {
      if (res.code === 0) {
        pieData.pieAgeData = processData({
          resData: res.data?.filter((item) => item.cnt > 0)
        })
        let options = {
          data: pieData.pieAgeData,
          name: '高原病患者确诊年龄组分布',
          type: 'age',
          chartRef: pieChartContainer4.value
        }
        updateChartOptions(options)
      }
    })
    .catch((err) => {
      console.error(err)
    }).finally(() => {
      ageLoading.value = false
    })
}

// 饼图下拉数据
async function getSelectedDiseaseJob() {
  let data = toRaw(formData)
  if (data.time?.length == 0) {
    return message.error('请选择时间范围')
  }
  let params = {
    from: data.time[0] || '',
    to: data.time[1] || ''
  }
  await getAltDiseaseDashBoardDisease(params)
    .then((res) => {
      if (res.code == 0) {
        diseaseOptions.value = res?.data ?? []
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

// 省份
async function handleSelectProvince(value) {
  pieData.pieProvinceData = []
  await getAltBrokenProvincePieChart()
}
// 性别
async function handleSelectSex(value) {
  pieData.pieSexData = []
  await getAltBrokenSexPieChart()
}
// 年龄
async function handleSelectAge(value) {
  pieData.pieAgeData = []
  await getAltBrokenAgePieChart()
}

async function query() {
  await getSelectedDiseaseJob()
  await getAltBrokenLine()
  await getAltBrokenBarChart()
  await getAltBrokenProvincePieChart()
  await getAltBrokenSexPieChart()
  await getAltBrokenAgePieChart()
}

// 初始化数据
function init() {
  // 初始化筛选数据
  Object.assign(formData, {
    time: [],
    selectedProvince: '全部',
    selectedSex: '全部',
    selectedAge: '全部'
  })
  clearData()
}
// 清空数据
function clearData() {
  // 折线图数据
  brokenLineDataList.value = []
  Object.assign(lineOptionsData, {
    ...chartOptions,
    legend: {
      ...chartOptions.legend,
      selected: []
    },
    toolbox: {},
    xAxis: [
      {
        ...chartOptions.xAxis[0],
        data: []
      }
    ],
    series: []
  })

  // 柱状图数据
  barChartDataList.value = []
  Object.assign(barChartOptionsData, {
    ...barChartOptions,
    xAxis: {
      ...barChartOptions.xAxis,
      data: []
    },
    series: []
  })

  // 饼图数据
  diseaseOptions.value = []
  Object.assign(pieData, {
    pieProvinceData: [], //  1.1 省份
    pieSexData: [], //  1.2 性别
    pieAgeData: [] // 1.3 年龄
  })
  Object.assign(pieChartOptionsData, {
    provinceOptions: {
      ...pieChartOptions,
      legend: {
        ...pieChartOptions.legend,
        selected: []
      },
      toolbox: {},
      series: []
    },
    sexOptions: {
      ...pieChartOptions,
      legend: {
        ...pieChartOptions.legend,
        selected: []
      },
      toolbox: {},
      series: []
    },
    ageOptions: {
      ...pieChartOptions,
      legend: {
        ...pieChartOptions.legend,
        selected: []
      },
      toolbox: {},
      series: []
    }
  })
}

onMounted(() => {
  formData.time = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]

  query()
})

const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '高原病发病分析仪表盘',
    ip: baseIP,
    type: '高原病监测分析'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
onMounted(() => {
  operationLogs()
})
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  min-height: 100%;
  background-color: #f8fafc;
  display: flex;
  flex-direction: column;
}

.time-filter {
  position: fixed;
  right: 30px;
  top: 58px;
  z-index: 999;
  height: 30px;
  display: flex;
  justify-content: flex-end;
}

.wrapper-content {
  width: 100%;
  min-height: 100%;
  background-color: #f8fafc;
  display: flex;
  flex-direction: column;

  .top-charts {
    width: 100%;
    height: auto;
    //  height: 390px;
    position: relative;

    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    .charts-styles-box {
      // width: calc(50% - 10px);
      // height: 100%;
      width: 100%;
      height: 390px;
      padding: 10px;
      box-sizing: border-box;

      padding: 10px;
      background: #ffffff;
      border-radius: 12px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
  }
}
</style>