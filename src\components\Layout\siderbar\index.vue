<template>
  <a-menu
    v-model:selectedKeys="selectedKeys"
    @click="menuClick"
    theme="light"
    mode="inline"
    class="app-sider-menu"
  >
    <template v-for="menu in menuList" :key="menu.name">
      <!-- 只有一个子节点的时候显示子节点 -->
      <router-link
        :to="menu?.children[0].path"
        v-if="menu?.children && menu?.children.length == 1"
      >
        <a-menu-item
          :class="selectedKeys[0] == menu?.children[0].name ? 'menu-item-text-link' : ''"
          :key="menu?.children[0].name"
        >
          <div class="menu-item-text">
            <div style="width: 16x; height: 16px; margin-right: 10px">
              <svg-icon
                :name="menu.children[0].meta.icon"
                className="my"
                color="green"
                size="16"
              />
            </div>
            <span class="one-ellipsis">{{ menu.children[0].meta.title }}</span>
          </div>
        </a-menu-item>
      </router-link>

      <a-sub-menu :key="menu.name" v-else>
        <template #title>
          <div class="menu-item-text">
            <div style="width: 16px; height: 16px; margin-right: 10px">
              <svg-icon :name="menu.meta.icon" className="my" color="green" size="16" />
            </div>
            <span class="one-ellipsis">{{ menu.meta.title }}</span>
          </div>
        </template>

        <router-link :to="item.path" v-for="item in menu.children" :key="item.name">
          <a-menu-item
            :class="selectedKeys[0] == item.name ? 'menu-item-text-link' : ''"
            :key="item?.name"
            v-if="!item?.meta.hidden"
          >
            <div class="menu-item-text">
              <div style="width: 16px; height: 16px; margin-right: 10px">
                <svg-icon
                  :name="item?.meta?.icon"
                  className="my"
                  color="green"
                  size="16"
                />
              </div>
              <span class="one-ellipsis">{{ item?.meta?.title || "" }}</span>
            </div>
          </a-menu-item>
        </router-link>
      </a-sub-menu>
    </template>
  </a-menu>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from "vue";
import { useMeanStore } from "@/store/routes/menus";
import { onBeforeRouteUpdate } from "vue-router";

// 默认选中第一个路由
const selectedKeys = ref<string[]>(["GeographicInfoAnalysis"]);
// 当前打开的路由
const openKeys = ref<string[]>([]);

const useMean = useMeanStore();
let menuList = ref([]);
const list = computed(() => {
  // console.log(useMean.menuList)
  // 过滤掉 hidden 属性为 true 的路由
  return filterMenuRoutes(useMean.menuList);
});
// 这样写是因为他警告说，会出现报错说递归最大值，那就等递归处理完成后再显示到页面菜单中
watch(
  () => list.value,
  (newVal, oldVal) => {
    menuList.value = newVal;
  },
  { immediate: true, deep: true }
);

// 处理不显示的菜单
function filterMenuRoutes<T>(routes: T[]) {
  if (!Array.isArray(routes) || routes.length === 0) {
    return [];
  }

  return routes.reduce((filtered, route) => {
    // 如果 hidden 属性为 true，不显示在菜单中
    if (route.meta && route.meta?.hidden) {
      return filtered;
    }

    // 递归处理子路由
    if (Array.isArray(route.children)) {
      route.children = filterMenuRoutes(route.children);
    }

    // 添加到菜单中
    filtered.push(route);
    return filtered;
  }, []);
}

// 处理页面刷新后，获取对应的选中
const onOpenChange = (routes) => {
  let keys = routes.path.split("/");
  let keyArr = [];
  if (keys.length > 0) {
    //取最后一项，最后一项才是你当前展开的菜单
    keyArr.push(keys[keys.length - 1]);
  }
  openKeys.value = keyArr;
  sessionStorage.setItem("openKeys", JSON.stringify(keyArr));
};

const menuClick = (item) => {
  //判断是否是一级菜单，一级菜单item.keyPath长度为1，二级菜单item.keyPath长度为2，清空二级菜单展开数组openKeys
  if (item.keyPath.length == 1) {
    sessionStorage.removeItem("openKeys");
    openKeys.value = [];
  }
};

onMounted(() => {
  const openKey = sessionStorage.getItem("openKeys");
  if (openKey) {
    selectedKeys.value = JSON.parse(openKey);
  }
});

// 核心逻辑就是使用 onBeforeRouteUpdate 方法在每次路由改变的时候进行监听将 a-menu 的 default-active 更改成我们面包屑点击的目标url
onBeforeRouteUpdate((to: any) => {
  onOpenChange(to);
  selectedKeys.value = [to.name];
});
</script>

<style lang="less" scoped>
.menu-item-text {
  width: 100%;
  display: flex;
  align-items: center;
  justify-self: center;
  // background-color: #e9f5f3;
}

:deep(.ant-menu-item-selected) {
  background-color: #e9f5f3;
}

.app-sider-menu {
  height: calc(100vh - 80px);
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--bg-siderbar-color);
}

:deep(.ant-menu-inline) {
  background-color: var(--bg-siderbar-color) !important;
}

:deep(.menu-item-text-link) {
  background-image: url("@/assets/icons/select-siderbar.svg");
  background-repeat: no-repeat;
  background-position: left;
}

//  设置菜单样式
//  .ant-menu, .ant-menu-sub, .ant-menu-inline {
//   color: white;
//   background-color: #044d50 !important;
// }

//  设置子菜单展开样式
//  .ant-menu-submenu>.ant-menu {
//   background-color: rgb(16, 71, 83) !important;
// }

//  .ant-menu-submenu-title {
//   color: white !important;
// }

//  去掉右边框
//  .ant-menu-inline {
//   border: none;
// }

//  设置 a 链接样式
//  .ant-menu-item a {
//   color: white !important;
// }

// .ant-menu-item a:hover {
//   color: #044d50 !important;
// }

//  下拉箭头样式
//  .ant-menu-submenu-arrow {
//   color: white;
// }

//  设置子菜单样式
//  .ant-menu-submenu-active {
//   background-color: #fff !important;
//    color: #044d50 !important;
//  }

//  选中菜单状态
// .ant-menu-light .ant-menu-item-selected {
//   background-color: #E6FFFB !important;
// }

// 设置未选中菜单项鼠标滑过样式
// .ant-menu-item-active {
//   background-color: #fff !important;
//   color: #666666 !important;
// }
</style>
