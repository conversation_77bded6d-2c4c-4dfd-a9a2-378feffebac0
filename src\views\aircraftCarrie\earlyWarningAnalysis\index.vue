<template>
  <div class="basic-wrapper">
    <!-- 筛选 -->
    <div class="filter-wrapper clearfix">
      <!-- form -->
      <a-card class="table-screen clearfix" title="急性高山病病情预警分析" :bordered="false"
        :bodyStyle="{ padding: '0 24px', minHeight: 'calc(100vh - 180px)' }">

      </a-card>
    </div>

    <div style="background-color: #f8fafc; margin: 16px 0; width: 100%; height: 100vh">
      <a-row :gutter="24" class="pie-row">
        <a-col :span="12" style="padding-left: 12px">
          <div class="card">
            <div>
              <p>路易斯湖评分系统介绍</p>
              <span>路易斯湖评分系统（Lake Louise Score, LLS） 是一种用于评估急性高山病（Acute Mountain Sickness,
                AMS）的标准化工具，广泛应用于高海拔地区的医学研究和临床实践中。它能够基于患者的主观症状，帮助医生或研究人员判断是否存在AMS及其严重程度。</span>
              <p>特别注意:</p>
              <span>
                1.建议在海拔升高（>2,500米）6小时后进行评分，以避免将AMS与旅行引起的混淆症状或急性低氧反应（例如，迷走神经反应）混淆。

              </span>
              <p>评分标准:</p>
              <div class="table">
                <table-list ref="myTable" :tableData="tableList" :tableProps="columns" :pagination="false">
                </table-list>
              </div>
              <p>参考来源：</p>
              <span>
                [1] Roach RC, Hackett PH, Oelz O, et al. The 2018 Lake Louise Acute Mountain Sickness
                Score. High Alt Med Biol. 2018;19(1):4-6. doi:10.1089/ham.2017.0164
              </span>
            </div>
          </div>
        </a-col>
        <a-col :span="12" style="padding-left: 12px">
          <div class="typeCategory card">
            <div>
              <a-spin style="height: 100%" tip="计算中，请稍侯..." :spinning="loading" v-show="isShow">
                <!-- 评估结果显示 -->
                <p>评估结果：
                  <span :style="{
                    fontSize: '16px',
                    color: getResultColor(resultCount.counts)
                  }">
                    {{ results[Number(resultCount.counts)].getResult }}
                  </span>
                </p>

                <!-- 临床建议显示 -->
                <p>临床建议：</p>
                <div class="suggestion-list">
                  <div v-for="(item, index) in results[Number(resultCount.counts)].getSuggestion" :key="index"
                    class="suggestion-item">
                    {{ resultCount.counts === 0 ? item : `${index + 1}. ${item}` }}
                  </div>
                </div>
              </a-spin>
              <div class="lines" v-show="isShow"></div>
              <p>症状</p>
              <!-- :label-col="labelCol" -->
              <a-form :model="formState" :rules="rules" ref="formRef" layout="horizontal" label-align="left">
                <span>
                  <a-form-item label="头痛" name="headache" :label-col="6">
                    <a-radio-group v-model:value="formState.headache">
                      <a-radio :value="0">完全无 (0分)</a-radio>
                      <a-radio :value="1">轻度头痛 (1分)</a-radio>
                      <a-radio :value="2">中度头痛 (2分)</a-radio>
                      <a-radio :value="3">严重头痛，使人无法工作 (3分)</a-radio>
                    </a-radio-group>
                  </a-form-item>

                  <a-form-item label="胃肠道症状" name="giSymptoms" :label-col="6">
                    <a-radio-group v-model:value="formState.giSymptoms">
                      <a-radio :value="0">食欲良好 (0分)</a-radio>
                      <a-radio :value="1">食欲不振或恶心 (1分)</a-radio>
                      <a-radio :value="2">中度恶心或呕吐 (2分)</a-radio>
                      <a-radio :value="3">严重的恶心和呕吐 (3分)</a-radio>
                    </a-radio-group>
                  </a-form-item>

                  <a-form-item label="虚弱" name="weakness" :label-col="6">
                    <a-radio-group v-model:value="formState.weakness">
                      <a-radio :value="0">无疲惫和虚弱 (0分)</a-radio>
                      <a-radio :value="1">轻度疲惫或虚弱 (1分)</a-radio>
                      <a-radio :value="2">中度疲惫或虚弱 (2分)</a-radio>
                      <a-radio :value="3">严重疲惫和虚弱，失去行动力 (3分)</a-radio>
                    </a-radio-group>
                  </a-form-item>

                  <a-form-item label="头晕" name="dizziness" :label-col="6">
                    <a-radio-group v-model:value="formState.dizziness">
                      <a-radio :value="0">无头晕 (0分)</a-radio>
                      <a-radio :value="1">轻度头晕 (1分)</a-radio>
                      <a-radio :value="2">中度头晕 (2分)</a-radio>
                      <a-radio :value="3">重度头晕 (3分)</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </span>
                <p>临床功能</p>
                <span>
                  <a-form-item label="症状对日常活动的影响" name="infectious">
                    <a-radio-group v-model:value="formState.infectious">
                      <a-radio :value="0">完全无 (0分)</a-radio>
                      <a-radio :value="1">症状轻微，活动受限但不明显 (1分)</a-radio>
                      <a-radio :value="2">症状重度，活动受限明显 (2分)</a-radio>
                      <a-radio :value="3">症状严重，无法进行任何活动 (3分)</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </span>
                <span>
                  <a-button style="margin-left: 20px;float: right;" type="primary" @click="submitForm">提交</a-button>
                  <a-button style="margin-left: 10px;float: right;" @click="resetForm">清空</a-button>
                </span>
              </a-form>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { message } from 'ant-design-vue'
  import TableList from "@/components/TableList/index.vue"
  import { operationLog } from '@/api/log'

  // 类型定义
  interface FormState {
    headache: number | undefined
    giSymptoms: number | undefined
    weakness: number | undefined
    dizziness: number | undefined
    infectious: number | undefined
  }

  interface ResultCount {
    counts: number
  }

  interface TableItem {
    countTotal: string
    headacheCount: string
    results: string
  }

  interface ColumnItem {
    id: number
    title: string
    dataIndex: string
    key: string
    ellipsis: boolean
  }

  interface ResultItem {
    getResult: string
    resultLevel: string
    getSuggestion: string[]
  }

  // 表单相关
  const isShow = ref<boolean>(false)
  const formRef = ref()
  const formState = reactive<FormState>({
    headache: undefined,
    giSymptoms: undefined,
    weakness: undefined,
    dizziness: undefined,
    infectious: undefined,
  })
  const resultCount = reactive<ResultCount>({
    counts: 0
  })

  // 表单验证规则
  const rules = {
    headache: [{ required: true, message: '请选择头痛程度', trigger: 'change' }],
    giSymptoms: [{ required: true, message: '请选择胃肠道症状', trigger: 'change' }],
    weakness: [{ required: true, message: '请选择虚弱程度', trigger: 'change' }],
    dizziness: [{ required: true, message: '请选择头晕程度', trigger: 'change' }],
  }

  // 提交表单
  const submitForm = () => {
    formRef.value.validate().then(() => {
      console.log('表单提交数据:', formState)
      isShow.value = true

      // 确保所有值都有默认值
      const headache = formState.headache ?? 0
      const giSymptoms = formState.giSymptoms ?? 0
      const weakness = formState.weakness ?? 0
      const dizziness = formState.dizziness ?? 0
      const infectious = formState.infectious ?? 0

      // 更新表单状态
      formState.headache = headache
      formState.giSymptoms = giSymptoms
      formState.weakness = weakness
      formState.dizziness = dizziness
      formState.infectious = infectious

      // 计算总分
      const counts = headache + giSymptoms + weakness + dizziness + infectious

      loading.value = true
      setTimeout(() => {
        // 根据评分标准确定结果
        if (headache === 0) {
          resultCount.counts = 0
        } else if (counts >= 3 && counts <= 5) {
          resultCount.counts = 1
        } else if (counts >= 6 && counts <= 9) {
          resultCount.counts = 2
        } else {
          resultCount.counts = 3
        }

        console.log(resultCount.counts, 'resultCount.counts')
        loading.value = false
        message.success('提交成功')
      }, 1000)
    }).catch((error: any) => {
      loading.value = false
      isShow.value = false
      message.error('请填写必填项！')
      console.log('表单验证失败:', error)
    })
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(formState, {
      headache: undefined,
      giSymptoms: undefined,
      weakness: undefined,
      dizziness: undefined,
      infectious: undefined,
    })
    formRef.value.resetFields()
    resultCount.counts = 0
    isShow.value = false
  }

  const loading = ref<boolean>(false)

  // 表格数据
  const tableList = ref<TableItem[]>([
    {
      countTotal: '0-12',
      headacheCount: '0',
      results: '无AMS',
    },
    {
      countTotal: '3-5',
      headacheCount: '1-3',
      results: '轻度AMS',
    },
    {
      countTotal: '6-9',
      headacheCount: '1-3',
      results: '中度AMS',
    },
    {
      countTotal: '10-12',
      headacheCount: '1-3',
      results: '重度AMS',
    },
  ])

  // 表格列定义
  const columns = ref<ColumnItem[]>([
    {
      id: 1,
      title: "症状总得分",
      dataIndex: "countTotal",
      key: "countTotal",
      ellipsis: true,
    },
    {
      id: 2,
      title: "头痛得分",
      dataIndex: "headacheCount",
      key: "headacheCount",
      ellipsis: true,
    },
    {
      id: 3,
      title: "结果",
      dataIndex: "results",
      key: "results",
      ellipsis: true,
    },
  ])

  // 结果数据
  const results = ref<ResultItem[]>([
    {
      getResult: '无AMS',
      resultLevel: '0',
      getSuggestion: [
        '无'
      ],
    },
    {
      getResult: '轻度AMS',
      resultLevel: '1',
      getSuggestion: [
        '避免继续攀登或上升至更高海拔',
        '使用便携式吸氧装置，缓解缺氧症状',
        '采用药物缓解症状',
        '如果症状在24 - 48小时内未缓解或加重，需采取进一步措施',
      ],
    },
    {
      getResult: '中度AMS',
      resultLevel: '2',
      getSuggestion: [
        '如果症状持续或加重，应下撤至低海拔地区',
        '高流量吸氧（2-4L/min），缓解缺氧症状',
        '采取药物干预',
        '避免镇静药物，如安眠药或酒精，可能加重症状',
      ],
    },
    {
      getResult: '重度AMS',
      resultLevel: '3',
      getSuggestion: [
        '立即下撤至低海拔地区',
        '使用面罩吸氧（4-6L/min），必要时使用高压氧舱',
        '采取药物急救',
        '尽快联系医疗机构，必要时使用呼吸机辅助治疗',
      ],
    },
  ])

  // 记录操作日志
  const baseIP = import.meta.env.BASE_IP
  const operationLogs = async () => {
    const obj = {
      category: '高原病病情预警分析',
      ip: baseIP,
      type: '高原病知识库'
    }
    try {
      const res = await operationLog(obj)
      if (res && res.data && res.data.code === 0) {
        // 日志记录成功
      }
    } catch (err) {
      console.log(err, 'err')
    }
  }

  // 获取结果颜色
  const getResultColor = (level: string | number): string => {
    switch (String(level)) {
      case '0': return 'inherit'
      case '1': return 'chocolate'
      case '2': return 'orange'
      case '3': return 'red'
      default: return 'inherit'
    }
  }

  // 生命周期钩子
  onMounted(() => {
    operationLogs()
  })
</script>

<style lang="less" scoped>
  .basic-wrapper {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background-color: #f8fafc;
  }

  .typeCategory {
    border-radius: 8px;
    padding: 16px 24px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    // margin-bottom: 16px;
    width: 100%;
    height: 100%;
    position: relative;
    // border: 0.5px solid #e2e8f0;
    box-shadow: none;
  }

  .my-charts {
    width: 100%;
    // height: calc(100% - 40px);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .my-charts-box {
      width: 100%;
      height: 100%;
    }

    .no-data-available {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      color: #5e6580;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
    }

    .userSearchCounts {
      position: absolute;
      top: 2%;
      left: 0;
      margin-left: 50px;
      font-size: 16px;
      color: #07123C;
      font-weight: 600;
    }
  }

  p {
    font-size: 16px;
    font-weight: bold;
    color: #07123c;
    font-family: 'Noto Sans SC';
  }

  .card {
    border-radius: 8px;
    padding: 16px 24px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    // margin-bottom: 16px;
    // height: 100vh;
    height: 800px;
    position: relative;
    // border: 0.5px solid #e2e8f0;
    box-shadow: none;
    overflow-y: scroll;

    p {
      // display: inline-block;
      margin: 30px 0;
    }

    span {
      margin: 30px 0;
      font-size: 14px;
    }
  }

  .lines {
    width: 100%;
    border: 0.1px solid rgb(224, 219, 219);
  }

  .iconDesc {
    // position: absolute;
    // top: 20px;
    // left: 300px;
    margin-left: 10px;
  }

  .selectType {
    position: absolute;
    top: 10px;
    right: 5%;
  }

  .topLeft {
    display: flex;
    flex-wrap: wrap;
    /* justify-content: space-between */
  }

  .selectTypeYear {
    border: none;
    color: #00b929;

    ::v-deep .ant-select-selector {
      border: none;
      color: #00b929;
    }
  }

  .topLeftTitle {
    margin-bottom: 8px;
    font-size: 16px;
    color: #07123c;
    font-weight: bold;
    font-family: 'Noto Sans SC';
    width: 100%;
  }

  .formsTopLeft {
    width: 100%;
  }

  .diseaseTypeSelectTopLeft {
    width: 50%;
  }

  .charts-top-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .charts-title {
      color: #07123c;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      margin-left: 22px;

      .title-tip {
        margin-left: 8px;
      }
    }

    .charts-btn-detail {
      display: inline-flex;
      align-items: center;
      justify-self: center;
      border-radius: 4px !important;
      border: 1px solid #e2e8f0;
      background: #fff;
      margin-right: 20px;

      .anticon-search {
        margin-bottom: 0;
      }
    }
  }

  // 建议列表样式
  .suggestion-list {
    margin-bottom: 20px;

    .suggestion-item {
      display: flex;
      padding: 8px 0;
      font-size: 14px;
      line-height: 1.5;
      border-bottom: 1px dashed #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .item-charts-list {
    width: 100%;
    height: calc(100% - 20px);
  }

  .chart-container-custom {
    height: 100%;
    margin-top: -25px;
  }

  // ::v-deep .a-radio{
  //     width: 150px;
  // }</style>