<template>
  <div class="table-wrap">
    <div class="attach-buttons-wrap" v-if="attachButtons.length > 0">
      <a-button :type="btn.type ? btn.type : 'primary'" class="table-btn letter-text" v-bind="btn.props"
        @click="btn.onclick({ selectedRowKeys })" v-for="btn in attachButtons" :key="btn.name" :disabled="typeof btn?.props?.disabled === 'function'
          ? btn?.props?.disabled(selectedRowKeys)
          : btn?.props?.disabled ?? false
          ">{{ btn.name }}</a-button>
    </div>
    <a-table class="table-list" :dataSource="dataSource" :columns="_tableProps" :size="size" v-bind="{
      bordered: bordered,
      loading: localLoading,
      pagination: isPagination ? localPagination : false,
      rowSelection: isSelection ? localRowSelection : null,
      rowKey: 'id',
      align: align,
      scroll: { width: '100%' },
      ...$attrs,
    }" @resizeColumn="handleResizeColumn">
      <!-- 自定义渲染单元格 -->
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="!column.slot && Object.keys(record).includes(column.dataIndex)">
          <a-tooltip :title="record[column.dataIndex]">
            {{ record[column.dataIndex] }}
          </a-tooltip>
        </template>
        <!-- 序号 -->
        <template v-if="column.type === 'serial'">
          <a-tooltip>
            <template #title>{{
              (localPagination.current - 1) * localPagination.pageSize + index + 1
            }}</template>
            {{ (localPagination.current - 1) * localPagination.pageSize + index + 1 }}
          </a-tooltip>
        </template>
        <!-- 列插槽 -->
        <!-- <template v-else-if="columnSlots.includes(column.dataIndex)">
          <template v-for="slot in columnSlots" :key="slot">
            <slot :name="`${slot}`" v-bind="{ text, record, index, column }"></slot>
          </template>
        </template> -->
        <template v-else-if="column.slot && column.slot == column.dataIndex">
          <div>
            <a-tooltip color="#ffffff" v-if="column.hideTooltip ? false : true">
              <template #title>
                <div style="color: #000000; width: 100%; padding: 5px; white-space: pre-wrap">
                  <slot :name="column.slot" v-bind="{ text, record, index, column }"></slot>
                </div>
              </template>
              <slot :name="column.slot" v-bind="{ text, record, index, column }"></slot>
            </a-tooltip>

            <slot v-else :name="column.slot" v-bind="{ text, record, index, column }"></slot>
          </div>
        </template>
        <!-- 自定义列渲染 -->
        <template v-else-if="typeof column?.customRender === 'function'">
          <!-- 渲染customRender -->
          <component :is="column.customRender" v-bind="{ text, record, index, column }"></component>
        </template>
        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'operation'">
          <div style="display: flex; align-items: center; flex-wrap: nowrap"
            :style="{ justifyContent: column?.justifyContent || 'flex-start' }">
            <a-button class="custom-style" type="link"
              v-for="(item, itemIndex) in column.buttons" :key="itemIndex"
              @click="item.onclick({ text, record, index, column })" :style="{
                display: btnShow({ item, text, record, index, column })
                  ? 'inline-block'
                  : 'none',
                ...(item.style || {}),
              }">{{ item.name }}</a-button>
          </div>
        </template>
      </template>
      <!-- 插槽透传 -->
      <template v-for="(value, name) in $slots" v-slot:[name]="slotProps">
        <slot :name="name" v-bind="slotProps"></slot>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import {
    ref,
    reactive,
    computed,
    onMounted,
    useSlots,
    defineEmits,
    unref,
    watch,
  } from "vue";
  const props = defineProps({
    align: {
      type: String,
      default: "left",
    },
    tableData: {
      type: [Function, Array],
      default: () => [],
    },
    tableProps: {
      type: Array,
      default: () => [],
    },
    bordered: {
      type: Boolean,
      default: false,
    },
    total: {
      type: Number,
      default: 0,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    // 是否多选
    isSelection: {
      type: Boolean,
      default: false,
    },
    // 是否分页
    isPagination: {
      type: Boolean,
      default: true,
    },
    // 附属操作按钮
    attachButtons: {
      type: Array,
      default: () => [],
    },
    size: {
      type: String,
      default: "large",
    },
  });
  const emits = defineEmits(["refresh", "changePage", "selectedRow", "resetSelectedRow"]);
  const slots = useSlots();
  const columnSlots = ref([]);
  const createColumnSlots = () => {
    columnSlots.value = Object.keys(slots)
      .filter((x) => x.indexOf("column") !== -1)
      .map((x) => x.split("-")[1]);
  };

  const _tableProps = ref([]);
  watch(
    props.tableProps,
    (newVal, oldVal) => {
      if (newVal.length == 0) return;
      _tableProps.value = newVal.map((item: any) => {
        return {
          ellipsis: true,
          resizable: true,
          minWidth: 20,
          width: 80,
          maxWidth: 1500,
          align: "left",
          key: item.dataIndex,
          ...item,
        };
      });
    },
    { immediate: true, deep: true }
  );

  // 为了可以列宽的伸缩，暂时放弃计算属性的写法
  // const _tableProps = computed(() => {
  //   return props.tableProps.map((item: any) => {
  //     return {
  //       ellipsis: true,
  //       resizable: true,
  //       width: 100,
  //       minWidth: 10,
  //       maxWidth: 1200,
  //       align: "center",
  //       key: item.dataIndex,
  //       ...item,
  //     };
  //   });
  // });

  const btnShow = computed(() => {
    return ({ item, text, record, index, column }) => {
      return typeof item?.show == "function"
        ? item.show({ text, record, index, column })
        : item.show ?? true;
    };
  });

  // 列表数据
  const dataSource = ref([]);
  const localPagination = reactive({
    position: ["bottomRight"], // 位置
    current: 1, // 分页
    pageSize: 10, // 每页显示的条数
    total: 0, // 总条数
    showTotal: (total: number) => `共 ${total} 条记录`, // 显示总条数和当前数据范围
    showSizeChanger: true, // 是否可以改变每页显示的条数
    pageSizeOptions: ["10", "20", "30", "50", "100"], // 可选的每页显示条数
    showQuickJumper: true, // 是否可以快速跳转到指定页
    onChange: handlePageChange,
  });

  // 分页事件
  function handlePageChange(current: number, size: number) {
    // 清空选中状态
    resetSelect();
    localPagination.current = current;
    localPagination.pageSize = size;
    emits("changePage", { pageNum: current, pageSize: size });
    if (!Array.isArray(props.tableData)) {
      loadData({ current, pageSize: size });
    }
  }

  watch(
    () => props.tableData,
    () => {
      if (Array.isArray(props.tableData)) {
        refresh();
      }
    }
  );

  // 是否分页
  const isPagination = ref(false);
  watch(
    () => props.isPagination,
    (newVal, oldVal) => {
      isPagination.value = props.isPagination;
    },
    {
      immediate: true,
    }
  );
  // loading状态
  const localLoading = ref(false);
  const selectedRowKeys = ref([]);
  // 选择列
  const onSelectChange = (rowKeys) => {
    selectedRowKeys.value = rowKeys;
    emits("selectedRow", rowKeys);
  };
  const resetSelect = () => {
    selectedRowKeys.value = [];
    emits("resetSelectedRow", []);
  };

  const localRowSelection = computed(() => {
    return {
      selectedRowKeys: unref(selectedRowKeys),
      onChange: onSelectChange,
    };
  });

  const loadData = (pagination) => {
    localLoading.value = true;
    const params = isPagination.value
      ? {
        pageNo: pagination?.current ? pagination.current : localPagination.current,
        pageSize: pagination?.pageSize ? pagination.pageSize : localPagination.pageSize,
      }
      : {};
    if (!props.tableData) {
      dataSource.value = [];
      return;
    }
    if (Array.isArray(props.tableData)) {
      localPagination.total = props.total || 0;
      dataSource.value = props.tableData;
      localLoading.value = false;
    } else {
      props
        .tableData(params)
        .then((retObj) => {
          const { list, total, pageSize, pageNum } = retObj;
          isPagination.value = retObj.hasOwnProperty("list");
          if (isPagination.value) {
            localPagination.total = total || 0;
            localPagination.pageSize = pageSize;
            localPagination.pageNum = pageNum;
            dataSource.value = list?.length ? list : [];
            if (list?.length === 0 && localPagination.current > 1) {
              localPagination.current--;
              loadData();
            }
          } else {
            dataSource.value = retObj?.length ? retObj : [];
          }
        })
        .finally(() => (localLoading.value = false));
    }
  };

  // 刷新表格数据
  const refresh = (isInit = false) => {
    // 页码重置1
    if (isInit) {
      localPagination.current = 1;
      localPagination.total = 0;
      emits("refresh");
    }
    console.log(localPagination.current, localPagination.pageSize);
    loadData({ current: localPagination.current, pageSize: localPagination.pageSize });
  };

  function handleResizeColumn(width: number, col: { width: number }) {
    col.width = width;
  }

  onMounted(() => {
    createColumnSlots();
    loadData();
  });

  defineExpose({ refresh });
</script>

<style lang="less" scoped>
  .table-wrap {
    overflow-x: auto !important;
  }

  .table-list {
    overflow-x: auto !important;
  }

  .attach-buttons-wrap {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  /*有固定行的ant-design-vue 表格滑动样式*/
  :deep(.ant-table-fixed .ant-table-row-hover) {
    background: var(--table-row-hover) !important;
  }

  :deep(.ant-table-fixed .ant-table-row-hover > td) {
    background: var(--table-row-hover) !important;
  }

  // 没有固定行的表格个样式
  :deep(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td) {
    background-color: var(--table-row-hover) !important;
  }

  :deep(.ant-table-body .ant-table-row-hover) {
    background: var(--table-row-hover) !important;
  }

  :deep(.ant-table-body .ant-table-row-hover > td) {
    background: var(--table-row-hover) !important;
  }

  :deep(.ant-table-thead > tr > th) {
    padding: 12px 16px !important;
    background-color: var(--table-thead-color);
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 9px 16px !important;
  }

  :deep(.ant-table-measure-row) {
    display: none;
  }

  :deep(.custom-style) {
    padding: 0 !important;
    margin-right: 10px !important;
    // margin-top: 8px;
    word-break: break-all;
    word-wrap: break-word;
    height: 20px;
    width: auto;
    color: #209e85;
  }

  .table-btn {
    margin-right: 16px;
  }

  .table-btn:last-child {
    margin-right: 0;
  }

  :deep(.ant-table-wrapper .ant-table-pagination.ant-pagination) {
    margin-top: 16px;
  }

  :deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row-selected > td) {
    background-color: var(--table-row-hover) !important;
  }
</style>
