<template>
  <div class="wrapper">
    <a-card
      title="高原病知识图谱管理"
      :bordered="false"
      :bodyStyle="{ padding: '24px', minHeight: 'calc(100vh - 180px)' }"
    >
      <div class="clearfix">
        <a-form
          :model="formData"
          layout="inline"
          class="form-data-inline clearfix"
        >
          <a-form-item label="状态">
            <a-select
              style="width: 240px"
              mode="multiple"
              v-model:value="formData.status"
              placeholder="请选择"
            >
              <a-select-option
                :value="item.code"
                v-for="item in statusList"
                :key="item.id"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-input-group
              class="input-group-styles"
              compact
            >
              <a-select
                class="select-styles"
                v-model:value="selectKeyLabel"
                @select="handleSelectKeyLabel"
              >
                <a-select-option
                  :value="item.code"
                  v-for="item in levelEnum"
                  :key="item.code"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
              <a-input
                class="auto-input-styles"
                v-model:value="formData[selectKeyLabel]"
                style="width: 300px"
                placeholder="请输入节点内容"
              />
            </a-input-group>
          </a-form-item>
          <div class="float-right flex flex-1 flex-col w-70px btn-export">
            <a-button
              @click="handleClear"
              class="letter-text"
              style="margin-right: 16px"
              >清空</a-button
            >
            <a-button
              type="primary"
              class="letter-text"
              style="background-color: #209e85"
              @click="handleSearch"
              >搜索</a-button
            >
          </div>
        </a-form>
      </div>

      <div class="table">
        <table-list
          :isSelection="true"
          ref="myTable"
          :tableData="tableList"
          :tableProps="tableProps"
          :total="total"
          :attachButtons="attachButtons"
          @changePage="handleChangePage"
          @selectedRow="handleSelectedRow"
        >
          >
          <template #status="{ record }">
            <div>
              <a-tag
                style="border-radius: 4px"
                :color="getTagColor(record.status)"
              >
                {{ statusEnum[record.status] }}
              </a-tag>
              <a-tooltip v-if="record.status == 4">
                <template #title>
                  <div>
                    {{ record.reason }}
                  </div>
                </template>
                <InfoCircleOutlined
                  :style="{
                    color: '#cbd2c0',
                    fontSize: '16px'
                  }"
                />
              </a-tooltip>
            </div>
          </template>
        </table-list>
      </div>
    </a-card>

    <div>
      <a-drawer
        :getContainer="false"
        title="新增"
        width="530"
        :open="isOpenDrawer"
        @close="onClose"
        :closable="false"
        :footer-style="{ textAlign: 'right' }"
      >
        <template #extra>
          <span
            @click="onClose"
            style="cursor: pointer"
          >
            <CloseOutlined />
          </span>
        </template>
        <a-form
          ref="formRef"
          :model="drawerFormData"
          :rules="rules"
          :label-col="labelCol"
        >
          <a-form-item
            v-for="(label, index) in levelLabels"
            :key="index"
            :label="label"
            :name="`level${index + 1}Cn`"
          >
            <a-select
              placeholder="请填写节点信息"
              v-model:value="drawerFormData[`level${index + 1}Cn`]"
              :default-active-first-option="false"
              :not-found-content="null"
              allow-clear
              show-search
              show-arrow
              :filter-option="false"
              mode="SECRET_COMBOBOX_MODE_DO_NOT_USE"
              style="width: 100%"
              :options="dataList[`level${index + 1}List`]"
              :fieldNames="propsLabel"
              @search="(val) => handleSearchLevel(null, index + 1, val)"
              @change="
                (val, options) => {
                  handleChangeSelect({
                    value: val,
                    options: options,
                    index: index + 1
                  })
                }
              "
            >
            </a-select>
          </a-form-item>
          <a-form-item
            label="四级节点（属性详情）"
            name="level4Cn"
          >
            <a-input
              style="width: 100%"
              placeholder="请填写节点信息"
              v-model:value="drawerFormData.level4Cn"
            />
          </a-form-item>
          <a-form-item
            label="五级节点（数据来源）"
            name="level5Cn"
          >
            <a-input
              style="width: 100%"
              placeholder="请填写节点信息"
              v-model:value="drawerFormData.level5Cn"
            />
          </a-form-item>
          <a-form-item label="知识库链接">
            <a-select
              allow-clear
              show-search
              :default-active-first-option="false"
              show-arrow
              style="width: 100%"
              v-model:value="drawerFormData.guideId"
              :filter-option="false"
              :not-found-content="null"
              placeholder="请填写知识库链接"
              :fieldNames="propsLabel"
              :options="guideList"
              @search="getGuideList"
            >
            </a-select>
          </a-form-item>
        </a-form>
        <template #footer>
          <a-button
            style="margin-right: 20px"
            @click="onClose"
            >取消</a-button
          >
          <a-button
            type="primary"
            @click="onSubmit"
            >确定</a-button
          >
        </template>
      </a-drawer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, useTemplateRef, toRaw } from 'vue'
import type { UnwrapRef } from 'vue'
import TableList from '@/components/TableList/index.vue'
import _ from 'lodash'
import { Modal, Form, message } from 'ant-design-vue'
import { InfoCircleOutlined, CloseOutlined } from '@ant-design/icons-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { statusEnum, levelEnum, rejectPopUpWindow } from './index.config'
import { getDicList, getListPage, updatePage, getKnowledgeGuidePage, getKnowledgeGraphTable, addKnowledgeGuide, deleteKnowledgeGuide } from './index.api'
import { operationLog } from '@/api/log'

interface FormData {
  level1Cn: string
  level2Cn: string
  level3Cn: string
  level4Cn: string
  level5Cn: string
  status: string[]
}
let formData = reactive<FormData>({
  level1Cn: '',
  level2Cn: '',
  level3Cn: '',
  level4Cn: '',
  level5Cn: '',
  status: []
})
const tableList = ref([])
const tableProps = ref([
  {
    id: 1,
    title: '一级节点（疾病大类）',
    dataIndex: 'level1Cn',
    key: 'level1Cn',
    ellipsis: true
  },
  {
    id: 2,
    title: '二级节点（疾病名称）',
    dataIndex: 'level2Cn',
    key: 'level2Cn',
    ellipsis: true
  },
  {
    id: 3,
    title: '三级节点（疾病属性）',
    dataIndex: 'level3Cn',
    key: 'level3Cn',
    ellipsis: true
  },
  {
    id: 4,
    title: '四级节点（属性详情）',
    dataIndex: 'level4Cn',
    key: 'level4Cn',
    ellipsis: true,

    hideTooltip: true
  },
  {
    id: 5,
    title: '五级节点（来源）',
    dataIndex: 'level5Cn',
    key: 'level5Cn',
    ellipsis: true
  },
  {
    id: 6,
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    ellipsis: true,
    slot: 'status',
    width: 50,
    hideTooltip: true
  },
  {
    id: 7,
    title: '审核者',
    dataIndex: 'modifier',
    key: 'modifier',
    ellipsis: true,
    width: 50
  },
  {
    id: 8,
    title: '更新者',
    dataIndex: 'modifier',
    key: 'modifier',
    width: 50,
    ellipsis: true
  },
  {
    id: 9,
    title: '更新时间',
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    width: 80,
    ellipsis: true
  },
  {
    id: 99,
    title: '操作',
    dataIndex: 'operation',
    slot: 'operation',
    width: 70,
    buttons: [
      {
        name: '通过',
        onclick: ({ record }) => {
          let params = {
            status: 3,
            id: record.id
          }
          handleEditData(params)
        },
        show: ({ record }) => record.status == 2
      },
      {
        name: '删除',
        onclick: ({ record }) => {
          let params = {
            idList: record.id
          }
          handleDeleteData(params)
        },
        show: ({ record }) => record.status == 3
      },
      {
        name: '驳回',
        onclick: ({ record }) => {
          let { vnode, reason } = rejectPopUpWindow()
          Modal.confirm({
            title: '驳回',
            content: vnode,
            icon: null,
            wrapClassName: 'reject-modal',
            bodyStyle: {
              padding: '10px 15px'
            },
            onOk() {
              if (!reason.value) {
                return message.error('驳回理由不能为空')
              }
              let params = {
                reason: reason.value,
                status: 4,
                id: record.id
              }
              handleEditData(params)
            },
            onCancel() {
              console.log('Cancel')
            }
          })
        },
        show: ({ record }) => record.status == 2
      }
    ] // 0 正常  1 已删除 2 待审核 3 已审核 4 已驳回
  }
])
const statusList = ref([])
const selectKeyLabel = ref('level1Cn')
const total = ref(0)
const selectList = ref([])
const pagiNation = reactive({
  current: 1,
  size: 10
})
const attachButtons = ref([
  {
    name: '批量删除',
    type: 'default',
    props: {
      disabled: false
    },
    onclick: () => {
      if (selectList.value.length == 0) {
        return message.error('请选择需要删除的数据')
      }
      let params = {
        idList: selectList.value.join(',')
      }
      handleDeleteData(params)
    }
  },
  {
    name: '新增',
    props: {
      style: {
        backgroundColor: '#209E85'
      }
    },
    onclick: () => {
      handleSearchLevel(null, 1, '')
      getGuideList('')
    }
  }
])

watch(
  () => tableList.value,
  (newVal) => {
    attachButtons.value[0].props!.disabled = newVal?.length == 0
  },
  { deep: true, immediate: true }
)

function handleSelectedRow(value) {
  console.log(value, 'value')
  selectList.value = value
}
function handleSelectKeyLabel() {
  let data = {
    level1Cn: '',
    level2Cn: '',
    level3Cn: '',
    level4Cn: '',
    level5Cn: ''
  }
  formData = Object.assign(formData, data)
}
// 搜索事件
function handleSearch() {
  pagiNation.current = 1
  getList()
}

// 清空事件
function handleClear() {
  let data = {
    level1Cn: '',
    level2Cn: '',
    level3Cn: '',
    level4Cn: '',
    level5Cn: '',
    status: []
  }
  selectKeyLabel.value = 'level1Cn'
  pagiNation.current = 1
  pagiNation.size = 10
  formData = Object.assign(formData, data)
  getList()
}

// 分页list
async function getList() {
  let params = {
    ...pagiNation,
    ...toRaw(formData),
    status: formData.status && formData.status.length > 0 ? formData.status.join(',') : ''
  }
  getListPage(params)
    .then((res) => {
      if (res.code == 0) {
        tableList.value = res?.data?.records ?? []
        total.value = res?.data?.total ?? 0
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

// 分页事件
function handleChangePage({ pageNum, pageSize }) {
  pagiNation.current = pageNum
  pagiNation.size = pageSize
  getList()
}

async function getDicListData() {
  let params = {
    type: 'guideStatus'
  }
  await getDicList(params)
    .then((res) => {
      if (res.code == 0) {
        statusList.value = res?.data ?? []
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
// 通过/驳回
function handleEditData<T>(params: T) {
  updatePage(params)
    .then((res) => {
      if (res.code == 0) {
        console.log('OK')
        message.success('操作成功')
        getList()
        operationLogs()
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

// 删除
function handleDeleteData<T>(params: T) {
  deleteKnowledgeGuide(params)
    .then((res) => {
      if (res.code == 0) {
        console.log('OK')
        message.success('操作成功')
        getList()
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

const getTagColor = (status: number) => {
  switch (status) {
    case 4:
    case 1:
      return 'red'
    case 3:
      return 'green'
    case 2:
      return 'blue'
    default:
      return '' // 可以根据需要返回默认颜色
  }
}

// 新增表单
type GuideId = string | number | null
interface DrawerFormData {
  level1?: any
  level2?: any
  level3?: any
  level1Cn: string
  level2Cn: string
  level3Cn: string
  level4Cn: string
  level5Cn: string
  guideId: GuideId
}

const levelLabels = ref(['一级节点（疾病大类）', '二级节点（疾病名称）', '三级节点（属性详情）'])
let drawerFormData: UnwrapRef<DrawerFormData> = reactive({
  level1: null,
  level1Cn: '',
  level2: null,
  level2Cn: '',
  level3: null,
  level3Cn: '',
  level4Cn: '',
  level5Cn: '',
  guideId: null
})
const isOpenDrawer = ref(false)
const labelCol = { span: 8 }
const wrapperCol = { span: 14 }
const propsLabel = {
  label: 'name',
  value: 'id'
}
const formRef = useTemplateRef('formRef')
interface DataList {
  level1List: any[]
  level2List: any[]
  level3List: any[]
}
const dataList = reactive<DataList>({
  level1List: [],
  level2List: [],
  level3List: []
})
const guideList = ref([])

/**
 * @params {value} 当前选中的id
 * @params {options} 当前选中的options
 * @params {idKey} 当前选中的 id 的 key
 * @params {labelKey} 当前选中的 label 的 key
 * @params {list} 当前选中的 options 的 list
 */
const handleChangeSelect = <T,>(data: T) => {
  console.log(data, 'data')
  const { value, options, index } = data
  const idKey = `level${index}`
  const labelKey = `level${index}Cn`

  if (!value) {
    // 更新 drawerFormData
    // 如果清空了或者没有的时候，他的下级为空
    drawerFormData[`level${index + 1}` as keyof typeof drawerFormData] = ''
    drawerFormData[`level${index + 1}Cn` as keyof typeof drawerFormData] = ''
    dataList[`level${index + 1}List` as keyof typeof dataList] = []
  }

  const list = dataList[`level${index}List` as keyof typeof dataList]
  // 更新 drawerFormData
  drawerFormData[labelKey as keyof typeof drawerFormData] = list?.length === 0 ? value : options?.name
  drawerFormData[idKey as keyof typeof drawerFormData] = list?.length > 0 && typeof value == 'number' ? value : '' // 仅在 list不为空 和 value 为 id 的时候设置 idKey

  const nextLevel = options?.level + 1
  if (nextLevel <= 3) {
    handleSearchLevel(value, nextLevel)
  }
  if (nextLevel <= 1) {
    handleSearchLevel(null, 1, '')
  }
}
const handleSearchLevel = (parentId = null, level = 1, name = '') => {
  drawerFormData[`level${level}Cn` as keyof typeof drawerFormData] = name
  const params = {
    parentId: parentId,
    level: level,
    name: name,
    size: 100,
    current: 1
  }

  if (level > 1 && !parentId && !name) {
    params.parentId = level == 2 ? drawerFormData.level1 : level == 3 ? drawerFormData.level2 : params.parentId
  }

  const levelListMap = {
    1: 'level1List',
    2: 'level2List',
    3: 'level3List'
  }

  const listKey = levelListMap[level]

  if (!listKey) return

  // 重置下一级列表
  dataList[listKey as keyof typeof dataList] = []
  getLevelListPage(params, listKey)
}

const getLevelListPage = _.debounce(async function (params, list) {
  await getKnowledgeGraphTable(params)
    .then((res) => {
      if (res.code == 0) {
        dataList[list] = res?.data?.records ?? []
        if (!isOpenDrawer.value) {
          isOpenDrawer.value = true
        }
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}, 200)

const getGuideList = _.debounce(function (name = '') {
  let params = {
    name,
    current: 1,
    size: 100
  }
  getKnowledgeGuidePage(params)
    .then((res) => {
      if (res.code == 0) {
        guideList.value = res?.data?.records
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}, 200)

const rules: Record<string, Rule[]> = {
  level1Cn: [
    { required: true, message: '请填写一级分类', trigger: 'blur' },
    { min: 0, max: 50, message: '一级分类最多不超过500个字符', trigger: 'blur' }
  ],
  level2Cn: [
    { required: true, message: '请填写二级分类', trigger: 'blur' },
    { min: 0, max: 50, message: '二级分类最多不超过500个字符', trigger: 'blur' }
  ],
  level3Cn: [
    { required: true, message: '请填写三级分类', trigger: 'blur' },
    { min: 0, max: 50, message: '三级分类最多不超过500个字符', trigger: 'blur' }
  ],
  level4Cn: [
    { required: true, message: '请填写四级分类', trigger: 'blur' },
    { min: 0, max: 500, message: '四级分类名称最多不超过500个字符', trigger: 'blur' }
  ],
  level5Cn: [
    { required: true, message: '请填写五级分类', trigger: 'blur' },
    { min: 0, max: 500, message: '五级分类最多不超过500个字符', trigger: 'blur' }
  ]
}

const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      let params = {
        ...toRaw(drawerFormData)
      }
      console.log(params, 'params')
      addKnowledgeGuide(params)
        .then((res) => {
          if (res.code == 0) {
            message.success('操作成功')
            onClose()
            getList()
            operationLogsUpdate()
          }
        })
        .catch((err) => {
          console.log(err, 'err')
        })
    })
    .catch((error) => {
      console.log('error', error)
    })
}

const { resetFields } = Form.useForm(drawerFormData, rules)
const onClose = () => {
  dataList.level1List = []
  dataList.level2List = []
  dataList.level3List = []
  resetFields()
  // 批量更新 drawerFormData
  Object.assign(drawerFormData, {
    level1Cn: '',
    level2Cn: '',
    level3Cn: '',
    level4Cn: '',
    level5Cn: '',
    guideId: null
  })
  isOpenDrawer.value = false
}

onMounted(() => {
  getDicListData()
  getList()
})

const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '审核高原病知识图谱',
    ip: baseIP,
    type: '高原病数据审核'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
const operationLogsUpdate = async () => {
  const obj = {
    category: '更新高原病知识图谱',
    ip: baseIP,
    type: '高原病数据审核'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  // display: flex;
  // flex-direction: column;
  // padding: 0 10px;
}

:deep(.ant-form-inline .ant-form-item) {
  margin-bottom: 24px;
  margin-right: 24px;
}

.form-data-inline {
  width: 100%;
  display: inline-flex;
}

.btn-export {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-bottom: 24px;
}

.wrapper-title {
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9e9e9;
}

.table {
  width: 100%;
}

.upload-btn {
  margin-bottom: 16px;
}

:deep(.select-styles .ant-select-selector) {
  border-radius: 4px 0 0 4px !important;
  width: 200px;
  background-color: var(--select-selector-styles);
}

:deep(.input-group-styles .auto-input-styles) {
  border-radius: 0px 4px 4px 0 !important;
}

:deep(.ant-tag) {
  margin-inline-end: 4px;
}
</style>
