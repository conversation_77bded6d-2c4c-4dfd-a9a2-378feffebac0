import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import vue from '@vitejs/plugin-vue'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import AutoImport from 'unplugin-auto-import/vite'
import os from 'os'

function getNetworkIp() {
  let needHost = ''
  try {
    const network = os.networkInterfaces()
    for (const dev in network) {
      const iface = network[dev]
      if (iface) {
        // 添加这个条件检查，确保iface有被定义
        for (let i = 0; i < iface.length; i++) {
          const alias = iface[i]
          if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
            needHost = alias.address
          }
        }
      }
    }
  } catch (e) {
    needHost = 'localhost'
  }
  return needHost
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }): any => {
  // 加载 .env 文件
  const env = loadEnv(mode, process.cwd())
  // process.env.NODE_ENV === 'production' ? '/ams-front' : '/'
  return {
    base: env.VITE_BASE_URL ? env.VITE_BASE_URL : '/',
    esbuild: {
      drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : []
    },
    build: {
      minify: 'esbuild',
      target: 'ESNext', // 编译目标
      sourcemap: true,
      outDir: 'ams-front',
      assetsDir: 'assets', // 静态资源目录（相对于outDir）
      cssCodeSplit: true, // 启用 CSS 代码分割
      rollupOptions: {
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
          assetFileNames: '[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
          // 最小化拆分包
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString()
            }
          }
        }
      }
    },
    root: process.cwd(),
    resolve: {
      alias: [
        {
          find: '@',
          replacement: path.resolve(__dirname, './src') // 路径别名
        },
        { find: 'views', replacement: '/src/views' },
        { find: 'components', replacement: '/src/components' }
      ],
      extensions: ['.js', '.json', '.ts'] // 使用路径别名时想要省略的后缀名，可以自己 增减
    },
    assetsInclude: ['./src/assets', '"**/*.docx"', './src/styles'],
    plugins: [
      vue(),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(__dirname, 'src/assets/icons')],
        // 指定symbolld格式
        symbolId: 'icon-[dir]-[name]'
      }),
      AutoImport({
        imports: ['vue', 'vue-router', 'vue-i18n', '@vueuse/core', 'pinia'],
        dts: 'types/auto-imports.d.ts', // 使用typescript，需要指定生成对应的d.ts文件或者设置为true,生成默认导入d.ts文件
        dirs: ['src/stores', 'src/components', 'src/hooks']
      })
    ],
    server: {
      host: '0.0.0.0',
      port: 3004,
      // 是否开启 https
      https: false,

      // // 配置https代理与证书
      // https: {
      //   key: fs.readFileSync('cert/8982715__nsfocus.com.key'),
      //   cert: fs.readFileSync('cert/8982715__nsfocus.com.pem')
      // },// 是否开启 https
      open: false, // 是否自动在浏览器打开
      //这里的ip和端口是前端项目的;下面为需要跨域访问后端项目
      proxy: {
        '/api': {
          // '/api'是代理标识，用于告诉node，url前面是/api的就是使用代理的
          target: env.VITE_BASE_API1, //这里填入你要请求的接口的前缀
          ws: true, //代理websocked
          changeOrigin: true, //是否跨域
          secure: true, //是否https接口
          rewrite: (path: string) => path.replace(/^\/api/, '')
        },
        '/cas': {
          // '/cas'是代理标识，用于告诉node，url前面是 /cas 的就是使用代理的
          target: env.VITE_BASE_API2, //这里填入你要请求的接口的前缀
          ws: true, //代理 websocked
          changeOrigin: true, //是否跨域
          secure: true, //是否 https 接口
          rewrite: (path: string) => path.replace(/^\/cas/, ''),
        }
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/antd-overflow.scss";`
        },
        less: {
          javascriptEnabled: true,
          modifyVars: {
            hack: `true; @import (reference) "${path.resolve('src/styles/main.css')}";`
          }
        }
      }
    },
    // 设置环境变量(重点)
    define: {
      // 'import.meta.env.BASE_IP': JSON.stringify(`http://${getNetworkIp()}:${process.env.PORT || 3003}`)  //完整地址
      'import.meta.env.BASE_IP': JSON.stringify(`${getNetworkIp()}`)
    }
  }
})
