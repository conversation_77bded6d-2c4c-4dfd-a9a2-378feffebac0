apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: '1'
  labels:
    k8s-app: ${K8S_POD_NAME}
  name: ${K8S_POD_NAME}
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: ${K8S_POD_NAME}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      name: ${K8S_POD_NAME}
      creationTimestamp: null
      labels:
        k8s-app: ${K8S_POD_NAME}
    spec:
      containers:
        image: ${IMAGE_NAME}:${IMAGE_TAG}
        imagePullPolicy: IfNotPresent
        name: ${K8S_POD_NAME}
        resources: {}
        securityContext:
          allowPrivilegeEscalation: false
          capabilities: {}
          privileged: false
          readOnlyRootFilesystem: false
          runAsNonRoot: false
        stdin: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        tty: true
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30

---
apiVersion: v1
kind: Service
metadata:
  labels:
    k8s-app: ${K8S_POD_NAME}
  name: ${K8S_POD_NAME}
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    k8s-app: ${K8S_POD_NAME}
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}
