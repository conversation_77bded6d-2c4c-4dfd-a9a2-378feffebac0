variables:
  IMAGE_NAME: harbor.yitu-med.info/big-data-platform/ams-front-end
  K8S_POD_NAME: ams-front-end
  K8S_CLUSTER: test-bigdata
  K8S_NAMESPACE: bigdata
include:
  - project: epi/cicd/includes
    file: /node/docker.yml
.base:
  tags:
    - test-inner
.npm_build:
  image: harbor.yitu-med.info/devops/node:16-alpine3.17
  script:
    - npm i n pnpm@8 -g
    - npm set registry http://*************:4873/
    - npm i
    - npm run build
  artifacts:
    paths:
      - ams-front
    expire_in: 1 h

sonar_for_release:
  extends:
    - .base
    - .sonar_node
    - .for_release
  only:
    variables:
      - $ENABLE_RELEASE_SONAR

sonar_for_tag:
  extends:
    - .base
    - .sonar_node
    - .for_tag
  only:
    variables:
      - $ENABLE_RELEASE_SONAR

sonar_develop_branch:
  extends:
    - .base
    - .sonar_node
    - .for_develop_branch
  only:
    variables:
      - $ENABLE_DEVELOP_SONAR
