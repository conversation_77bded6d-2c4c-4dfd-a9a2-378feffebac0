import { createApp } from 'vue'
import App from './App.vue'
// import { router } from './router/router'
import router from './router/index'
import store from './store/index'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

import './styles/reset.css'
import './styles/style.css'
import './styles/antd-overflow.less'
import './styles/main.css'
import './styles/transition.css'

import * as echarts from 'echarts'

// svg 组件
import 'virtual:svg-icons-register' // 引入注册脚本
import SvgIcon from '@/components/svgIcon/index.vue' // 引入组件

// 自定义缩放指令
import sizeDirect from './utils/sizeDirect'

// import 'echarts/map/js/china.js'
const app = createApp(App)
// 全局挂载 echarts
app.config.globalProperties.$echarts = echarts

app.component('SvgIcon', SvgIcon)
app.directive('size-direct', sizeDirect)

app.use(Antd)

app.use(store)

// 导入初始化函数
import init from './common/init'
async function install(app: any) {
  await init()
  console.log('init success')
  // 等菜单数据拿到之后再开始加载router和挂载app
  app.use(router)
  app.mount('#app')
}

install(app)