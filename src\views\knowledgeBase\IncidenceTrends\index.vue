<template>
    <div class="basic-wrapper">
        <!-- 筛选 -->
        <div class="filter-wrapper clearfix">
            <!-- form -->
            <a-card class="table-screen clearfix" title="高原肺水肿趋势预测" :bordered="false"
                :bodyStyle="{ padding: '0 24px', minHeight: 'calc(100vh - 180px)' }">

            </a-card>
        </div>

        <div style="background-color: #f8fafc; margin: 16px 0; width: 100%;">
            <a-row :gutter="24" class="pie-row">
                <a-col :span="12" style="padding-left: 12px">
                    <div class="card">
                        <div>
                            <p>高原肺水肿预测模型介绍</p>
                            <span>
                                高原肺水肿（HAPE）是一种严重的高原病，其致命风险主要源于诊断不准确和治疗延误。
                                <br />
                                基于血液学参数和人口统计学变量，开发了一种用于诊断HAPE的列线图模型，可以方便地用于HAPE的诊断。在应急环境有限的高海拔地区，可以为医护人员提供快速可靠的诊断支持，帮助他们做出更好的治疗决策。</span>
                            <p>受试者的纳入标准:</p>
                            <span>
                                （1）常住低海拔居民的汉族人<br />
                                （2）以前没有患过高原肺水肿<br />
                                （3）进入高海拔地区前无肺部疾病（肺栓塞和肺炎）、心血管疾病（心力衰竭）、严重精神障碍
                            </span>
                            <p>模型性能:</p>
                            <span>
                                区分度：<br />

                                （1）训练队列的AUC为0.787（95% CI [0.757-0.817]）<br />

                                （2）验证队列的AUC为0.833（95% CI [0.793-0.874]）<br />
                                准确性：<br />

                                （1）训练组的准确度为70.95%<br />

                                （2）验证组的准确度为74.17%
                            </span>
                            <p>参考来源：</p>
                            <span>
                                [1] Li Q, Xu Z, Gong Q, Shen X. 2024. Clinical characteristics and a diagnostic model
                                for high-altitude pulmonary edema in habitual low altitude dwellers. PeerJ 12:e18084
                            </span>
                        </div>
                    </div>
                </a-col>
                <a-col :span="12" style="padding-left: 12px">
                    <div class="typeCategory card">
                        <div>
                            <a-spin style="height: 100%" tip="计算中，请稍侯..." :spinning="loading" v-show="isShow">
                                <p style="text-align: center;align-items: center;height: 30px;" v-if="loading==false">
                                    
                                        高原肺水肿风险发病风险为：{{ colors }}
                                    <!-- <span v-if="colors >= 0 && colors <= 30" style="color:green;font-size: 16px;">{{
                                        colors
                                    }}%</span>
                                    <span v-else-if="colors > 30 && colors <= 60"
                                        style="color:chocolate;font-size: 16px;">{{
                                            colors
                                        }}%</span>
                                    <span v-else-if="colors > 60 && colors <= 100" style="color:red;font-size: 16px;">{{
                                        colors
                                    }}%</span> -->
                                    
                                </p>
                                <p v-else  style="height: 30px;width: 100%;">

                                </p>
                            </a-spin>

                            <div class="lines" v-show="isShow" ></div>

                            <span>
                                <a-form :model="formState" :rules="rules" ref="formRef" v-bind="formItemLayout">
                                    <a-form-item label="性别" name="gender" required>
                                        <a-radio-group v-model:value="formState.gender">
                                            <a-radio value="male">男</a-radio>
                                            <a-radio value="female">女</a-radio>
                                        </a-radio-group>
                                    </a-form-item>
                                    <a-form-item label="年龄" name="age" class="formItemClass">
                                        <!-- suffix="岁" -->
                                        <a-input v-model:value="formState.age" placeholder="请输入"
                                            @input="validateAge"></a-input>
                                        <!-- <span class="suffixSpan">岁</span> -->
                                    </a-form-item>
                                    <a-form-item label="身高" name="height" required class="formItemClass">
                                        <!-- suffix="m" -->
                                        <a-input v-model:value="formState.height" placeholder="请输入"
                                            @input="validateNumber('height')"></a-input><span
                                            class="suffixSpan">m</span>
                                    </a-form-item>

                                    <a-form-item label="体重" name="weight" required class="formItemClass">
                                        <!-- suffix="kg" -->
                                        <a-input v-model:value="formState.weight" placeholder="请输入"
                                            @input="validateNumber('weight')" /><span class="suffixSpan">kg</span>
                                    </a-form-item>

                                    <a-form-item label="BMI" name="bmi" required class="formItemClass">
                                        <a-input v-model:value="formState.bmi" placeholder="请输入" disabled />
                                    </a-form-item>

                                    <a-form-item label="舒张压" name="diastolicPressure" required class="formItemClass">
                                        <!-- suffix="mmHg" -->
                                        <a-input v-model:value="formState.diastolicPressure" placeholder="请输入"
                                            @input="validateNumber('diastolicPressure')" /><span
                                            class="suffixSpan">mmHg</span>
                                    </a-form-item>

                                    <a-form-item label="平均动脉压" name="meanArterialPressure" class="formItemClass">
                                        <!-- suffix="mmHg"  -->
                                        <a-input v-model:value="formState.meanArterialPressure"
                                            @input="validateNumber('meanArterialPressure')" placeholder="请输入" /><span
                                            class="suffixSpan">mmHg</span>
                                    </a-form-item>

                                    <a-form-item label="白细胞" name="whiteBloodCell" required class="formItemClass">
                                        <!-- suffix="10^9/L" -->
                                        <a-input v-model:value="formState.whiteBloodCell" placeholder="请输入"
                                            @input="validateNumber('whiteBloodCell')" /><span
                                            class="suffixSpan">10^9/L</span>
                                    </a-form-item>

                                    <a-form-item label="淋巴细胞百分比" name="lymphocytePercentage" required
                                        class="formItemClass">
                                        <!-- suffix="%" -->
                                        <a-input v-model:value="formState.lymphocytePercentage"
                                            @input="validateNumber('lymphocytePercentage')" placeholder="请输入" /><span
                                            class="suffixSpan">%</span>
                                    </a-form-item>

                                    <a-form-item label="血细胞比容" name="hematocrit" required class="formItemClass">
                                        <!-- suffix="%" -->
                                        <a-input v-model:value="formState.hematocrit" placeholder="请输入"
                                            @input="validateNumber('hematocrit')" /><span class="suffixSpan">%</span>
                                    </a-form-item>

                                    <a-form-item label="平均红细胞体积" name="meanRedCellVolume" required
                                        class="formItemClass">
                                        <!-- suffix="fl"  -->
                                        <a-input v-model:value="formState.meanRedCellVolume" placeholder="请输入"
                                            @input="validateNumber('meanRedCellVolume')" /><span
                                            class="suffixSpan">fl</span>
                                    </a-form-item>

                                    <a-form-item label="平均红细胞血红蛋白浓度" name="meanHemoglobinConcentration" required
                                        class="formItemClass">
                                        <!-- suffix="g/L"  -->
                                        <a-input v-model:value="formState.meanHemoglobinConcentration"
                                            @input="validateNumber('meanHemoglobinConcentration')"
                                            placeholder="请输入" /><span class="suffixSpan">g/L</span>
                                    </a-form-item>

                                    <a-form-item label="血小板计数" name="plateletCount" required class="formItemClass">
                                        <!-- suffix="10^9/L" -->
                                        <a-input v-model:value="formState.plateletCount" placeholder="请输入"
                                            @input="validateNumber('plateletCount')" /><span
                                            class="suffixSpan">10^9/L</span>
                                    </a-form-item>

                                    <a-form-item label="平均血小板体积" name="meanPlateletVolume" required
                                        class="formItemClass">
                                        <!-- suffix="fl" -->
                                        <a-input v-model:value="formState.meanPlateletVolume" placeholder="请输入"
                                            @input="validateNumber('meanPlateletVolume')" /><span
                                            class="suffixSpan">fl</span>
                                    </a-form-item>
                                    <span>
                                        <a-button style="margin-left: 20px;float: right;" type="primary"
                                            @click="submitForm">提交</a-button>
                                        <a-button style="margin-left: 10px;float: right;"
                                            @click="resetForm">清空</a-button>
                                    </span>


                                </a-form>
                            </span>
                        </div>
                    </div>
                </a-col>
            </a-row>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, toRaw, getCurrentInstance, watch } from 'vue'
import { message } from 'ant-design-vue'
import { operationLog } from '@/api/log'

const isShow=ref<any>(false)
// const validateNumber = (field: keyof typeof formState) => {
//     let value = formState[field];
//     // 只允许输入正数
//     if (!/^\d*\.?\d+$/.test(value)) {
//         formState[field] = value.replace(/[^0-9.]/g, ''); // 只保留数字和小数点
//     }
//     if (parseFloat(formState[field]) <= 0) {
//         formState[field] = '';
//     }
// };
const validateNumber = (field: keyof typeof formState) => {
    let value = formState[field];
    // 允许输入 "0" 或 "0." 开头
    if (!/^\d*\.?\d*$/.test(value)) {
        formState[field] = value.replace(/[^0-9.]/g, ''); // 只保留数字和小数点
    }
    // 避免多余的小数点
    if ((formState[field].match(/\./g) || []).length > 1) {
        formState[field] = formState[field].slice(0, -1);
    }
    // 允许 "0"，但防止负数
    if (parseFloat(formState[field]) < 0) {
        formState[field] = '';
    }
};
const validateAge = () => {
    let value = formState.age;
    // 只保留数字
    formState.age = value.replace(/\D/g, '');
    // 避免首字符为 0 的无效情况（如 "012"）
    if (formState.age.length > 1 && formState.age.startsWith('0')) {
        formState.age = formState.age.replace(/^0+/, '');
    }
    // 防止负数，允许 0
    if (formState.age === '') {
        formState.age = '0';
    }
};

const loading = ref<any>(false)
const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 12 },
};
const colors = ref<any>(0)
const formRef = ref();
const formState = reactive({
    gender: undefined,
    age: '',
    height: '',
    weight: '',
    bmi: '',
    diastolicPressure: '',
    meanArterialPressure: '',
    whiteBloodCell: '',
    lymphocytePercentage: '',
    hematocrit: '',
    meanRedCellVolume: '',
    meanHemoglobinConcentration: '',
    plateletCount: '',
    meanPlateletVolume: '',
    countTotal: 0
});

const rules = {
    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
    height: [{ required: true, message: '请输入身高', trigger: 'blur' }],
    weight: [{ required: true, message: '请输入体重', trigger: 'blur' }],
    bmi: [{ required: true, message: '请输入BMI', trigger: 'blur' }],
    diastolicPressure: [{ required: true, message: '请输入舒张压', trigger: 'blur' }],
    whiteBloodCell: [{ required: true, message: '请输入白细胞数量', trigger: 'blur' }],
    lymphocytePercentage: [{ required: true, message: '请输入淋巴细胞百分比', trigger: 'blur' }],
    hematocrit: [{ required: true, message: '请输入血细胞比容', trigger: 'blur' }],
    meanRedCellVolume: [{ required: true, message: '请输入平均红细胞体积', trigger: 'blur' }],
    meanHemoglobinConcentration: [{ required: true, message: '请输入平均红细胞血红蛋白浓度', trigger: 'blur' }],
    plateletCount: [{ required: true, message: '请输入血小板计数', trigger: 'blur' }],
    meanPlateletVolume: [{ required: true, message: '请输入平均血小板体积', trigger: 'blur' }],
};
// 监听身高和体重的变化，自动计算 BMI
watch([() => formState.height, () => formState.weight], ([newHeight, newWeight]) => {
    const height = parseFloat(newHeight);
    const weight = parseFloat(newWeight);
    if (height > 0 && weight > 0) {
        formState.bmi = (weight / (height * height)).toFixed(2); // 保留两位小数
    } else {
        formState.bmi = ''; // 身高或体重无效时清空 BMI
    }
});

const calculateCountTotal = () => {
    let total = 0;

    // 性别
    if (formState.gender === 'male') total += 10;

    // BMI 计算
    const bmi = formState.bmi
    if (bmi >= 18.5 && bmi <= 24.9) total += 5;
    if (bmi >= 25) total += 10;

    // 舒张压
    let diastolic = parseFloat(formState.diastolicPressure) || 0;
    if (diastolic >= 160) {
        total += 12;
    } else {
        total += diastolic * 0.075;
    }

    // 白细胞
    let wbc = parseFloat(formState.whiteBloodCell) || 0;
    if (wbc >= 80) {
        total += 72;
    } else {
        total += wbc * 0.9;
    }

    // 淋巴细胞百分比
    let lymph = parseFloat(formState.lymphocytePercentage) || 0;
    if (lymph <= 55) {
        total += (55 - lymph) * 0.25;
    } else {
        total += 13.75;
    }

    // 血细胞比容
    let hematocrit = parseFloat(formState.hematocrit) || 0;
    if (hematocrit <= 80) {
        total += (80 - hematocrit) * 0.75;
    } else {
        total += 0;
    }

    // 平均红细胞体积
    let mcv = parseFloat(formState.meanRedCellVolume) || 0;
    if (mcv <= 120) {
        total += (120 - mcv) * 0.45;
    }

    // 平均红细胞血红蛋白浓度
    let mchc = parseFloat(formState.meanHemoglobinConcentration) || 0;
    if (mchc >= 500) {
        total += 25;
    } else {
        total += mchc * 0.05;
    }

    // 血小板计数
    let platelet = parseFloat(formState.plateletCount) || 0;
    if (platelet <= 500) {
        total += (500 - platelet) * 0.052;
    }

    // 平均血小板体积
    let mpv = parseFloat(formState.meanPlateletVolume) || 0;
    if (mpv >= 180) {
        total += 100;
    } else {
        total += mpv * 0.56;
    }

    formState.countTotal = total;
};

// const submitForm = () => {
//     formRef.value.validate().then(() => {
//         calculateCountTotal();
//         console.log('表单提交数据:', formState);
//         loading.value = false
//         message.success('提交成功');
//     }).catch(error => {
//         console.log('表单验证失败:', error);
//         loading.value = true
//         message.warning('请填写所有必填内容后进行提交预测')
//     });
// };
let countTotal = 0;
const submitForm = () => {
    let countTotal = 0;
    formRef.value.validate().then(() => {
        // 计算 countTotal
        isShow.value=true
        // if (formState.gender === "male") countTotal += 10;

        // const bmi = parseFloat(formState.bmi);
        // if (bmi >= 18.5 && bmi <= 24.9) countTotal += 5;
        // else if (bmi >= 25) countTotal += 10;

        // const diastolicPressure = parseFloat(formState.diastolicPressure);
        // if (diastolicPressure > 0 && diastolicPressure <= 160) {
        //     countTotal += diastolicPressure * 0.075;
        // } else if (diastolicPressure > 160) {
        //     countTotal += 12;
        // }

        // const whiteBloodCell = parseFloat(formState.whiteBloodCell);
        // if (whiteBloodCell > 0 && whiteBloodCell < 80) {
        //     countTotal += whiteBloodCell * 0.9;
        // } else if (whiteBloodCell >= 80) {
        //     countTotal += 72;
        // }

        // const lymphocytePercentage = parseFloat(formState.lymphocytePercentage);
        // if (lymphocytePercentage < 55) {
        //     countTotal += 13.75 + (55 - lymphocytePercentage) * 0.25;
        // }

        // const hematocrit = parseFloat(formState.hematocrit);
        // if (hematocrit < 80) {
        //     countTotal += (80 - hematocrit) * 0.75;
        // } else {
        //     countTotal += 0;
        // }

        // const meanRedCellVolume = parseFloat(formState.meanRedCellVolume);
        // if (meanRedCellVolume < 120) {
        //     countTotal += (120 - meanRedCellVolume) * 0.45;
        // }

        // const meanHemoglobinConcentration = parseFloat(formState.meanHemoglobinConcentration);
        // if (meanHemoglobinConcentration < 500) {
        //     countTotal += meanHemoglobinConcentration * 0.05;
        // } else {
        //     countTotal += 25;
        // }

        // const plateletCount = parseFloat(formState.plateletCount);
        // if (plateletCount < 500) {
        //     countTotal += (500 - plateletCount) * 0.052;
        // }

        // const meanPlateletVolume = parseFloat(formState.meanPlateletVolume);
        // if (meanPlateletVolume > 0 && meanPlateletVolume < 180) {
        //     countTotal += meanPlateletVolume * 0.56;
        // } else if (meanPlateletVolume >= 180) {
        //     countTotal += 100;
        // }

        calculateCountTotal();
        countTotal=formState.countTotal
        loading.value = true
        setTimeout(() => { 
            
            // 计算 colors 值
            if (countTotal <= 28) {
                colors.value = "小于1%";
            } else if (countTotal <= 44) {
                colors.value = "小于等于5%";
            } else if (countTotal <= 50) {
                colors.value = "5%～10%";
            } else if (countTotal <= 60) {
                colors.value = "10%~25%";
            } else if (countTotal <= 68) {
                colors.value = "25%~40%";
            } else if (countTotal <= 72) {
                colors.value = "40%~55%";
            } else if (countTotal <= 78) {
                colors.value = "55%~70%";
            } else if (countTotal <= 85) {
                colors.value = "70%~85%";
            } else if (countTotal <= 96) {
                colors.value = "85%~95%";
            } else if (countTotal <= 112) {
                colors.value = "95%~99%";
            } else {
                colors.value = "大于99%";
            }
            loading.value = false;
            console.log("计算结果 countTotal:", countTotal);
            message.success('提交成功');
        }, 3000)

    }).catch((error: any) => {
        
        loading.value = true
        isShow.value=false
        console.log("表单验证失败:", error);
        message.warning("请填写所有必填内容后进行提交预测");
    });
};
const resetForm = () => {
    formRef.value.resetFields();
    countTotal = 0;
    isShow.value=false
};


const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
    const obj = {
        category: '高原病发病趋势预测',
        ip: baseIP,
        type: '高原病知识库'
    }
    await operationLog(obj)
        .then((res) => {
            if (res?.code == 0) {
            }
        })
        .catch((err) => {
            console.log(err, 'err')
        })
}
onMounted(() => {
    operationLogs()
})
</script>

<style lang="less" scoped>
.basic-wrapper {
    width: 100%;
    // min-height: 100vh;
    position: relative;
    // background-color: #f8fafc;
}



.typeCategory {
    border-radius: 8px;
    padding: 16px 24px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    // margin-bottom: 16px;
    width: 100%;
    height: 100%;
    position: relative;
    // border: 0.5px solid #e2e8f0;
    box-shadow: none;
}

.my-charts {
    width: 100%;
    // height: calc(100% - 40px);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .my-charts-box {
        width: 100%;
        height: 100%;
    }

    .no-data-available {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        color: #5e6580;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
    }

    .userSearchCounts {
        position: absolute;
        top: 2%;
        left: 0;
        margin-left: 50px;
        font-size: 16px;
        color: #07123C;
        font-weight: 600;
    }
}

p {
    font-size: 16px;
    font-weight: bold;
    color: #07123c;
    font-family: 'Noto Sans SC';
}

.card {
    border-radius: 8px;
    padding: 16px 24px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    // margin-bottom: 16px;
    height: 100%;
    // height: calc(100vh - 180px);
    // height: calc(100vh - 180px);

    position: relative;
    // border: 0.5px solid #e2e8f0;
    box-shadow: none;
    overflow: scroll;

    p {
        // display: inline-block;
        margin: 30px 0;
    }

    span {
        margin: 30px 0;
        font-size: 14px;
    }
}

.lines {
    width: 100%;
    margin: 30px 0;
    border: 0.1px solid rgb(224, 219, 219);
}

.iconDesc {
    // position: absolute;
    // top: 20px;
    // left: 300px;
    margin-left: 10px;
}

.selectType {
    position: absolute;
    top: 10px;
    right: 5%;
}

.topLeft {
    display: flex;
    flex-wrap: wrap;
    /* justify-content: space-between */
}

.selectTypeYear {
    border: none;
    color: #00b929;

    ::v-deep .ant-select-selector {
        border: none;
        color: #00b929;
    }
}

.topLeftTitle {
    margin-bottom: 8px;
    font-size: 16px;
    color: #07123c;
    font-weight: bold;
    font-family: 'Noto Sans SC';
    width: 100%;
}

.formsTopLeft {
    width: 100%;
}

.diseaseTypeSelectTopLeft {
    width: 50%;
}

.charts-top-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .charts-title {
        color: #07123c;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        margin-left: 22px;

        .title-tip {
            margin-left: 8px;
        }
    }

    .charts-btn-detail {
        display: inline-flex;
        align-items: center;
        justify-self: center;
        border-radius: 4px !important;
        border: 1px solid #e2e8f0;
        background: #fff;
        margin-right: 20px;

        .anticon-search {
            margin-bottom: 0;
        }
    }
}

.item-charts-list {
    width: 100%;
    height: calc(100% - 20px);
}

.chart-container-custom {
    height: 100%;
    margin-top: -25px;
}

.formItemClass {
    position: relative;
}

.suffixSpan {
    position: absolute;
    right: -25%;
    top: -80%;
}
</style>