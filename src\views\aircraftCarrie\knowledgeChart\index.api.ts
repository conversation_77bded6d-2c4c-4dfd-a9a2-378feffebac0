import request from '@/utils/request.ts'

// 下载文件
export function getPdfFile(query: any) {
  return request({
    url: '/file/download',
    method: 'get',
    params: query,
    // headers: {
    //   Accept: 'application/octet-stream', // 请求文件流
    // },
    responseType: 'blob'
  })
}

// 获取知识图谱信息
export function guideSearchGraph(query: any) {
  return request({
    url: '/altKnowledgeGraph/listName',
    method: 'get',
    params: query
  })
}

// 获取知识图谱树形数据
export function getKnowledgeTree(params: any) {
  return request({
    url: '/altKnowledgeGraph/selectTreeStructure',
    method: 'post',
    data: params
  })
}

// 获取知识图谱节点信息
export function getAltKnowledgeGuide(params: any) {
  return request({
    url: '/altKnowledgeGuide/getById',
    method: 'get',
    params: params
  })
}
