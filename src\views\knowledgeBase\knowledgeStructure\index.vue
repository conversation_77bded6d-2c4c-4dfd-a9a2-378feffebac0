<template>
    <div class="basic-wrapper">
        <div class="filter-wrapper clearfix">
            <a-card class="table-screen clearfix" title="高原病知识结构" :bordered="false"
                :bodyStyle="{ padding: '0 24px', minHeight: 'calc(100vh - 180px)' }">
            </a-card>
        </div>


    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, toRaw, getCurrentInstance, watch } from 'vue'
import { message } from 'ant-design-vue'
import { operationLog } from '@/api/log'


// 生命周期钩子
onMounted(() => {

})

const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '高原病知识结构',
    ip: baseIP,
    type: '高原病知识库'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
onMounted(() => {
//   operationLogs()
})
</script>

<style lang="less" scoped></style>