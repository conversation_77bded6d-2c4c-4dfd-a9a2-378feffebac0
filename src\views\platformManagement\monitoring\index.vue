<template>
  <div></div>
</template>

<script setup lang="ts">
import { MinusCircleOutlined } from '@ant-design/icons-vue'
import { reactive,getCurrentInstance, onMounted } from 'vue'
import { Form, Input, Select, InputNumber, Button, Space, Row, Col } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()

onMounted(()=>{
  window.open('http://*************/grafana/d/fa49a4706d07a042595b664c87fb33ea/nodes?orgld=1','_blanck');
  router.push({ name: 'operationLogs' })
})
</script>

<style scoped>

</style>
