import { defineStore } from 'pinia'

export const usePageSourceStore = defineStore('pageSource', {
  state: () => ({
    pageSource: '',
    parentOrigin: ''
  }),
  getters: {
    getPageSource: (state) => state.pageSource,
    getParentOrigin: (state) => state.parentOrigin
  },
  actions: {
    setPageSource(pageSource: string) {
      this.pageSource = pageSource
    },
    setParentOrigin(origin: string) {
      this.parentOrigin = origin
    }
  }
})
