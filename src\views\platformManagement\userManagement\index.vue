<template>
  <div class="user-wrapper">
    <a-card title="用户管理" :bordered="false" :bodyStyle="{ padding: '24px', minHeight: 'calc(100vh - 180px)' }">
      <div class="search-container">
        <div class="search-box">
          <a-input-search allowClear style="width:238px" v-model:value="formData.searchKeyword" placeholder="请输入用户名搜索"
            @search="handleSearch" />
        </div>
        <div class="btn-box">
          <a-button type="primary" @click="handleAddUser">新建用户</a-button>
        </div>
      </div>

      <div class="table">
        <table-list ref="myTable" :tableData="state.tableList" :tableProps="state.tableProps" :total="state.total"
          @changePage="handleChangePage">
          <template #userRoles="{ record }">
            <a-tooltip color="#ffffff">
              <template #title>
                <a-tag style="margin-bottom: 5px;" color="cyan" v-for="(i, k) in record.userRoles" :key="k">
                  {{ i.roleName }}
                </a-tag>
              </template>
              <div style="width: 100%;white-space: normal;-webkit-box-orient:vertical;" class="two-ellipsis">
                <a-tag style="margin-bottom: 5px;" color="cyan" v-for="(i, k) in record.userRoles" :key="k">
                  {{ i.roleName }}
                </a-tag>
              </div>
            </a-tooltip>
          </template>
          <template #active="{ record }">
            <div style="display: flex; align-items: center;">
              <div class="active-status-box" :style="{ backgroundColor: record.active ? '#52C41A' : '#FF4D4F' }"></div>
              <div>{{ record.active ? '启用' : '停用' }}</div>
            </div>
          </template>
          <template #action="{ record }">
            <div style=" display: flex; align-items: center; flex-wrap: nowrap">
              <a-button class="custom-style" type="link" @click="handleEditUser(record)">编辑</a-button>
              <a-button class="custom-style" type="link">
                <a-popconfirm title="确定要重置密码?" ok-text="确定" cancel-text="取消" @confirm="handleConfirmRest(record)">
                  重置密码
                </a-popconfirm>
              </a-button>
              <a-button class="custom-style" type="link" v-if="!record.active"
                @click="handleEnableUser(record)">启用</a-button>
              <a-button class="custom-style" type="link" v-if="record.active">
                <a-popconfirm title="你确定要停用该账号吗?" ok-text="确定" cancel-text="取消" @confirm="handleConfirmStop(record)">
                  停用
                </a-popconfirm>
              </a-button>
            </div>
          </template>
        </table-list>
      </div>
    </a-card>

    <!-- 弹窗 -->
    <div class="modal-box">
      <a-modal :maskClosable="false" :closable="false" v-model:open="state.isOpenModal"
        :title="state.isModalTitle ? '新增用户' : '编辑用户'" @ok="handleOpenModalOk" @cancel="handleCancelModalOk">
        <div>
          <a-form :labelCol="{ span: 4 }" labelAlign="left" :model="formState" name="basic" autocomplete="off"  ref="userFormRef">
            <a-form-item label="账号" name="username"
              :rules="[{ required: true, message: '账号6-20位字符,仅支持英文和数字', min: 6, max: 20, validator: handleValidator, }]">
              <a-input :disabled="!state.isModalTitle" v-model:value="formState.username" placeholder="6-20位字符,仅支持英文和数字"
                @change="handleInputUser" />
            </a-form-item>

            <a-form-item label="密码" name="password"
              :rules="[{ required: true, message: '密码6-20位字符,不包含空格', min: 6, max: 20, validator: handlePasswordValidator}]">
              <a-input-password :disabled="!state.isModalTitle" v-model:value="formState.password"
                placeholder="6-20位字符,不包含空格" />
            </a-form-item>

            <a-form-item label="用户名" name="displayName"
              :rules="[{ required: true, message: '该字段是必填字段', min: 1, max: 20, }]">
              <a-input v-model:value="formState.displayName" placeholder="请输入" />
            </a-form-item>

            <a-form-item label="角色" name="roles" :rules="[{ required: true, message: '该字段是必填字段' }]">
              <a-select mode="multiple" v-model:value="formState.roles" placeholder="请选择">
                <a-select-option :value="item.roleCode" v-for="item in state.rolesList" :key="item.id">
                  {{ item.roleName }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="机构" name="userHospital">
              <a-select v-model:value="formState.userHospital" placeholder="请选择">
                <a-select-option :value="item.institutionName" v-for="item in state.institutionsList"
                  :key="item.institutionId">
                  {{ item.institutionName }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="科室" name="userDepartment">
              <a-input v-model:value="formState.userDepartment" placeholder="请输入" />
            </a-form-item>
          </a-form>
        </div>
      </a-modal>

      <a-modal v-model:open="state.isCheckModal" :closable="false" title="提示" @ok="handleCheakModalOk">
        <div>
          {{ state.errorMessage }}
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { onMounted, reactive, toRaw, ref } from 'vue'
import TableList from "@/components/TableList/index.vue";
import {
  getPageList, getRolesList, getAddUser, getEditUser, getRestPassword, getDisableUser,
  getEnableUser, getUserDetail, getUserBindPermission, getUserUnbindPermission, getInstitutionsList, getWithUsername
} from './index.api'
import { message } from 'ant-design-vue';
import _ from "lodash"
import { useUserStore } from '@/store/routes/user'

const userStore = useUserStore()
// 表单
const formData = reactive({
  searchKeyword: "",
})
const userFormRef=ref()
const formState = reactive({
  username: "",
  password: "",
  displayName: "",
  roles: [],
  userHospital: '',
  userDepartment: '',
})
// data
const state = reactive({
  pageNation: {
    pageNum: 1,
    pageSize: 10
  },
  tableList: [],
  tableProps: [
    {
      id: 1,
      title: "账号",
      dataIndex: "username",
      key: "username",
      ellipsis: true,
    },
    {
      id: 2,
      title: "用户名",
      dataIndex: "displayName",
      key: "displayName",
      ellipsis: true,
    },
    {
      id: 3,
      title: "角色",
      dataIndex: "userRoles",
      key: "userRoles",
      slot: "userRoles",
      ellipsis: true,
      hideTooltip: true
    },
    {
      id: 4,
      title: "状态",
      dataIndex: "active",
      key: "active",
      ellipsis: true,
      slot: "active",
      hideTooltip: true
    },
    {
      id: 5,
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      ellipsis: true,
    },
    {
      id: 99,
      title: "操作",
      dataIndex: "action",
      slot: "action",
      justifyContent: "flex-start",
      width: 60,
      hideTooltip: true
    },
  ],
  total: 0,
  // 弹窗
  isModalTitle: "新增用户",
  isOpenModal: false,
  isCheckModal: false,
  rolesList: [],
  institutionsList: [],
  currentItem: {},
  // 合并绑定用户角色
  bindUserParams: {},
  errorMessage: ''
})

const handleSearch = () => {
  // if (formData.searchKeyword.trim() === "") return
  state.pageNation = {
    pageNum: 1,
    pageSize: 10
  }
  getList()
}

const handleChangePage = ({ pageNum, pageSize }: { pageNum: number, pageSize: number }) => {
  state.pageNation = {
    pageNum: pageNum,
    pageSize: pageSize
  }
  getList()
}

function handleInputUser(value) {
  formState.password = formState.username + '654321'
}

const handleValidator = (rule, value, callback) => {
  let reg = /^[a-zA-Z0-9]{6,20}$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('6-16位字符,仅支持英文和数字'))
  }
}
const handlePasswordValidator = (rule, value, callback) => {
  let reg = /^[^\s]{6,20}$/  // 6-20位，不能有空格
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('密码6-20位字符，且不能包含空格'))
  }
}
const handleAddUser = async () => {
  state.isModalTitle = true
  await getRoles()
  await getInstitutions()
}

const handleEditUser = async (data) => {
  state.isModalTitle = false
  Object.assign(formState, {
    ...data,
    password: "******",
    roles: data.userRoles.map((item) => item.roleCode)
  })
  await getRoles()
  await getInstitutions()
}

const getRoles = async () => {
  let params = {
    pageSize: 9999,
    pageNum: 1,
    appCode: 'ALT'
  }
  await getRolesList(params).then((res) => {
    if (res.code == 200) {
      state.rolesList = res?.data ?? []
      state.isOpenModal = true
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

const getInstitutions = async () => {
  await getInstitutionsList().then((res) => {
    if (res.code == 200) {
      state.institutionsList = res.data ?? []
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

const handleOpenModalOk = async () => {
  try {
    await userFormRef.value.validate()
    let data = toRaw(formState)
    let params = {
      ...data,
      appCode: 'ALT',
      roles: data.roles.map((item) => {
        let obj = {
          appCode: 'ALT',
          roleCode: item
        }
        return JSON.stringify(obj)
      })
    }

    // 暂存绑定信息为了处理提示信息和合并账户使用
    state.errorMessage = ''
    state.bindUserParams = params

    if (!state.isModalTitle) {
      params.password = null
      let editRes = await getEditUser(params)

      if (editRes?.code == 200) {
        setUserRole(editRes.data, params.roles.map((r: string) => JSON.parse(r)), '编辑用户')
      }
    } else {
      delete params.userId
      let addRes = await getAddUser(params)

      if (addRes?.code == 200) {
        setUserRole(addRes.data, params.roles.map((r: string) => JSON.parse(r)), '新建用户')
      }

      // 账户关联特殊处理
      if (addRes?.code == 4001) {
        state.errorMessage = addRes.message || ''
        state.isCheckModal = true
      }
    }
  } catch (err) {
    console.log(err, 'err')
  }
}

const handleCheakModalOk = async () => {
  try {
    if (!state.bindUserParams.username) return message.error('请输入账号')
    let userData = await getWithUsername({ username: state.bindUserParams.username }).catch((err) => console.log('username-find-err', err))
    let params = {
      ...toRaw(state.bindUserParams),
      userId: userData.data.userId
    }
    let editRes = await getEditUser(params).catch((err) => console.log('username-edit-err', err))
    if (editRes?.code == 200) {
      setUserRole(editRes.data, params.roles.map((r: string) => JSON.parse(r)), '新建用户')
    }
  } catch (err) {
    console.log(err, 'err')
  }
}

// 设置用户角色
async function setUserRole(userId: string, roles: any, typeInfo: string) {
  try {
    const originRoles = (await getUserDetail(userId, { appCode: 'ALT' })).data.userRoles?.map(r =>
      _.pick(r, ['appCode', 'roleCode']),
    ) ?? []
    const append = _.differenceWith(roles, originRoles, _.isEqual);
    const removed = _.differenceWith(originRoles, roles, _.isEqual);

    if (append.length) await getUserBindPermission(append.map(pk => ({ ...pk, userId })));
    if (removed.length) await getUserUnbindPermission(removed.map(pk => ({ ...pk, userId })));

    handleCancelModalOk()
    await userStore.operationLogs(typeInfo)
    await getList()
  } catch (err) {
    console.log(err, 'err')
  }
}

const handleCancelModalOk = () => {
  state.isCheckModal = false
  state.isOpenModal = false
  Object.assign(formState, {
    username: '',
    password: '',
    displayName: '',
    roles: [],
    userHospital: '',
    userDepartment: '',
  })

  state.errorMessage = ''
  state.bindUserParams = {}
}

// 重置密码
const handleConfirmRest = (data) => {
  let params = {
    password: data.username + '654321'
  }
  let userId = data.userId
  getRestPassword(userId, params).then((res) => {
    if (res.code == 200) {
      message.success('操作成功')
      getList()
      userStore.operationLogs('重置密码')
    }
  }).catch((err) => {
    console.log('err', err)
  })
}
// 停用账号
const handleConfirmStop = (data) => {
  let params = {
    ...toRaw(data)
  }
  getDisableUser(params).then((res) => {
    if (res.code == 200) {
      message.success('操作成功')
      getList()
      userStore.operationLogs('停用用户')
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

const handleEnableUser = (data) => {
  let params = {
    ...toRaw(data)
  }
  getEnableUser(params).then((res) => {
    if (res.code == 200) {
      message.success('操作成功')
      getList()
      userStore.operationLogs('启用用户')
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

function getList() {
  let params = {
    appCode: "ALT",
    pageInfo: state.pageNation,
    searchKeyword: formData.searchKeyword,
    sortFields: []
  }
  getPageList(params).then((res) => {
    console.log(res, 'res')
    if (res.code == 200) {
      state.tableList = res.data || []

    }
  }).catch((err) => {
    console.log(err, 'err')
  })
}

function query() {
  getList()
}

onMounted(() => {
  query()
})
</script>

<style scoped lang='less'>
.search-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}


:deep(.search-container .search-box .ant-input) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

:deep(.search-container .search-box .ant-btn) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.table {
  width: 100%;
}

.active-status-box {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #52C41A;
  margin-right: 8px;
}


.modal-box .ant-modal-content {
  border-radius: 10px;
  overflow: hidden;
}

.custom-style {
  padding: 0 !important;
  margin-right: 10px !important;
  // margin-top: 8px;
  word-break: break-all;
  word-wrap: break-word;
  width: auto;
}
</style>