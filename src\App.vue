<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ConfigProvider as AConfigProvider } from 'ant-design-vue'
import { useTheme } from './utils/use-theme'
import zh_CN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')
const { theme, setTheme, antdTheme } = useTheme()

setTheme(theme.value)
</script>

<template>
  <a-config-provider
    :theme="antdTheme"
    componentSize="middle"
    :locale="zh_CN"
  >
    <!-- relative h-full w-full bg-normal-bg-color text-normal-text-color -->
    <!-- <div id="app"> -->
    <router-view />
    <!-- </div> -->
  </a-config-provider>
</template>
<style lang="less" scoped></style>
