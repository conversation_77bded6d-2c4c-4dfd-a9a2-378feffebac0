<template>
  <div class="basic-wrapper">
    <!-- 筛选 -->
    <div class="filter-wrapper clearfix">
      <a-card class="table-screen clearfix" title="高原肺水肿趋势预测" :bordered="false"
        :bodyStyle="{ padding: '0 24px', minHeight: 'calc(100vh - 180px)' }"></a-card>
    </div>

    <div style="background-color: #f8fafc; margin: 16px 0; width: 100%;">
      <a-row :gutter="24" class="content-row">
        <!-- 左侧模型介绍 -->
        <a-col :span="12" style="padding-left: 12px">
          <div class="card">
            <h4>高原肺水肿预测模型介绍</h4>
            <div>
              <p>高原肺水肿（HAPE）是一种严重的高原病，其致命风险主要源于诊断不准确和治疗延误。</p>
              <p>
                基于血液学参数和人口统计学变量，开发了一种用于诊断HAPE的列线图模型，可以方便地用于HAPE的诊断。
                在应急环境有限的高海拔地区，可以为医护人员提供快速可靠的诊断支持，帮助他们做出更好的治疗决策。
              </p>
            </div>
            <h4>受试者的纳入标准:</h4>
            <ul>
              <li>常住低海拔居民的汉族人</li>
              <li>以前没有患过高原肺水肿</li>
              <li>进入高海拔地区前无肺部疾病（肺栓塞和肺炎）、心血管疾病（心力衰竭）、严重精神障碍</li>
            </ul>
            <h4>模型性能:</h4>
            <ul>
              <li>区分度：</li>
              <li style="text-indent: 1em;">训练队列的AUC为0.787（95% CI [0.757-0.817]）</li>
              <li style="text-indent: 1em;">验证队列的AUC为0.833（95% CI [0.793-0.874]）</li>
              <br>
              <li>准确性：</li>
              <li style="text-indent: 1em;">训练组的准确度为70.95%</li>
              <li style="text-indent: 1em;">验证组的准确度为74.17%</li>
            </ul>
            <h4>参考来源：</h4>
            <ul>
              <li>
                [1] Li Q, Xu Z, Gong Q, Shen X. 2024. Clinical characteristics and a diagnostic model
                for high-altitude pulmonary edema in habitual low altitude dwellers. PeerJ 12:e18084
              </li>
            </ul>
          </div>
        </a-col>

        <!-- 右侧预测表单 -->
        <a-col :span="12" style="padding-left: 12px">
          <div class="typeCategory card">
            <!-- 结果显示区域 -->
            <a-spin style="height: 100%" tip="计算中，请稍侯..." :spinning="loading" v-show="isShow">
              <div class="result-container" v-if="!loading">
                <p class="result-title">高原肺水肿风险预测结果</p>
                <div class="result-value">
                  <span :class="getRiskClass(colors)">{{ colors }}</span>
                </div>
              </div>
              <div v-else class="result-placeholder"></div>
            </a-spin>

            <div class="lines" v-show="isShow"></div>

            <!-- 预测表单 -->
            <a-form :model="formState" :rules="rules" ref="formRef" v-bind="formItemLayout">
              <!-- 基本信息 -->
              <h4 style="color:#0065ff">基本信息</h4>
              <a-form-item label="性别" name="gender" required>
                <a-radio-group v-model:value="formState.gender">
                  <a-radio value="male">男</a-radio>
                  <a-radio value="female">女</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item label="年龄" name="age">
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.age" placeholder="请输入" @input="validateAge" />
                  <span class="suffixSpan">岁</span>
                </div>
              </a-form-item>

              <!-- 身体指标 -->
              <h4 style="color:#0065ff">身体指标</h4>
              <a-form-item label="身高" name="height" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.height" placeholder="请输入"
                    @input="validateNumber('height')" />
                  <span class="suffixSpan">m</span>
                </div>
              </a-form-item>

              <a-form-item label="体重" name="weight" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.weight" placeholder="请输入"
                    @input="validateNumber('weight')" />
                  <span class="suffixSpan">kg</span>
                </div>
              </a-form-item>

              <a-form-item label="BMI" name="bmi" required>
                <a-input style="width: 300px" v-model:value="formState.bmi" placeholder="请输入" disabled />
              </a-form-item>

              <!-- 血压指标 -->
              <h4 style="color:#0065ff">血压指标</h4>
              <a-form-item label="舒张压" name="diastolicPressure" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.diastolicPressure" placeholder="请输入"
                    @input="validateNumber('diastolicPressure')" />
                  <span class="suffixSpan">mmHg</span>
                </div>
              </a-form-item>

              <a-form-item label="平均动脉压" name="meanArterialPressure">
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.meanArterialPressure" placeholder="请输入"
                    @input="validateNumber('meanArterialPressure')" />
                  <span class="suffixSpan">mmHg</span>
                </div>
              </a-form-item>

              <!-- 血液指标 -->
              <h4 style="color:#0065ff">血液指标</h4>
              <a-form-item label="白细胞" name="whiteBloodCell" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.whiteBloodCell" placeholder="请输入"
                    @input="validateNumber('whiteBloodCell')" />
                  <span class="suffixSpan">10^9/L</span>
                </div>
              </a-form-item>

              <a-form-item label="淋巴细胞百分比" name="lymphocytePercentage" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.lymphocytePercentage" placeholder="请输入"
                    @input="validateNumber('lymphocytePercentage')" />
                  <span class="suffixSpan">%</span>
                </div>
              </a-form-item>

              <a-form-item label="血细胞比容" name="hematocrit" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.hematocrit" placeholder="请输入"
                    @input="validateNumber('hematocrit')" />
                  <span class="suffixSpan">%</span>
                </div>
              </a-form-item>

              <a-form-item label="平均红细胞体积" name="meanRedCellVolume" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.meanRedCellVolume" placeholder="请输入"
                    @input="validateNumber('meanRedCellVolume')" />
                  <span class="suffixSpan">fl</span>
                </div>
              </a-form-item>

              <a-form-item label="平均红细胞血红蛋白浓度" name="meanHemoglobinConcentration" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.meanHemoglobinConcentration" placeholder="请输入"
                    @input="validateNumber('meanHemoglobinConcentration')" />
                  <span class="suffixSpan">g/L</span>
                </div>
              </a-form-item>

              <a-form-item label="血小板计数" name="plateletCount" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.plateletCount" placeholder="请输入"
                    @input="validateNumber('plateletCount')" />
                  <span class="suffixSpan">10^9/L</span>
                </div>
              </a-form-item>

              <a-form-item label="平均血小板体积" name="meanPlateletVolume" required>
                <div class="custom-input-group">
                  <a-input style="width: 300px" v-model:value="formState.meanPlateletVolume" placeholder="请输入"
                    @input="validateNumber('meanPlateletVolume')" />
                  <span class="suffixSpan">fl</span>
                </div>
              </a-form-item>

              <!-- 表单按钮 -->
              <a-form-item>
                <div class="form-buttons">
                  <a-button type="primary" @click="submitForm">提交</a-button>
                  <a-button @click="resetForm">清空</a-button>
                </div>
              </a-form-item>
            </a-form>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, watch } from 'vue'
  import { message } from 'ant-design-vue'
  import { operationLog } from '@/api/log'

  // 类型定义
  type FormState = {
    gender: 'male' | 'female' | undefined;
    age: string;
    height: string;
    weight: string;
    bmi: string;
    diastolicPressure: string;
    meanArterialPressure: string;
    whiteBloodCell: string;
    lymphocytePercentage: string;
    hematocrit: string;
    meanRedCellVolume: string;
    meanHemoglobinConcentration: string;
    plateletCount: string;
    meanPlateletVolume: string;
    countTotal: number;
    [key: string]: string | number | undefined;
  }

  // 状态变量
  const isShow = ref<boolean>(false)
  const loading = ref<boolean>(false)
  const colors = ref<string>('0')
  const formRef = ref()

  // 表单布局
  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 15 },
  }

  // 表单状态
  const formState = reactive<FormState>({
    gender: undefined,
    age: '',
    height: '',
    weight: '',
    bmi: '',
    diastolicPressure: '',
    meanArterialPressure: '',
    whiteBloodCell: '',
    lymphocytePercentage: '',
    hematocrit: '',
    meanRedCellVolume: '',
    meanHemoglobinConcentration: '',
    plateletCount: '',
    meanPlateletVolume: '',
    countTotal: 0
  })

  // 表单验证规则
  const rules = {
    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
    height: [{ required: true, message: '请输入身高', trigger: 'blur' }],
    weight: [{ required: true, message: '请输入体重', trigger: 'blur' }],
    bmi: [{ required: true, message: '请输入BMI', trigger: 'blur' }],
    diastolicPressure: [{ required: true, message: '请输入舒张压', trigger: 'blur' }],
    whiteBloodCell: [{ required: true, message: '请输入白细胞数量', trigger: 'blur' }],
    lymphocytePercentage: [{ required: true, message: '请输入淋巴细胞百分比', trigger: 'blur' }],
    hematocrit: [{ required: true, message: '请输入血细胞比容', trigger: 'blur' }],
    meanRedCellVolume: [{ required: true, message: '请输入平均红细胞体积', trigger: 'blur' }],
    meanHemoglobinConcentration: [{ required: true, message: '请输入平均红细胞血红蛋白浓度', trigger: 'blur' }],
    plateletCount: [{ required: true, message: '请输入血小板计数', trigger: 'blur' }],
    meanPlateletVolume: [{ required: true, message: '请输入平均血小板体积', trigger: 'blur' }],
  }

  // 表单验证函数
  const validateNumber = (field: keyof FormState) => {
    let value = formState[field] as string;

    // 只允许数字和小数点
    if (!/^\d*\.?\d*$/.test(value)) {
      formState[field] = value.replace(/[^0-9.]/g, '');
    }

    // 处理多余的小数点
    const decimalPoints = (formState[field] as string).match(/\./g) || [];
    if (decimalPoints.length > 1) {
      // 保留第一个小数点，移除其他小数点
      const parts = (formState[field] as string).split('.');
      formState[field] = parts[0] + '.' + parts.slice(1).join('');
    }

    // 确保值不为负数
    const numValue = parseFloat(formState[field] as string);
    if (isNaN(numValue) || numValue < 0) {
      formState[field] = '';
    }
  }

  const validateAge = () => {
    // 只保留整数
    formState.age = formState.age.replace(/\D/g, '');

    // 处理前导零
    if (formState.age.length > 1 && formState.age.startsWith('0')) {
      formState.age = formState.age.replace(/^0+/, '');
    }

    // 确保年龄在合理范围内 (0-120)
    const age = parseInt(formState.age);
    if (isNaN(age) || age > 120) {
      formState.age = '';
    }
  }

  // 监听身高和体重的变化，自动计算 BMI
  watch([() => formState.height, () => formState.weight], ([newHeight, newWeight]) => {
    const height = parseFloat(newHeight);
    const weight = parseFloat(newWeight);

    if (height > 0 && weight > 0) {
      formState.bmi = (weight / (height * height)).toFixed(2); // 保留两位小数
    } else {
      formState.bmi = ''; // 身高或体重无效时清空 BMI
    }
  })

  // 计算总分数
  const calculateCountTotal = (): number => {
    let total = 0;

    // 性别因素
    if (formState.gender === 'male') {
      total += 10; // 男性加10分
    }

    // BMI评分
    const bmi = parseFloat(formState.bmi);
    if (!isNaN(bmi)) {
      if (bmi >= 18.5 && bmi <= 24.9) {
        total += 5;  // 正常BMI
      } else if (bmi >= 25) {
        total += 10; // 超重或肥胖
      }
    }

    // 舒张压评分
    const diastolic = parseFloat(formState.diastolicPressure) || 0;
    if (diastolic >= 160) {
      total += 12; // 高舒张压上限
    } else {
      total += diastolic * 0.075; // 按比例计分
    }

    // 白细胞评分
    const wbc = parseFloat(formState.whiteBloodCell) || 0;
    if (wbc >= 80) {
      total += 72; // 白细胞上限
    } else {
      total += wbc * 0.9; // 按比例计分
    }

    // 淋巴细胞百分比评分
    const lymph = parseFloat(formState.lymphocytePercentage) || 0;
    if (lymph <= 55) {
      total += (55 - lymph) * 0.25; // 低于55%按比例加分
    } else {
      total += 13.75; // 高于55%固定分数
    }

    // 血细胞比容评分
    const hct = parseFloat(formState.hematocrit) || 0;
    if (hct <= 80) {
      total += (80 - hct) * 0.75; // 低于80%按比例加分
    }

    // 平均红细胞体积评分
    const mcv = parseFloat(formState.meanRedCellVolume) || 0;
    if (mcv <= 120) {
      total += (120 - mcv) * 0.45; // 低于120按比例加分
    }

    // 平均红细胞血红蛋白浓度评分
    const mhc = parseFloat(formState.meanHemoglobinConcentration) || 0;
    if (mhc >= 500) {
      total += 25; // 高浓度上限
    } else {
      total += mhc * 0.05; // 按比例计分
    }

    // 血小板计数评分
    const platelet = parseFloat(formState.plateletCount) || 0;
    if (platelet <= 500) {
      total += (500 - platelet) * 0.052; // 低于500按比例加分
    }

    // 平均血小板体积评分
    const mpv = parseFloat(formState.meanPlateletVolume) || 0;
    if (mpv >= 180) {
      total += 100; // 高体积上限
    } else {
      total += mpv * 0.56; // 按比例计分
    }

    // 更新总分并返回
    formState.countTotal = total;
    return total;
  }

  // 根据总分计算风险等级
  const calculateRiskLevel = (score: number): string => {
    if (score <= 28) return "小于1%";
    if (score <= 44) return "小于等于5%";
    if (score <= 50) return "5%～10%";
    if (score <= 60) return "10%~25%";
    if (score <= 68) return "25%~40%";
    if (score <= 72) return "40%~55%";
    if (score <= 78) return "55%~70%";
    if (score <= 85) return "70%~85%";
    if (score <= 96) return "85%~95%";
    if (score <= 112) return "95%~99%";
    return "大于99%";
  }

  // 提交表单
  const submitForm = async () => {
    try {
      // 表单验证
      await formRef.value.validate();

      // 显示结果区域
      isShow.value = true;

      // 计算总分
      const score = calculateCountTotal();

      // 显示加载状态
      loading.value = true;

      // 模拟计算过程（实际项目中可能是API调用）
      setTimeout(() => {
        // 计算风险等级
        colors.value = calculateRiskLevel(score);

        // 完成加载
        loading.value = false;

        // 记录操作日志
        operationLogs().catch(err => {
          console.error('记录操作日志失败:', err);
        });

        message.success('预测结果计算完成');
      }, 2000); // 减少等待时间以提升用户体验

    } catch (error) {
      // 处理验证失败情况
      loading.value = false;
      isShow.value = false;
      console.error("表单验证失败:", error);
      message.warning("请填写所有必填项后再提交");
    }
  }

  // 重置表单
  const resetForm = () => {
    // 重置所有表单字段
    formRef.value.resetFields();

    // 隐藏结果区域
    isShow.value = false;

    // 清空结果
    colors.value = '0';

    message.info('表单已重置');
  }

  // 记录操作日志
  const baseIP = import.meta.env.BASE_IP;
  const operationLogs = async () => {
    const obj = {
      category: '高原病发病趋势预测',
      ip: baseIP,
      type: '高原病知识库'
    };

    try {
      const res = await operationLog(obj);
      // 根据实际API响应结构处理结果
      if (res && (res as any).code === 0) {
        // 日志记录成功
      }
    } catch (err) {
      console.log(err, 'err');
    }
  }

  // 获取风险等级对应的CSS类
  const getRiskClass = (risk: string): string => {
    if (risk.includes('小于') || risk.includes('1%')) {
      return 'risk-low';
    } else if (risk.includes('5%') || risk.includes('10%') || risk.includes('25%')) {
      return 'risk-medium';
    } else {
      return 'risk-high';
    }
  }

  // 组件挂载时记录操作日志
  onMounted(() => {
    operationLogs();
  })
</script>

<style lang="less" scoped>

  /* 基础布局 */
  .basic-wrapper {
    width: 100%;
    position: relative;
  }

  /* 卡片样式 */
  .card,
  .typeCategory {
    border-radius: 8px;
    padding: 16px 24px;
    width: 100%;
    height: calc(100vh - 100px);
    position: relative;
    box-shadow: none;
    overflow-y: auto;
  }

  .card {
    overflow: auto;

    h4 {
      font-size: 16px;
      font-weight: bold;
      color: #07123c;
      font-family: 'Noto Sans SC';
      margin: 15px 0;
    }
  }

  /* 分隔线 */
  .lines {
    width: 100%;
    margin: 30px 0;
    border: 0.1px solid rgb(224, 219, 219);
  }


  .suffixSpan {
    margin-left: 5px;
  }

  /* 表单按钮 */
  .form-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;

    button {
      margin-left: 10px;
    }
  }

  /* 结果显示样式 */
  .result-container {
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
  }

  .result-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
  }

  .result-value {
    font-size: 24px;
    font-weight: bold;

    span {
      padding: 5px 15px;
      border-radius: 4px;
      display: inline-block;
    }

    .risk-low {
      color: #52c41a;
      background-color: rgba(82, 196, 26, 0.1);
    }

    .risk-medium {
      color: #faad14;
      background-color: rgba(250, 173, 20, 0.1);
    }

    .risk-high {
      color: #f5222d;
      background-color: rgba(245, 34, 45, 0.1);
    }
  }

  .result-placeholder {
    height: 100px;
  }

  .content-row {
    height: calc(100vh - 100px);
  }

  .custom-input-group {
    display: flex;
    align-items: center;
  }
</style>