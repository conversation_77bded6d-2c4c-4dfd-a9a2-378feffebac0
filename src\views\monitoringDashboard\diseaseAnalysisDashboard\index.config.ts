// 折线图数据配置
// https://echarts.apache.org/zh/option.html#tooltip.confine
export const chartOptions = {
  color: ['#13A89B', '#2316AB', '#405BC8', '#CCF56A', '#14705E', '#00CFBE', '#098BEA', '#079A35', '#9DD962 '],
  tooltip: {
    trigger: 'axis',
    triggerOn: 'mousemove',
    confine: true,
    height: 'auto',
    backgroundColor: 'rgba(40, 49, 67, 0.85);',
    borderColor: 'rgba(40, 49, 67, 0.85);',
    enterable: true,
    appendToBody: true,
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
      // crossStyle: {
      //   color: '#fff'
      // }
    },
    // 自定义提示框内容
    // formatter: function (data) {
    //   let tooltipHtml = '<div style="color:#ffffff;font-size: 14px;font-weight: 400;margin-bottom: 8px;max-height: 200px; overflow-y: auto;">'
    //   let str = '<div style="color:#ffffff;font-size: 14px;font-weight: 400;margin-bottom: 8px;">' + data[0].axisValue + '</br></div>'
    //   // 这里根据数据格式拼接 tooltip 的内容
    //   data.forEach((item) => {
    //     let text = `
    //     <div style="font-size: 12px;padding: 4px 0;width: auto;max-height: 200px; overflow-y: auto;">
    //       <div style="display:inline-block;margin-right:4px;width:10px;height:10px;background-color: ${item.color};color:#C1C4CE"></div>
    //       <span>${item.seriesName} ：</span>
    //       <span style="color:#ffffff">${item.data}</span>
    //     </div>
    //     `
    //     tooltipHtml = tooltipHtml + text
    //   })

    //   tooltipHtml += '</div>'
    //   return str + tooltipHtml
    // },
    formatter: function (data) {
      if (!data || data.length === 0) return '' // 空数据直接返回
      let tooltipHtml = '<div style="color:#ffffff;font-size: 14px;font-weight: 400;margin-bottom: 8px;max-height: 200px; overflow-y: auto;">'
      let str = '<div style="color:#ffffff;font-size: 14px;font-weight: 400;margin-bottom: 8px;">' + data[0].axisValue + '</br></div>'

      data.forEach((item) => {
        if (item.data !== 0) {
          // 过滤 0 值
          tooltipHtml += `
            <div style="font-size: 12px;padding: 4px 0;width: auto;max-height: 200px; overflow-y: auto;">
              <div style="display:inline-block;margin-right:4px;width:10px;height:10px;background-color: ${item.color};"></div>
              <span>${item.seriesName} ：</span>
              <span style="color:#ffffff">${item.data}</span>
            </div>
          `
        }
      })

      tooltipHtml += '</div>'
      return str + tooltipHtml
    },
    textStyle: {
      fontSize: 14
    }
  },
  toolbox: {
    show: false
    // feature: {
    //   dataView: { show: true, readOnly: false },
    //   magicType: { show: true, type: ['line', 'bar'] },
    //   restore: { show: true },
    //   saveAsImage: { show: true }
    // }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  // X轴可滚动
  dataZoom: [
    {
      type: 'slider', // 设置为滑动条型式
      show: true, // 显示dataZoom组件
      // start: 0, // 默认显示的起始位置为0
      // end: 20, // 默认显示的结束位置为100
      bottom: 0,
      height: 10,
      // handleSize: 8, // 滑动条的手柄大小
      // handleStyle: {
      //   color: '#DCE2E8' // 滑动条的手柄颜色
      // },
      showDetail: false,
      startValue: 0, //滚动条的起始位置
      endValue: 9, //滚动条的截止位置（按比例分割你的柱状图x轴长度）
      xAxisIndex: [0], // 表示控制第一个x轴
      filterMode: 'filter' // 设置为filter模式，即数据超过范围时会被过滤掉
    },
    {
      type: 'inside', //设置鼠标滚轮缩放
      show: true,
      xAxisIndex: [0],
      startValue: 0,
      endValue: 9
    }
  ],
  legend: {
    width: '97%',
    itemHeight: 10,
    itemWidth: 10,
    itemGap: 15,
    top: '2%',
    left: '2%',
    type: 'scroll', // 数据过多时，分页显示
    icon: 'rect', // 设置图例的形状
    selected: [] //这里默认显示数组中前十个，如果不设置，则所有的数据都会显示在图表上
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: [],
      axisTick: {
        show: false
      },
      axisLabel: {
        // show: true, //下方日期显示与否
        // interval: 0, // 设置数据间隔
        // rotate: 28, // 标题倾斜
        // margin: 5, //刻度标签与轴线之间的距离
        // textStyle: {
        //   fontSize: 9, //横轴字体大小
        //   color: '#000000' //颜色
        // },

        // 轴文字
        show: true,
        color: '#A6AAB2',
        fontSize: 12,
        interval: 0,
        rotate: 20
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: []
}

// 柱状图配置
export const barChartOptions = {
  title: {
    textStyle: {
      //文字颜色
      color: '#07123C',
      fontWeight: 'bold',
      //字体系列
      fontFamily: 'sans-serif',
      //字体大小
      fontSize: 18
    }
  },
  toolbox: {
    show: true
    // feature: {
    //   dataView: { show: true, readOnly: false },
    //   magicType: { show: true, type: ['line', 'bar'] },
    //   restore: { show: true },
    //   saveAsImage: { show: true }
    // }
  },
  tooltip: {
    backgroundColor: '#48505f',
    borderColor: '#48505f',
    enterable: true,
    appendToBody: true,
    textStyle: {
      color: '#fff'
    },
    trigger: 'axis'
  },
  legend: {
    itemHeight: 10,
    itemWidth: 10,
    icon: 'rect', // 设置图例的形状
    data: ['Forest', 'Steppe', 'Desert', 'Wetland']
  },
  color: ['#405BC8'],
  barWidth: 20,
  calculable: true,
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false
    },

    axisLabel: {
      // 轴文字
      show: true,
      color: '#A6AAB2',
      fontSize: 12,
      interval: 0,
      rotate: 20
    },
    axisLine: {
      lineStyle: {
        type: 'solid',
        color: '#E6E6E8',
        width: '1'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      // 轴文字
      show: true,
      color: '#A6AAB2',
      fontSize: 12
    }
    // max:99999//给y轴设置最大值
  },
  series: [
    {
      type: 'bar',
      data: [],
      // barGap:'80%',/*多个并排柱子设置柱子之间的间距*/
      // barCategoryGap:'50%',/*多个并排柱子设置柱子之间的间距*/
      itemStyle: {
        //柱状图上方显示数值
        normal: {
          color: '#405BC8',
          label: {
            show: true, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#A7ABB3',
              fontSize: 12
            }
          }
        }
      },
      showBackground: true,
      backgroundStyle: {
        color: '#f2f8ff'
      },
      label: {
        show: true,
        position: 'top',
        formatter: '{c}'
      }
    }
  ],
  dataZoom: [
    {
      type: 'slider', //给x轴设置滚动条
      show: true, //flase直接隐藏图形
      xAxisIndex: [0],
      bottom: 0,
      height: 10,
      showDetail: false,
      startValue: 0, //滚动条的起始位置
      endValue: 9 //滚动条的截止位置（按比例分割你的柱状图x轴长度）
    },
    {
      type: 'inside', //设置鼠标滚轮缩放
      show: true,
      xAxisIndex: [0],
      startValue: 0,
      endValue: 9
    }
  ]
}

// 饼图配置
// https://echarts.apache.org/examples/zh/editor.html?c=pie-labelLine-adjust
export const pieChartOptions = {
  color: ['#13A89B', '#2316AB', '#405BC8', '#CCF56A', '#14705E', '#00CFBE', '#098BEA', '#079A35', '#9DD962 '],
  tooltip: {
    trigger: 'item',
    formatter: '{b} : {c}例'
  },
  legend: {
    // orient: 'vertical',
    x: 'center',
    y: 'bottom',
    bottom: '0%',
    itemHeight: 10,
    itemWidth: 10,
    itemGap: 15,
    icon: 'rect', // 设置图例的形状
    type: 'scroll', // 数据过多时，分页显示
    selected: [] //这里默认显示数组中前十个，如果不设置，则所有的数据都会显示在图表上
  },
  series: [
    // 主要层
    {
      radius: ['33%', '61%'],
      center: ['50%', '43%'],
      type: 'pie',
      data: [],
      avoidLabelOverlap: true, //对，就是这里avoidLabelOverlap
      minAngle: 5, //最小角度
      emphasis: {
        label: {
          show: false,
          fontSize: 10,
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: true,
        length: 10, // 第一段指导线 长度
        length2: 10 // 第二段指导线 长度
        // maxSurfaceAngle: 80
      },
      label: {
        show: true,
        formatter: '{b}: {c}例'
        // alignTo: 'edge',
        // minMargin: 5,
        // edgeDistance: 150,
        // lineHeight: 20,
        // rich: {
        //   time: {
        //     fontSize: 10,
        //     color: '#999'
        //   }
        // }
      },
      name: ''
    }
  ]
}
