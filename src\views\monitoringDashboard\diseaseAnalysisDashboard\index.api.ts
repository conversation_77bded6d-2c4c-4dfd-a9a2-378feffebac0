import request from '@/utils/request.ts'

// 获取折线图
export function getAltDiseaseDashBoardDeginnrt(query: any) {
  return request({
    url: '/altDiseaseDashBoard/trending',
    method: 'get',
    params: query
  })
}

// 获取柱状图
export function getAltDiseaseDashBoardAmount(query: any) {
  return request({
    url: '/altDiseaseDashBoard/amount',
    method: 'get',
    params: query
  })
}

// 获取饼图
export function getAltDiseaseDashBoardDistribution(query: any) {
  return request({
    url: '/altDiseaseDashBoard/distribution',
    method: 'get',
    params: query
  })
}

// 获取下拉数据
export function getAltDiseaseDashBoardDisease(query: any) {
  return request({
    url: '/altDiseaseDashBoard/disease',
    method: 'get',
    params: query
  })
}
