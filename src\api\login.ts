import request from '@/utils/request.ts'

// 登录
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function logout() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve({ code: 0, data: null, errorMsg: '' })
    }, 300)
  })

  // return request({
  //   url: '/xxx/logout',
  //   method: 'get',
  // })
}
