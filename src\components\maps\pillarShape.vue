<template>
  <div class="chartsMain">
    <!-- ECharts图表 -->
    <div
      ref="chart"
      style="width: 100%; height: 700px"
    ></div>

    <!-- 返回按钮 -->
    <a-button
      v-if="showBackButton"
      class="backLevel"
      @click="onBackClick"
      >返回</a-button
    >
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, defineProps, watch, getCurrentInstance, onBeforeUnmount, onBeforeMount } from 'vue'
import * as echarts from 'echarts'
// import { ASelect, ASelectOption, AButton } from 'ant-design-vue';
import { allAreasIndicators, belongWith, allProvincesIndicators, allCitiesIndicators, allPatientUnitsIndicators,allDimensionIndicator } from './gisIndicator'
//import { overflow } from 'html2canvas/dist/types/css/property-descriptors/overflow'
import { useSharedStore } from '@/store/geoState/state'
import { storeToRefs } from 'pinia'
import Bus from '@/utils/Bus'
import { areaProvinces } from '@/store/geoState/areaProvince'

const store = useSharedStore()
const { storeInfectiousType, dValue } = storeToRefs(store)

const props = defineProps({
  dateRangeTime: {
    type: Array,
    required: false // option 是必须的
  },
  dataSelectType: {
    type: String,
    required: true
  }
})

const getInfectiousTypeFromAuto = ref<any>(props.dataSelectType)

watch(props, (newVal: { dataSelectType: any }) => {
  console.log(currentDimension.value, 'currentDimension111')
  getInfectiousTypeFromAuto.value = newVal.dataSelectType
  // gisDateRangeTime.value = newVal.dateRangeTime;
  const clickedData = {
    area: '',
    infectiousType: getInfectiousTypeFromAuto.value,
    from: gisDateRangeTime.value[0],
    to: gisDateRangeTime.value[1],
    "prevDimensionFilter": "",
    "queryDimension": "",
  }
  if (currentDimension.value == '战区') {
    const objs = {
      selectOptions: getInfectiousTypeFromAuto.value,
      rangTimeDateFrom: gisDateRangeTime.value[0],
      rangTimeDateTo: gisDateRangeTime.value[1],
      "prevDimensionFilter": "",
      "queryDimension": "战区",
    }
    fetchChartData(objs)
  } else if (currentDimension.value == '省') {
    clickedData.area = store.leftForArea;
    clickedData.queryDimension = '省';
    clickedData.prevDimensionFilter = store.leftForArea;
    fetchProvincesIndicators(clickedData)
  } else if (currentDimension.value == '市') {
    clickedData.area = store.leftForCityProvince
    clickedData.queryDimension = '市';
    clickedData.prevDimensionFilter = store.leftForCityProvince;
    fetchCitiesIndicators(clickedData)
  } else {
    store.leftForUniDimension = currentDimension.value
    clickedData.area = store.leftForUniName;
    clickedData.queryDimension = '区';
    clickedData.prevDimensionFilter = store.leftForUniName;
    fetchPatientUnitsIndicators(clickedData)
  }
})

const { proxy }: any = getCurrentInstance()
const gisDateRangeTime = ref<any>(props.dateRangeTime)
const selectedOption = ref<any>('全部')
console.log(props.dateRangeTime, 'gisDateRangeTime')

watch(
  props,
  (newVal) => {
    console.log(newVal, 'newValnewVal')
    console.log(currentDimension.value, 'ffff')
    gisDateRangeTime.value = newVal.dateRangeTime

    const clickedData = {
    area: '',
    infectiousType: getInfectiousTypeFromAuto.value,
    from: gisDateRangeTime.value[0],
    to: gisDateRangeTime.value[1],
    "prevDimensionFilter": "",
    "queryDimension": "",
  }
  if (currentDimension.value == '战区') {
    const objs = {
      selectOptions: getInfectiousTypeFromAuto.value,
      rangTimeDateFrom: gisDateRangeTime.value[0],
      rangTimeDateTo: gisDateRangeTime.value[1],
      "prevDimensionFilter": "",
      "queryDimension": "战区",
    }
    fetchChartData(objs)
  } else if (currentDimension.value == '省') {
    clickedData.area = store.leftForArea;
    clickedData.queryDimension = '省';
    clickedData.prevDimensionFilter = store.leftForArea;
    fetchProvincesIndicators(clickedData)
  } else if (currentDimension.value == '市') {
    clickedData.area = store.leftForCityProvince
    clickedData.queryDimension = '市';
    clickedData.prevDimensionFilter = store.leftForCityProvince;
    fetchCitiesIndicators(clickedData)
  } else {
    store.leftForUniDimension = currentDimension.value
    clickedData.area = store.leftForUniName;
    clickedData.queryDimension = '区';
    clickedData.prevDimensionFilter = store.leftForUniName;
    fetchPatientUnitsIndicators(clickedData)
  }
  }
  // { deep: true }
)
watch(
  () => store.leftForCityProvince,
  (newValue: string) => {
    console.log(store.leftForCityProvince, 11111)
    console.log(store.leftForCityDimension, 222222)
    console.log(currentDimension.value, 'currentDimension.value')
    store.updateleftForCityProvince(store, newValue)
    store.updateleftForCityDimension(store, currentDimension.value)
    const clickedData = {
      area: '',
      infectiousType: getInfectiousTypeFromAuto.value,
      from: gisDateRangeTime.value[0],
      to: gisDateRangeTime.value[1],
      "prevDimensionFilter": "",
      "queryDimension": "",
    }
    if (currentDimension.value == '战区') {

      clickedData.area = store.leftForArea
      clickedData.queryDimension = '省';
      clickedData.prevDimensionFilter = store.leftForArea;
      fetchProvincesIndicators(clickedData)
    } else if (currentDimension.value == '省') {
      // clickedData.area = store.leftForArea
      // fetchProvincesIndicators(clickedData)
      clickedData.area = store.leftForCityProvince
      clickedData.queryDimension = '市';
      clickedData.prevDimensionFilter = store.leftForCityProvince;
      fetchCitiesIndicators(clickedData)
    } else if (currentDimension.value == '市') {
      // clickedData.area = store.leftForCityProvince

      // fetchCitiesIndicators(clickedData)
      store.leftForUniDimension = currentDimension.value
      clickedData.queryDimension = '区';
      // clickedData.prevDimensionFilter = store.leftForUniDimension;
      clickedData.prevDimensionFilter= store.leftForUniName
    } else {
      // store.leftForUniDimension = currentDimension.value
      const objs = {
        selectOptions: getInfectiousTypeFromAuto.value,
        rangTimeDateFrom: gisDateRangeTime.value[0],
        rangTimeDateTo: gisDateRangeTime.value[1],
        "prevDimensionFilter": "",
        "queryDimension": "战区",
      }
      fetchChartData(objs)
    }
  }
)
watch(
  () => store.leftForUniName,
  (newValue: string) => {
    console.log(store.leftForUniName, 7777)
    console.log(store.leftForUniDimension, 6666)
    const clickedData = {
      area: '',
      infectiousType: getInfectiousTypeFromAuto.value,
      from: gisDateRangeTime.value[0],
      to: gisDateRangeTime.value[1],
      queryDimension:'',
      prevDimensionFilter:'',
    }
    if (currentDimension.value == '市') {
      clickedData.area = store.leftForUniName
      console.log(clickedData, 'jinu')
      clickedData.queryDimension = '区';
      clickedData.prevDimensionFilter = store.leftForUniName;
      fetchPatientUnitsIndicators(clickedData).then(() => {
        updateChart()
      })
    }
  }
)
const showBackButton = ref(false) // 控制返回按钮显示与否
const currentChartData = ref<any>([]) // 当前显示的柱形图数据
const previousChartData = ref<any>([]) // 上一次的柱形图数据
const chart = ref<any>(null) // 图表引用
const getRequestDataX = ref<any>([])
const getRequestDataY = ref<any>([])
const currentDimension = ref('')
const countryMap = ref<any>([])
const provinceMap = ref<any>([])
const cityMap = ref<any>([])
const unitsMap = ref<any>([])
const optionMap = ref<any>({})
store.updateGetleftcurrentDimension(store, showBackButton.value)
// 模拟后台请求数据 全国下的展区
const fetchChartData = async (objs:any) => {
  // console.log(objs, 'objs')

  const obj = {
    infectiousType: objs.selectOptions,
    from: objs.rangTimeDateFrom,
    to: objs.rangTimeDateTo,
    "prevDimensionFilter":objs.prevDimensionFilter,
    "queryDimension": objs.queryDimension,
  }
  optionMap.value = objs.selectOptions

  const res = await allDimensionIndicator(obj)
  getRequestDataX.value = []
  getRequestDataY.value = []
  currentChartData.value = []
  if (res?.code == 0) {
    res.data.curDimensionIndicators.sort(function (a: { cnt: number }, b: { cnt: number }) {
      return b.cnt - a.cnt
    })
    res.data.curDimensionIndicators.map((item: { cnt: any; curValue: any }) => {
      getRequestDataX.value.push(item.cnt)
      getRequestDataY.value.push(item.curValue)
    })
    currentDimension.value = res.data.curDimension
  } else {
    proxy.$message.error(res?.errorMsg)
  }

  currentChartData.value = getRequestDataX.value
  countryMap.value = getRequestDataX.value
  updateChart()
}

let chartInstance = null
// 更新图表
const updateChart = () => {
  if (chart.value) {
    chartInstance = echarts.init(chart.value)
    var emphasisStyle = {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0,0,0,0.3)'
      },
      focus: 'series'
    }
    const chartOptions = {
      series: [
        {
          data: currentChartData.value,
          type: 'bar',

          emphasis: emphasisStyle,
          label: {
            show: true,
            formatter: '{c}', //显示数据带上百分比
            position: 'right',
            formatter (a:any) { return a.value.toLocaleString(); },
          },
          
          itemStyle: {
            barBorderRadius: [0, 2, 2, 0], //柱体圆角
            color: '#405BC8',
            // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            //   {
            //     //代表渐变色从正上方开始
            //     offset: 0, //offset范围是0~1，用于表示位置，0是指0%处的颜色
            //     color: '#016EFF'
            //   }, //柱图渐变色
            //   {
            //     offset: 1, //指100%处的颜色
            //     color: '#00CFBE'
            //   }
            // ]),
            label: {
              normal: {
                color: '#177BEA',
                label: {
                  show: true, //开启显示
                  position: 'right', //在上方显示
                  textStyle: {
                    //数值样式
                    color: '#A7ABB3',
                    fontSize: 12
                  },
                  
                }
              }
            }
            // }
          }
        }
      ],
      // barWidth: '16px',
      barWidth: '16px', //柱体宽度
      // barHeight: '16px',
      // barGap:'32px',
      calculable: true,
      grid: {
        left: '0',
        right: '6%',
        top:'7%',
        bottom: '17%',
        containLabel: true
      },

      xAxis: {
        type: 'value',

        splitLine: {
          show: false
        },
        axisLabel: {
          // 轴文字
          show: true,
          color: '#A6AAB2',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'category',
        inverse: true,
        axisTick: {
          show: false
        },

        data: getRequestDataY.value,
        axisLabel: {
          width: 65,
          overflow: 'truncate',
          ellipsis: '...',
          // 轴文字
          show: true,
          color: '#A6AAB2',
          fontSize: 12,
          interval: 0,
          rotate: 20
        },
        axisLine: {
          lineStyle: {
            type: 'solid',
            color: '#E6E6E8',
            width: '1'
          }
        }
      },
      title: {
        text: '区域发病量排序',
        textStyle: {
          //文字颜色
          color: '#07123C',

          //字体粗细 'normal','bold','bolder','lighter',100 | 200 | 300 | 400...
          fontWeight: 'bold',
          //字体系列
          fontFamily: 'sans-serif',
          //字体大小
          fontSize: 18
        },
      },
      // tooltip: {//提示框组件
			// 		trigger: 'item', //item数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用。
			// 		axisPointer: {
			// 			// 坐标轴指示器，坐标轴触发有效
			// 			type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
			// 		},
			// 		formatter: '{b} : {c} 例' //{a}（系列名称），{b}（数据项名称），{c}（数值）, {d}（百分比）
      //   },

      tooltip: {
          trigger: 'axis',
          // axisPointer: {
          //   // type: 'line',
          // },
          // 处理千分位
          formatter(params) {
            let relVal = params[0].name;
            for (let i = 0, l = params.length; i < l; i++) {
              relVal += `<br/>${params[i].marker} ${params[i].value.toLocaleString()} 例`;
            }
            return relVal;
          },
        },
    }

    // 设置图表配置
    chartInstance.setOption(chartOptions)
    chartInstance.off('click')
    // 绑定点击事件
    chartInstance.on('click', (params: any) => {
      onChartClick(params)
    })
  }
}
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const currntName = ref('')
// 点击柱形图时触发
const onChartClick = (params: any) => {
  console.log(params, '柱形图')
  console.log(currentDimension.value, 'currentDimension')

  getRequestDataX.value = []
  getRequestDataY.value = []
  // 获取点击的柱形数据
  const clickedData = {
    area: params.name,
    infectiousType: getInfectiousTypeFromAuto.value,
    from: gisDateRangeTime.value[0],
    to: gisDateRangeTime.value[1],
    "prevDimensionFilter": params.name,
    "queryDimension": "",
  }

  // // 保存当前数据并发送请求
  previousChartData.value = currentChartData.value
  currentChartData.value = [clickedData] // 模拟只显示点击的柱形图数据
  showBackButton.value = true
  console.log(clickedData, 'clickedData')
  store.updateGetleftcurrentDimension(store, showBackButton.value)
  if (currentDimension.value == '战区') {
    console.log(params.name, 'params.name')
    clickedData.queryDimension='省'
    store.updateleftForArea(store, params.name)
    // 发起后台请求获取新的数据
    fetchProvincesIndicators(clickedData).then(() => {
      updateChart()
    })
  } else if (currentDimension.value == '省') {
    console.log(params.name, 'params.name111')
    store.updateleftForCityProvince(store, params.name)
    store.updateleftForCityDimension(store, params.name);
     clickedData.queryDimension='市'
    fetchCitiesIndicators(clickedData).then(() => {
      updateChart()
    })
  } else if (currentDimension.value == '市') {
    console.log(params.name, 'params.name222')

    store.updateleftForUniName(store, params.name)
    // store.updateleftForUniDimension(store, currentDimension.value)
    store.updateleftForUniDimension(store, '市')
    
    clickedData.queryDimension='区'
    fetchPatientUnitsIndicators(clickedData).then(() => {
      updateChart()
    })
  }
}
// 获取展区下省一级数据
const fetchProvincesIndicators = async (option: object) => {
  console.log(option.queryDimension,222222222222)
  const obj = {
    infectiousType: getInfectiousTypeFromAuto.value,
    from: option.from,
    to: option.to,
    area: option.area,
    "prevDimensionFilter": option.prevDimensionFilter,
    "queryDimension": option.queryDimension,
  }
  optionMap.value = option
  getRequestDataX.value = []
  getRequestDataY.value = []

  await allDimensionIndicator(obj).then((res: any) => {
    console.log(res, 'kkkkk')
    getUniDimension.value = res.data.prevDimension
    getValue.value = res.data.prevValue
    if (res?.code == 0) {
      res.data.curDimensionIndicators.sort(function (a: { cnt: number }, b: { cnt: number }) {
      return b.cnt - a.cnt
    })
      // res.data.gisIndicators.map((item: { dimension: string; infectiousCnt: any; province: any }) => {
      //   getRequestDataX.value.push(item.infectiousCnt)
      //   getRequestDataY.value.push(item.province)
      //   currentDimension.value = item.dimension
      //   currntName.value = item.province
      // })
      res.data.curDimensionIndicators.map((item: { cnt: any; curValue: any }) => {
      getRequestDataX.value.push(item.cnt)
      getRequestDataY.value.push(item.curValue)
      currntName.value = item.curValue
    })
      currentDimension.value = res.data.curDimension
    
      store.updateGetAreasProvince(store, getRequestDataY.value)
    } else {
      proxy.$message.error(res?.errorMsg)
    }
  })
  // 假设根据下拉框选项发送请求，返回不同的数据
  // const data = option === 'option1' ? [120, 200, 150, 80, 70] : [90, 160, 130, 200, 250];

  currentChartData.value = getRequestDataX.value
  provinceMap.value = getRequestDataX.value
  updateChart()
}
// 获取省下的市一级数据
const fetchCitiesIndicators = async (option: object) => {
  console.log(option, '市级option')
  const obj = {
    infectiousType: getInfectiousTypeFromAuto.value,
    from: option.from,
    to: option.to,
    province: '',
    "prevDimensionFilter": "",
    "queryDimension": "市",
  }
  if (option.area) {
    obj.province = option.area;
    obj.prevDimensionFilter=option.area;
  } else if (option.province) {
    obj.province = option.province;
    obj.prevDimensionFilter=option.province;
  }

  // store.updateleftForCityProvince(store,obj.province)
  // store.updateleftForCityDimension(store, '省')

  optionMap.value = option

  await allDimensionIndicator(obj).then((res: any) => {
    console.log(res, 'kkkkk')
    // store.updateleftForCityDimension(store,res.data.dimension)
    // getUniDimension.value = store.leftForCityDimension
    getUniDimension.value = res.data.prevDimension
    getValue.value = res.data.prevValue
    getRequestDataX.value = []
    getRequestDataY.value = []
    if (res?.code == 0) {
      res.data.curDimensionIndicators.sort(function (a: { cnt: number }, b: { cnt: number }) {
      return b.cnt - a.cnt
    })
    res.data.curDimensionIndicators.map((item: { cnt: any; curValue: any }) => {
      getRequestDataX.value.push(item.cnt)
      getRequestDataY.value.push(item.curValue)
      currntName.value = item.curValue
    })
    currentDimension.value = res.data.curDimension
      store.updateGetProvincesCity(store, getRequestDataY.value)
    } else {
      proxy.$message.error(res?.errorMsg)
    }
  })

  currentChartData.value = getRequestDataX.value
  cityMap.value = getRequestDataX.value
  console.log(currentChartData.value)

  updateChart()
}
const getUniDimension = ref<any>('')
const getValue = ref<any>('')
// 获取市下的机构一级数据
const fetchPatientUnitsIndicators = async (option: object) => {
  const obj = {
    infectiousType: getInfectiousTypeFromAuto.value,
    from: option.from,
    to: option.to,
    city: option.area,
    queryDimension:'区',
    prevDimensionFilter:option.area,
  }
  optionMap.value = option

  await allDimensionIndicator(obj).then((res: any) => {
    console.log(res, 'kkkkk')
    getRequestDataX.value = []
    getRequestDataY.value = []
    if (res?.code == 0) {

      res.data.curDimensionIndicators.sort(function (a: { cnt: number }, b: { cnt: number }) {
      return b.cnt - a.cnt
    })

    store.updateleftForUniDimension(store, res.data.prevDimension)
      getUniDimension.value = res.data.prevDimension
      // getUniDimension.value = res.data.dimension
      getValue.value = res.data.prevValue
      res.data.curDimensionIndicators.map((item: { cnt: any; curValue: any }) => {
        getRequestDataX.value.push(item.cnt)
        getRequestDataY.value.push(item.curValue)
        currntName.value = item.curValue
      })
    currentDimension.value = res.data.curDimension
 
    } else {
      proxy.$message.error(res?.errorMsg)
    }
  })
  // 假设根据下拉框选项发送请求，返回不同的数据
  // const data = option === 'option1' ? [120, 200, 150, 80, 70] : [90, 160, 130, 200, 250];

  currentChartData.value = getRequestDataX.value
  unitsMap.value = getRequestDataX.value
  // updateChart()
}
const getBackData = ref<any>('')
// 返回按钮点击事件
const onBackClick = async () => {
  const obj = {
    dimension: getUniDimension.value,
    value: getValue.value
  }
  if (currentDimension.value != '省') {
    belongWith(obj).then((res: any) => {
      // if (res?.code == 0) {

      getBackData.value = res.data
      if (currentDimension.value == '区') {
        store.updateBackCurrentLevel(store, 'city')
        store.updateleftForUniName(store, getValue.value)
        store.updateleftForUniDimension(store, getUniDimension.value)
        const obj = {
          province: getBackData.value,
          infectiousType: getInfectiousTypeFromAuto.value,
          from: gisDateRangeTime.value[0],
          to: gisDateRangeTime.value[1],
          "prevDimensionFilter": getBackData.value,
          "queryDimension": '市',
        }

        fetchCitiesIndicators(obj)

        // updateChart()
      } else if (currentDimension.value == '市') {
        console.log(getValue.value, '市市市市')

        store.updateBackCurrentLevel(store, 'province')
        // store.leftForCityProvince=getValue.value;

        store.updateleftForCityProvince(store, getValue.value)
        store.updateleftForCityDimension(store, getUniDimension.value)

        const obj = {
          infectiousType: getInfectiousTypeFromAuto.value,
          from: gisDateRangeTime.value[0],
          to: gisDateRangeTime.value[1],
          area: res.data,
          "prevDimensionFilter": res.data,
          "queryDimension": '省',
        }

        fetchProvincesIndicators(obj)
        // updateChart()
      }
    })
  } else {
    store.updateBackCurrentLevel(store, 'country')
    const infectiousType = getInfectiousTypeFromAuto.value
    const objs = {
      selectOptions: infectiousType,
      rangTimeDateFrom: gisDateRangeTime.value[0],
      rangTimeDateTo: gisDateRangeTime.value[1],
      "prevDimensionFilter": "",
      "queryDimension": "战区",
    }
    fetchChartData(objs)
    showBackButton.value = false
    store.updateGetleftcurrentDimension(store, showBackButton.value)
    // updateChart()
  }
  // console.log(optionMap.value, '上一级')
  console.log(currentDimension.value, '当前层级')
}
watch(
  selectedOption,
  (newVal) => {
    // fetchChartData(newVal)
  },
  { deep: true }
)

onBeforeMount(() => {
  const objInfo = {
    selectOptions: selectedOption.value,
    rangTimeDateFrom: gisDateRangeTime.value[0],
    rangTimeDateTo: gisDateRangeTime.value[1],
    "prevDimensionFilter": "",
    "queryDimension": "战区",
  }

  fetchChartData(objInfo)
  window.addEventListener('resize', handleResize)
  // updateChart()
  // })
})
// 初始化图表
onMounted(() => {
  nextTick(() => {})
})
// 在组件卸载时销毁图表，并移除 resize 事件监听
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 样式可以根据需要自定义 */
.chartsMain {
  border: 1px solid #e2e8f0;
  height: 100%;
  width: 100%;
  padding: 20px;
  border-radius: 10px;
}
.backLevel {
  position: absolute;
  top: -8%;
  right: -30%;
}
</style>
