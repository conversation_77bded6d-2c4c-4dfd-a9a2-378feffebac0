<template>
  <div class="login-box">
    <div class="left-box">
      <div class="logo-title-tip">
        <svg-icon
          name="logo-title"
          width="496"
          height="121"
        ></svg-icon>
      </div>
      <div class="login-box-content">
        <div class="login-logo-box">
          <div class="log-title">高原病分析监测预警系统</div>
          <div class="log-title-desc">Analysis, Monitoring, and Early Warning System for High Altitude Diseases</div>
        </div>
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
        >
          <a-form-item
            ref="userName"
            name="userName"
            style="margin-bottom: 24px"
          >
            <a-input
              style="width: 100%"
              class="input-class"
              ref="usernameRef"
              :maxlength="20"
              placeholder="请输入用户名"
              v-model:value="formState.userName"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item
            ref="password"
            name="password"
          >
            <a-input-password
              style="width: 100%"
              class="input-class"
              placeholder="请输入密码"
              v-model:value="formState.password"
            >
              <template #prefix>
                <LockOutlined />
              </template>
            </a-input-password>
          </a-form-item>
          <!-- <a-form-item ref="campusId" name="campusId">
            <a-select style="width: 100%" class="select-class" allowClear placeholder="请选择院区"
              v-model:value="formState.campusId" popupClassName="login-select-dropdown-class">
              <a-select-option v-for="item in campusList" :key="item.key" :value="item.key">{{ item.value
                }}</a-select-option>
            </a-select>
          </a-form-item> -->
        </a-form>
        <div
          class="btn-login"
          @click="handleLogin"
        >
          登录
        </div>
      </div>
    </div>
    <div class="right-box"></div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, toRaw } from 'vue'
import dayjs from 'dayjs'
// ant-design-vue
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
// 登录
import { login } from '@/api/login'
// 路由
import { useRouter } from 'vue-router'
// store
import { useMeanStore } from '@/store/routes/menus.ts'
import { useUserStore } from '@/store/routes/user.ts'

const formState = reactive({
  userName: '',
  password: ''
  // code: '',
  // campusId: undefined
})
// const activeKey = ref('1') // 1: 账号密码登录 2:验证码登录
const formRef = ref()

const rules = {
  // phoneNo: [{ pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: '请输入正确手机号', trigger: 'change' }]
}

const user = useUserStore()
const menuStore = useMeanStore()

const router = useRouter()
const handleLogin = () => {
  // 用户名和密码都不为空
  if (!formState.userName || !formState.password) {
    message.error('请输入用户名和密码')
    return
  }
  // 用户名或密码错误
  if (formState.userName !== user.VALID_USERNAME || formState.password !== user.VALID_PASSWORD || formState.password.length < 6) {
    message.error('用户名或密码错误')
    return
  }

  let params = {
    ...toRaw(formState)
  }

  login(params)
    .then((res) => {
      if (res.code == 0) {
        let token: string = res.data
        // token 过期时间
        let tokenExpired = dayjs(new Date()).add(3, 'day').valueOf()

        localStorage.setItem('token', token)
        localStorage.setItem('tokenExpired', JSON.stringify(tokenExpired))

        // 存登录凭证
        user.SAVE_TOKEN({
          token,
          tokenExpired
        })
        // 存用户的账号信息
        user.SET_INFO({
          username: user.VALID_USERNAME,
          password: user.VALID_PASSWORD
        })

        menuStore.getMenu().then((res: any) => {
          router.replace('/')
        })
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
</script>

<style lang="less">
.login-select-dropdown-class {
  background-color: #57bda1;

  .ant-select-item-option {
    color: #fff;
  }
}

.login-box {
  position: relative;

  .left-box {
    background-image: url('/src/assets/png/denglubg.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    position: relative;
  }

  .input-class {
    border-radius: 4px;
    background-color: rgba(32, 158, 133, 0.2);
    border: 1px solid rgba(226, 232, 240, 0.5);

    .ant-input {
      color: #fff;
    }
  }

  .select-class {
    border-radius: 8px;

    .ant-select-selector {
      border-radius: 8px;
      background-color: rgba(32, 158, 133, 0.2);
      border: 1px solid rgba(226, 232, 240, 0.5);
    }

    .ant-select-clear {
      // background-color: rgba(32, 158, 133, 0.20);
      border-radius: 6px;
    }
  }

  & {
    width: 100%;
    height: 100%;
    display: flex;
  }

  .left-box {
    flex: 1;
  }

  .right-box {
    flex: 0 0 0px;
    display: flex;
    padding-top: 200px;
    justify-content: center;
  }

  .login-box-content {
    position: absolute;
    right: 230px;
    top: 240px;
    width: 420px;
    height: 430px;
    padding-top: 48px;
    padding-left: 35px;
    padding-right: 35px;
    padding-bottom: 24px;
    border: 1px solid rgba(45, 220, 210, 0.7);
    border-radius: 16px;
    background-image: linear-gradient(40.54deg, rgba(194, 216, 208, 0.5) 1.45%, rgba(14, 183, 152, 0.5) 131.72%);
  }

  .login-logo-box {
    padding-top: 10px;
    margin-bottom: 40px;
  }

  .tabsTop {
    width: 330px;
    text-align: center;
    height: 30px;
    line-height: 30px;
  }

  .btn-login {
    width: 100%;
  }

  .log-title {
    color: #fff;
    width: 100%;
    font-weight: 600;
    text-align: center;
    font-size: 28px;
  }

  .ant-input-prefix {
    color: #fff;
  }

  .ant-input-affix-wrapper > input.ant-input {
    background-color: transparent !important;
    color:#fff;
  }

  .log-title-desc {
    font-size: 10px;
    color: #fff;
    text-align: center;
    width: 100%;
    margin-bottom: 24px;
    white-space: nowrap;
  }

  .btn-login {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 4px;
    color: #fff;
    border: 1px solid #fff;
    cursor: pointer;
    margin-top: 60px;
  }

  .ant-input {
    &::placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }

  .ant-input-affix-wrapper .anticon.ant-input-password-icon {
    color: #fff !important;
  }
}

.logo-title-tip {
  position: absolute;
  top: 24px;
  left: 50px;
  z-index: 999;
}
</style>
