@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 不要直接用以下变量 以下变量为主题相关 请使用data-theme中的 */
  --color-light-background: #ffffff;
  --color-light-border: #f0f0f0;
  --color-light-text: #333333;

  --color-light-neutral-background: #fafafa;

  --color-light-disable-background: #f5f5f5;
  --color-light-disable-border: rgba(240, 240, 240, 1);
  --color-light-disable-text: rgba(0, 0, 0, 0.45);
  --color-light-divide: rgba(0, 0, 0, 0.06);
  --scorll-bar-color: rgba(0, 0, 0, 0.3);

  /* 以下变量为暗黑模式 */
  --color-dark-background: #333333;
  --color-dark-border: #0f0f0f;
  --color-light-text: #333333;
  --color-dark-text: #ffffff;
  --color-dark-bar-color: rgba(255, 255, 255, 0.3);

  --color-dark-neutral-background: #383838;

  --color-dark-disable-background: #0a0a0a;
  --color-dark-disable-border: rgba(#0f0f0f, 1);
  --color-dark-disable-text: rgba(#ffffff, 0.45);
  --color-dark-divide: rgba(255, 255, 255, 0.06);
  /* 以下变量是品牌色可以直接使用 */
  --neutral-bg-color: var(--color-light-neutral-background);
  --normal-bg-color: var(--color-light-background);
  --normal-border-color: var(--color-light-border);
  --normal-text-color: var(--color-light-text);
  --disable-bg-color: var(--color-light-disable-background);
  --disable-border-color: var(--color-light-disable-border);
  --disable-text-color: var(--color-light-disable-text);
  --divide-bg-color: var(--color-light-divide);

  --primary-color: #209e85;
  --primary-bg-color: #f0f5f4;
  --primary-dark-color: #1c8b74;
  --primary-light-color: #59dec2;
  --primary-text-color: #ffffff;
  --primary-text-pendding-color: #faad14;
  --primary-text-end-color: #ff4d4f;
  --primary-text-finish-color: #52c41a;

  --primary-active-bg-color: #e8fff7;
  --primary-active-text-color: #209e85;
  --primary-table-bg-color: #b5ecd9;
  --primary-table-active-bg-color: #60c5aa;

  --primary-topbar-bg-color: #209e85;
  --select-bg-color: #eefaf6;
  --scroll-bar-bg: var(--scorll-bar-color);
  --separate-bar-color: #eef3f2;
  --menu-item-selected-bg-color: #e6fffb;

  --table-thead-color: #f8fafc;
  --table-row-hover: #eff8f6;
  --select-selector-styles: #f8fafc;
  --bg-siderbar-color: #ffffff;
}

[data-theme='dark'] {
  --neutral-bg-color: var(--color-dark-neutral-background);
  --normal-bg-color: var(--color-dark-background);
  --normal-border-color: var(--color-dark-border);
  --normal-text-color: var(--color-dark-text);
  --disable-bg-color: var(--color-dark-disable-background);
  --disable-border-color: var(--color-dark-disable-border);
  --disable-text-color: var(--color-dark-disable-text);
  --divide-bg-color: var(--color-dark-divide);
  --scroll-bar-bg: var(--color-dark-bar-color);

  --menu-item-selected-bg-color: #121d1c;
  --table-thead-color: #555555;
  --table-row-hover: #555555;
  --select-selector-styles: #555555;
  --select-bg-color: #555555;
  --bg-siderbar-color: #555555;

  --color-light-text: #ffffff;
}

/* 选中菜单状态 */
.ant-menu-light .ant-menu-item-selected {
  background-color: var(--menu-item-selected-bg-color);
}

*:focus-visible {
  outline: none;
}

::-webkit-scrollbar {
  width: 8px;
  /* 滚动条宽度 */
  height: 8px;
  /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: transparent;
  /* 滚动条轨道背景色 */
  border-radius: 10px;
  /* 圆角 */
}

::-webkit-scrollbar-thumb {
  /* background: var(--primary-color); 滚动条颜色 */
  background: var(--scroll-bar-bg);
  /* 滚动条颜色 肖羽更改 */
  border-radius: 10px;
  /* 圆角 */
  cursor: pointer;
}

.flex-center {
  @apply flex items-center justify-center;
}

.active-list-item {
  @apply border-r-4 bg-primary-active-bg-color text-primary-active-text-color border-primary-color;
}

.active-card-item {
  @apply border-primary-color text-primary-active-text-color;
}

html,
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

@media print {

  *,
  *::before,
  *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }

  @page {
    margin: 0;
    size: 1100px 1556px;
  }

  a:not(.btn) {
    text-decoration: underline;
  }

  abbr[title]::after {
    content: ' ('attr(title) ')';
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }

  body {
    -webkit-print-color-adjust: exact;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  a:hover {
    color: #747bff;
  }

  button {
    background-color: #f9f9f9;
  }
}

@layer utilities {
  .spin-button-none {

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }

    -moz-appearance: textfield;
  }
}

@layer components {
  .card {
    @apply rounded-md shadow-md p-4 bg-normal-bg-color;
  }
}