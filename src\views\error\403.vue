<template>
  <a-result status="403" title="403" sub-title="Sorry, you are not authorized to access this page.">
    <template #extra>
      <a-button type="primary" @click="handleBackLogin">Back Login</a-button>
    </template>
  </a-result>
</template>
<script lang="ts" setup>
import { Result as AResult, <PERSON><PERSON> as <PERSON>utton } from 'ant-design-vue'
import { useRouter } from "vue-router";
import { useUserStore } from "@/store/routes/user";
import { setConfig, config } from '@/config/index'
const router = useRouter()
const userStore = useUserStore();

function handleBackLogin() {
  let service = localStorage.getItem("service");
  if (service && service == 'ALT') {
    setConfig({
      APP_CODE: service,
      REDIRECT_URI: import.meta.env.MODE == 'production' ? `${window.location.origin}/ams-front/home?service=${service}` : 'http://localhost:3004/?service=ALT'
    })
  }
  userStore.remove();
}

defineOptions({
  name: 'err404'
})
</script>

<style lang="less" scoped></style>
