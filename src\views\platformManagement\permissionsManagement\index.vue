<template>
  <div class="wrapper">
    <a-card title="数据权限管理" :bordered="false" :bodyStyle="{ padding: '0 24px', minHeight: 'calc(100vh - 180px)' }">
      <div class="clearfix" style="margin-top: 24px">
        <a-form :model="formData" layout="inline" class="form-data-inline clearfix">
          <a-form-item label="">
            <a-input-search allowClear="true" style="width: 300px" v-model:value="formData.description"
              placeholder="请输入用户名搜索" @search="onSearch" @clear="clearSerach" />
          </a-form-item>
        </a-form>
      </div>
      <div class="table">
        <table-list ref="myTable" :tableData="tableList" :tableProps="tableProps" :total="total"
          @changePage="handleChangePage">
          <template #authority="{ record }">
            <div style="display: inline-flex; flex-wrap: wrap; overflow: scroll;">
              <a-tag color="rgba(32, 158, 133, 0.1)"
                v-if="record.authority && record.authority[0].areaHospitals.length == 0" style="color: #209e85">
                <span>陆军</span>
              </a-tag>
              <span v-else-if="record.authority && record.authority[0].areaHospitals.length > 0"
                v-for="(item, index) in record.authority[0].areaHospitals" :key="index"
                style="width: 100%;max-height: 50px;overflow-y: scroll;margin-bottom: 3px;white-space: normal;-webkit-box-orient:vertical;"
                class="two-ellipsis">
                <a-tag v-if="item.area == '东部战区' && item.hospitals.length == 0" style="color: #209e85">
                  陆军/{{ item.area }}
                </a-tag>
                <a-tag v-if="item.area == '南部战区' && item.hospitals.length == 0" style="color: #209e85">
                  陆军/{{ item.area }}
                </a-tag>
                <a-tag v-if="item.area == '西部战区' && item.hospitals.length == 0" style="color: #209e85">
                  陆军/{{ item.area }}
                </a-tag>
                <a-tag v-if="item.area == '北部战区' && item.hospitals.length == 0" style="color: #209e85">
                  陆军/{{ item.area }}
                </a-tag>
                <a-tag v-if="item.area == '中部战区' && item.hospitals.length == 0" style="color: #209e85">
                  陆军/{{ item.area }}
                </a-tag>
                <a-tag v-if="item.area == '新疆军区' && item.hospitals.length == 0" style="color: #209e85">
                  陆军/{{ item.area }}
                </a-tag>
                <a-tag v-if="item.area == '西藏军区' && item.hospitals.length == 0" style="color: #209e85">
                  陆军/{{ item.area }}
                </a-tag>
                <a-tag v-else color="rgba(32, 158, 133, 0.1)" v-for="(j, k) in item.hospitals" :key="k"
                  style="color: #209e85">
                  <span>
                    陆军/{{ item.area }}/{{ j }}
                  </span>
                </a-tag>
              </span>
              <span v-else></span>
            </div>
          </template>
        </table-list>
      </div>
    </a-card>
    <div>
      <a-drawer title="编辑" width="450" :open="isOpenDrawer" @close="onClose" :closable="false"
        :footer-style="{ textAlign: 'right' }">
        <template #extra>
          <span @click="onClose" style="cursor: pointer">
            <CloseOutlined />
          </span>
        </template>
        <a-form ref="formRef" :model="drawerFormData" :rules="rules" :label-col="labelCol">
          <a-form-item label="账号" name="name">
            <a-input style="width: 100%" v-model:value="drawerFormData.account" disabled />
          </a-form-item>
          <a-form-item label="用户名" name="description">
            <a-input v-model:value="drawerFormData.name" disabled />
          </a-form-item>
          <a-form-item label="组织权限" name="authority">
            <a-cascader v-model:value="drawerFormData.authority" style="width: 320px" multiple
              :displayRender="displayRender" :options="options" placeholder="请选择组织权限"
              :show-checked-strategy="Cascader.SHOW_CHILD" :showArrow="true" :maxTagCount="4"
              @change="handleStatisticalScope"></a-cascader>
          </a-form-item>
          <!-- <pre>{{ authority }}</pre> -->
        </a-form>
        <template #footer>
          <a-button style="margin-right: 20px" @click="onClose">取消</a-button>
          <a-button type="primary" @click="onSubmit">确定</a-button>
        </template>
      </a-drawer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { reactive, getCurrentInstance, ref, useTemplateRef, toRaw, onMounted } from 'vue'
import { Form, Input, Select, InputNumber, Button, Space, Row, Col, message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import TableList from '@/components/TableList/index.vue'
import type { UnwrapRef } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import { Cascader, Tag } from 'ant-design-vue'
import type { CascaderProps } from 'ant-design-vue'
import { authorityList, areaHospitalData, authoritySave } from './index.api'

// 统计范围
const options: CascaderProps['options'] = ref<any>([{
  label: '陆军',
  value: '陆军',
  children: []
}])

const getAarea = async () => {
  const params = {}
  let option = []
  await areaHospitalData(params)
    .then((res) => {
      if (res.code == 0) {
        res.data[0].areaHospitals.forEach((item: { area: any; hospitals: any[] }) => {
          const areaItem = {
            label: item.area,
            value: item.area,
            children: []
          }
          // 根据A中的hospitals，添加医院数据到children
          item.hospitals.forEach((hospital) => {
            areaItem.children.push({
              label: hospital,
              value: hospital
            })
          })

          // 将转换后的展区数据加入B
          option.push(areaItem)
          options.value[0].children = option
        })
      }
      console.log(options, 'options')
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}

const selectedValues = ref<string[][]>([])
const authority = ref<{ area: string; hospitals: string[] }[]>([])

/** ✅ 确保 displayRender 正确返回已选项的文字 */
const displayRender = ({ labels }: { labels: string[] }) => labels.join(' / ')

function handleStatisticalScope(values: any) {
  console.log(values, 'value')
  const result: Record<string, string[]> = {}
  const selectedAreas = new Set(values.map((v) => v[0]))
  values.forEach(([area, hospital]) => {
    if (!hospital) return

    if (!result[area]) {
      result[area] = []
    }
    result[area].push(hospital)
  })

  // **确保 selectedValues 正确格式化，防止 UI 不显示已选项**
  selectedValues.value = Object.entries(result).flatMap(([area, hospitals]) => hospitals.map((hospital) => [area, hospital]))
}
const { proxy }: any = getCurrentInstance()
const $route = useRoute()
const router = useRouter()
let formData = reactive({
  description: ''
})
const onSearch = (val: any) => {
  console.log(val)
  formData.description = val
  getList(formData)
}
const clearSerach = (val: any) => {
  console.log(val)

  formData.description = ''
  getList(formData)
}
const tableList = ref([])
const tableProps = ref([
  {
    id: 1,
    title: '账号',
    dataIndex: 'account',
    key: 'account',
    ellipsis: true
  },
  {
    id: 2,
    title: '用户名',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true
  },
  {
    id: 3,
    title: '组织权限',
    dataIndex: 'authority',
    key: 'authority',
    ellipsis: true,
    width: "60%",
    slot: 'authority',

    // hideTooltip: true
  },
  {
    id: 4,
    title: '操作',
    dataIndex: 'operation',
    slot: 'operation',
    width: 40,
    buttons: [
      {
        name: '编辑',
        onclick: ({ record }) => {
          viewFile(record)
        }
      }
    ]
  }
])
const isOpenDrawer = ref(false)
const labelCol = { span: 5 }
// 上传表单
interface FormState {
  name: string
  account: string
  authority: Array
}

let drawerFormData: UnwrapRef<FormState> = reactive({
  name: '',
  account: '',
  authority: []
})

const formRef = useTemplateRef('formRef')
const rules: Record<string, Rule[]> = {
  authority: [
    { required: true, message: '请填写组织权限', trigger: 'blur' }
  ]
}

const onSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      let data = toRaw(drawerFormData)
      let params = {
        ...data
      }

      const obj = {
        account: params.account,
        name: params.name,
        authority: [{
          "areaHospitals": [],
          "type": "陆军"
        }],
      }
      if (params.authority.length == 33) {
        obj.authority = [
          {
            "areaHospitals": [],
            "type": "陆军"
          }
        ]
      } else {
        let arr: { area: any; hospitals: any[] }[] = []
        params.authority.forEach((item: any[]) => {
          const areaName = item[1] // 获取区域名称
          const hospitalName = item[2] // 获取医院名称

          // 查找B中是否已经存在该区域
          const existingArea = arr.find((area) => area.area === areaName)
          if (existingArea) {
            // 如果区域已存在，直接将医院名称添加到对应的hospitals数组中
            existingArea.hospitals.push(hospitalName)
          } else {
            // 如果区域不存在，创建新的区域对象，并将医院添加到hospitals数组
            arr.push({
              area: areaName,
              hospitals: [hospitalName]
            })
          }
        })
        obj.authority[0].areaHospitals = arr
      }



      // 此处将勾选某战区全选该战区下医院时  按后端要求传空数组
      obj.authority[0].areaHospitals.map(item => {
        if (item.area == '东部战区' && item.hospitals.length == 3) {
          item.hospitals = []
        }
        if (item.area == '南部战区' && item.hospitals.length == 2) {
          item.hospitals = []
        }
        if (item.area == '西部战区' && item.hospitals.length == 6) {
          item.hospitals = []
        }
        if (item.area == '北部战区' && item.hospitals.length == 4) {
          item.hospitals = []
        }
        if (item.area == '中部战区' && item.hospitals.length == 4) {
          item.hospitals = []
        }
        if (item.area == '新疆军区' && item.hospitals.length == 7) {
          item.hospitals = []
        }
        if (item.area == '西藏军区' && item.hospitals.length == 7) {
          item.hospitals = []
        }
      })

      await authoritySave(obj)
        .then((res) => {
          if (res.code == 0) {
            message.success('操作成功')
            onClose()
            getList(formData)
          }
        })
        .catch((err) => {
          console.log(err, 'err')
        })
    })
    .catch((error) => {
      message.warning('请填写必填项！')
      console.log('error', error)
    })
}
const onClose = () => {
  drawerFormData = Object.assign(drawerFormData, {
    name: '',
    account: '',
    authority: []
  })

  isOpenDrawer.value = false
}

const viewFile = (key: any) => {
  getAarea()
  console.log(key)
  isOpenDrawer.value = true
  const obj = toRaw(key)
  drawerFormData.name = obj.name
  drawerFormData.account = obj.account
  let option = [{
    "areaHospitals": [],
    "type": "陆军"
  }]
  if (obj.authority[0].areaHospitals && obj.authority[0].areaHospitals.length > 0) {
    // 处理已经为全选陆军的情况

    // 此处再做处理 如果传过来的值为空数组 说明该战区下的医院全选
    obj.authority[0].areaHospitals.map(item => {
      if (item.area == '东部战区' && item.hospitals.length == 0) {
        item.hospitals = ["陆军第73集团军医院", "陆军第72集团军医院", "陆军第71集团军医院"]
      }
      if (item.area == '南部战区' && item.hospitals.length == 0) {
        item.hospitals = ["陆军第75集团军医院", "陆军第74集团军医院"]
      }
      if (item.area == '西部战区' && item.hospitals.length == 0) {
        item.hospitals =
          ["陆军第958医院", "陆军军医大学第二附属医院", "陆军军医大学第一附属医院", "陆军特色医学中心", "陆军第76集团军医院", "陆军第77集团军医院"]
      }
      if (item.area == '北部战区' && item.hospitals.length == 0) {
        item.hospitals = ["陆军第78集团军医院", "陆军第79集团军医院", "陆军第80集团军医院", "陆军试验训练基地医院"]
      }
      if (item.area == '中部战区' && item.hospitals.length == 0) {
        item.hospitals =
          ["陆军第83集团军医院", "陆军第82集团军医院", "陆军军医大学士官学校附属医院", "陆军第81集团军医院"]
      }
      if (item.area == '新疆军区' && item.hospitals.length == 0) {
        item.hospitals =
          ["陆军第946医院", "新疆军区总医院", "陆军第950医院", "陆军第951医院", "陆军第949医院", "陆军第947医院", "陆军第948医院"]
      }
      if (item.area == '西藏军区' && item.hospitals.length == 0) {
        item.hospitals =
          ["陆军第953医院", "陆军第955医院", "西藏军区总医院", "陆军第954医院", "陆军第952医院", "陆军第956医院", "陆军第957医院"]
      }

    })
    option = obj.authority[0].areaHospitals.flatMap(item =>
      item.hospitals.map(hospital => ['陆军', item.area, hospital])
    );
    drawerFormData.authority = option
    console.log(option);
  } else {
    obj.authority[0].areaHospitals = [

      {
        "area": "东部战区",
        "hospitals": [
          "陆军第73集团军医院",
          "陆军第72集团军医院",
          "陆军第71集团军医院"
        ]
      },
      {
        "area": "南部战区",
        "hospitals": [
          "陆军第75集团军医院",
          "陆军第74集团军医院",
        ]
      },
      {
        "area": "西部战区",
        "hospitals": [
          "陆军第958医院",
          "陆军军医大学第二附属医院",
          "陆军军医大学第一附属医院",
          "陆军特色医学中心",
          "陆军第76集团军医院",
          "陆军第77集团军医院",
        ]
      },
      {
        "area": "北部战区",
        "hospitals": [
          "陆军第78集团军医院",
          "陆军第79集团军医院",
          "陆军第80集团军医院",
          "陆军试验训练基地医院",
        ]
      },
      {
        "area": "中部战区",
        "hospitals": [
          "陆军第83集团军医院",
          "陆军第82集团军医院",
          "陆军军医大学士官学校附属医院",
          "陆军第81集团军医院",

        ]
      },
      {
        "area": "新疆军区",
        "hospitals": [
          "陆军第946医院",
          "新疆军区总医院",
          "陆军第950医院",
          "陆军第951医院",
          "陆军第949医院",
          "陆军第947医院",
          "陆军第948医院",
        ]
      },
      {
        "area": "西藏军区",
        "hospitals": [
          "陆军第953医院",
          "陆军第955医院",
          "西藏军区总医院",
          "陆军第954医院",
          "陆军第952医院",
          "陆军第956医院",
          "陆军第957医院",

        ]
      },
    ]

    option = obj.authority[0].areaHospitals.flatMap(item =>
      item.hospitals.map(hospital => ['陆军', item.area, hospital])
    );
    drawerFormData.authority = option

  }
  

}

const total = ref(0)
const pagiNation = reactive({
  current: 1,
  size: 10
})
// 分页list
async function getList(formData: { description: string }) {
  let params = {
    content: formData.description,
    current: pagiNation.current,
    size: pagiNation.size,
    // appCode:'INF',
  }

  await authorityList(params)
    .then((res) => {
      if (res.code == 0) {
        tableList.value = res?.data?.records ?? []
        total.value = res?.data?.total ?? 0
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
// 分页事件
function handleChangePage({ pageNum, pageSize }) {
  pagiNation.current = pageNum
  pagiNation.size = pageSize
  getList(formData)
}
// 获取所有战区医院

onMounted(() => {
  getList(formData)
})
</script>

<style scoped lang="less">
::v-deep .ant-tabs-nav-list {
  padding-left: 36px;
}

.table {
  margin-top: 24px;
}
</style>
