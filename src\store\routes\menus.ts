// @src/store/menus.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { addRoute } from './addRoutes'
import { routes, mockRoutes } from '@/router/index'
// import { getMenuList } from "@/api/login"
const getMenuList = () => {
  return new Promise((resolve) => {
    resolve({
      code: 0,
      data: mockRoutes
    })
  })
}

interface AddRoute {
  id?: Number
  title: String
  icon: String
  path?: String
  name: String
  component: String
  children?: []
}

export const useMeanStore = defineStore(
  'menus',
  () => {
    // 菜单数据
    const menuList = ref([] as AddRoute[])

    // 是否有路由
    const hasRoute = ref(false)
    // 改变路由状态
    function changeRouteStatus(state: any) {
      hasRoute.value = state
      sessionStorage.setItem('hasRoute', state)
    }

    // 设置菜单数据
    function setMenuList(menus: any[]) {
      let addRouterList = routes.filter((route) => !route?.meta?.notDetect)
      menuList.value = [...addRouterList, ...menus]
      // 生成动态路由
      addRoute(menus)
    }

    // 获取菜单
    async function getMenu() {
      await getMenuList().then((res: any) => {
        setMenuList(res.data)
        // setPermList(res.data.authority)
      })
    }

    return {
      menuList,
      hasRoute,
      changeRouteStatus,
      setMenuList,
      getMenu
    }
  },
  {
    // persist: true
  }
)
