import request from '@/utils/request'

// 获取知识库树形数据
export function getKnowledgeNodeTree(params: any) {
  return request({
    url: '/altKnowledgeNode/selectTreeStructure',
    method: 'get',
    params: params
  })
}

//   获取树对应的指南信息--分页
export function guideQueryPage(query: any) {
  return request({
    url: '/altKnowledgeGuide/listPage',
    method: 'get',
    params: query
  })
}

// 下载文件
export function getPdfFile(query: any) {
  return request({
    url: '/file/download',
    method: 'get',
    params: query,
    // headers: {
    //   Accept: 'application/octet-stream', // 请求文件流
    // },
    responseType: 'blob'
  })
}

export function guideGraph(query: any) {
  return request({
    url: '/altKnowledgeGuide/list',
    method: 'get',
    params: query
  })
}

// 获取知识图谱信息
export function guideSearchGraph(query: any) {
  return request({
    url: '/infKnowledgeGraph/listName',
    method: 'get',
    params: query
  })
}

// 获取知识图谱树形数据
export function getKnowledgeTree(params: any) {
  return request({
    url: '/infKnowledgeGraph/selectTreeStructure',
    method: 'post',
    data: params
  })
}

// 获取知识图谱节点信息
export function getInfKnowledgeGuide(params: any) {
  return request({
    url: '/altKnowledgeGuide/getById',
    method: 'get',
    params: params
  })
}
