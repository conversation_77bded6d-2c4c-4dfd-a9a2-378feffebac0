<template>
    <div></div>
</template>
  
  <script setup lang="ts">
  import { reactive,getCurrentInstance, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { operationLog } from '@/api/log'
  const router = useRouter()
  
  onMounted(()=>{
    window.open('http://*************/bigdata-platform/iddb/filter/index?disease=ams','_blanck');
    router.push({ name: 'monitoringReports' })
  })
  const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '查看高原病患者信息',
    ip: baseIP,
    type: '高原病患者管理'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
onMounted(() => {
 
  operationLogs()
})
  </script>
  <style scoped>
  </style>
  