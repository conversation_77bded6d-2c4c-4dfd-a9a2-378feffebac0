<template>
  <div style="">
    <a-card title="基本设置" :bordered="false" :bodyStyle="{ padding: '24px', minHeight: 'calc(100vh - 180px)' }">
      <template #extra>
        <a-button style="float: right" @click="backListButton"> 返回 </a-button>
      </template>

      <a-form ref="formRef" name="custom-validation" :model="state.formState" :labelCol="{ span: 1 }">
        <a-form-item label="账号" name="username">
          <!-- <a-input style="width:300px" v-model:value=" /> -->
          <div style="margin-left:5px">
            {{ state.formState.username }}
          </div>
        </a-form-item>
        <a-form-item label="用户名" name="displayName" :rules="[{ required: true, message: '该字段是必填字段' }]">
          <!-- <a-input style="width:300px" v-model:value="" /> -->
          <div style="margin-left:5px">
            {{ state.formState.displayName }}
          </div>
        </a-form-item>
        <a-form-item label="用户角色" name="roles">
          <!-- <a-select style="width:300px" mode="multiple" v-model:value="state.formState.roles" placeholder="请选择">
            <a-select-option :value="item.roleCode" v-for="item in state.rolesList" :key="item.id">
              {{ item.roleName }}
            </a-select-option>
          </a-select> -->
          <div style="margin-left:5px">
            <a-tag v-for="item in state.formState.userRoles" :key="item.userId">
              {{ item.roleName }}
            </a-tag>
          </div>
        </a-form-item>
        <a-form-item style="margin-top: 30px;">
          <a-button style="margin-right: 10px" @click="handleEditPassword">修改密码</a-button>
          <a-button type="primary" @click="handleEditUserName">修改用户名</a-button>
        </a-form-item>
      </a-form>

      <!-- 弹窗 -->
      <div class="modal-box">
        <a-modal :maskClosable="false" :closable="false" v-model:open="state.isOpenModal" title="修改密码"
          @ok="handleOpenModalOk" @cancel="handleCancelModalOk">
          <div>
            <a-form :labelCol="{ span: 4 }" labelAlign="left" :model="state.formData" name="basic" autocomplete="off">
              <a-form-item label="旧密码" name="oldPassword"
                :rules="[{ required: true, message: '密码6-18位字符,不包含空格', min: 6, max: 20, }]">
                <a-input-password v-model:value="state.formData.oldPassword" />
              </a-form-item>
              <a-form-item label="新密码" name="newPassword"
                :rules="[{ required: true, message: '密码6-18位字符,不包含空格', min: 6, max: 20, }]">
                <a-input-password v-model:value="state.formData.newPassword" />
              </a-form-item>
              <a-form-item label="确认密码" name="confirmPassword"
                :rules="[{ required: true, message: '密码6-18位字符,不包含空格', min: 6, max: 20, }]">
                <a-input-password v-model:value="state.formData.confirmPassword" />
              </a-form-item>
            </a-form>
          </div>
        </a-modal>

        <a-modal :maskClosable="false" :closable="false" v-model:open="state.isOpenUserModal" title="修改用户名"
          @ok="handleOpenUserModalOk" @cancel="handleCancelUserModalOk">
          <div>
            <a-form :labelCol="{ span: 4 }" labelAlign="left" :model="state.formUserData" name="basic"
              autocomplete="off">
              <a-form-item label="用户名" name="newName"
                :rules="[{ required: true, message: '请输入用户名', min: 6, max: 20, }]">
                <a-input v-model:value="state.formUserData.newName" />
              </a-form-item>
            </a-form>
          </div>
        </a-modal>
      </div>
    </a-card>
  </div>
</template>

<script lang='ts' setup>
import { onMounted, reactive, toRaw } from 'vue'
import { getUsers, getEditUsersPassword, getEditUserName, getRolesList } from '@/api/user'
import { useRouter, useRoute } from 'vue-router'
import { message } from "ant-design-vue"
import { useUserStore } from '@/store/routes/user'

const router = useRouter()
const route = useRoute()

const userStore = useUserStore()

const state = reactive({
  formState: {
    username: '',
    displayName: '',
    roles: []
  },
  formData: {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  },
  formUserData: {
    newName: '',
  },
  isOpenModal: false,
  isOpenUserModal: false,
  rolesList: [],
})
const backListButton = () => { router.go(-1); }
const handleEditPassword = () => {
  state.isOpenModal = true
}
const handleEditUserName = () => {
  state.isOpenUserModal = true
  state.formUserData.newName = state.formState.displayName
}
const handleOpenModalOk = () => {
  const userId = route.query.userId || JSON.parse(localStorage.getItem('userInfo')).id
  let params = {
    ...toRaw(state.formData),
  }
  getEditUsersPassword(userId, params).then((res) => {
    if (res.code == 200) {
      message.success('修改成功')
      handleCancelModalOk()
      userStore.remove('登入登出')
    }
  }).catch((err) => {
    console.log(err, 'err')
  })
}

const handleCancelModalOk = () => {
  state.isOpenUserModal = false
  Object.assign(state.formUserData, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  })
}

const handleCancelUserModalOk = () => {
  state.isOpenModal = false
  Object.assign(state.formData, {
    newName: ''
  })
}

const handleOpenUserModalOk = () => {
  const userId = route.query.userId || JSON.parse(localStorage.getItem('userInfo')).id
  let params = {
    ...toRaw(state.formUserData),
  }
  getEditUserName(userId, params).then((res) => {
    if (res.code == 200) {
      message.success('修改成功')
      handleCancelModalOk()
      getPageList()
    }
  }).catch((err) => {
    console.log(err, 'err')
  })
}

async function getPageList() {
  const userId = route.query.userId || JSON.parse(localStorage.getItem('userInfo')).id
  await getUsers(userId).then((res) => {
    if (res.code == 200) {
      state.formState = {
        ...res?.data,
        // roles: res?.data?.userRoles.map((item) => item.roleCode)
      }
    }
  }).catch((err) => {
    console.log(err, 'err')
  })
}

const getRoles = async () => {
  let params = {
    pageSize: 9999,
    pageNum: 1,
    appCode: 'ALT'
  }
  await getRolesList(params).then((res) => {
    if (res.code == 200) {
      state.rolesList = res?.data ?? []
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

async function query() {
  // await getRoles()
  await getPageList()
}
onMounted(() => {
  query()
})
</script>

<style scoped lang='less'></style>