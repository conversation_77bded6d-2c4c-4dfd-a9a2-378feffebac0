<template>
  <div class="wrapper">
    <a-card title="日志审计管理" :bordered="false" :bodyStyle="{ padding: '0 24px', minHeight: 'calc(100vh - 180px)' }">
      <div class="clearfix">
        <a-form :model="formData" layout="inline" class="form-data-inline clearfix">
          <a-form-item>
            <a-input-group compact class="input-group-styles">
              <a-select class="select-styles" v-model:value="selectedLabel" placeholder="请选择" @change="changeSelect">
                <a-select-option value="userAccount">账号</a-select-option>
                <a-select-option value="userName">操作人</a-select-option>
              </a-select>
              <span v-if="selectedLabel == 'userAccount'">
                <a-auto-complete class="auto-input-styles" style="width: 200px" :dropdown-match-select-width="252"
                  :options="accountSuggestions" placeholder="请输入账号" v-model:value="formData.userAccount"
                  @input="handleAccountInput" :fetch-suggestions="fetchAccountSuggestions" @select="handleAccountSelect"
                  :allowClear="true" @clear="clearAutoAccount" ref="autoCompleteRef">
                </a-auto-complete>
              </span>
              <span v-else-if="selectedLabel == 'userName'">
                <a-auto-complete class="auto-input-styles" style="width: 200px" :dropdown-match-select-width="252"
                  :options="nameSuggestions" placeholder="请输入操作人" v-model:value="formData.userName"
                  @input="handleNameInput" :fetch-suggestions="fetchNameSuggestions" @select="handleNameSelect"
                  :allowClear="true" @clear="clearAutoName" ref="autoCompleteRef">
                </a-auto-complete>
              </span>
            </a-input-group>
          </a-form-item>
          <a-form-item label="操作类型">
            <a-select style="width: 260px" mode="multiple" v-model:value="formData.operationCategories" placeholder="请选择"
              @change="getOprateTypesAll">
              <a-select-option :value="item.code" v-for="item in statusList" :key="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="range-picker" label="时间范围">
            <a-range-picker allowClear v-model:value="formData.dates"  
              :presets="presetTimeRange()" value-format="YYYY-MM-DD"  />
              <!-- :disabled-date="disabledStartDate" -->
          </a-form-item>
          <a-form-item label="异常行为">
            <a-radio-group v-model:value="formData.behaviorStatus">
              <a-radio value="正常">正常</a-radio>
              <a-radio value="异常">异常</a-radio>
            </a-radio-group>
          </a-form-item>
          <div class="float-right flex flex-1 flex-col  w-70px btn-export">
            <a-button @click="handleClear" class="letter-text" style="margin-right: 16px">清空</a-button>
            <a-button type="primary" class="letter-text" @click="handleSearch(formData)">搜索</a-button>
          </div>
        </a-form>
      </div>


      <div class="table">
        <table-list ref="myTable" :tableData="tableList" :tableProps="tableProps" :total="total"
          @changePage="handleChangePage">
          <!-- <template #ip="{ record }">
            <div v-if="record.behaviorStatus == '正常'">
              <span style="color: #209e85">
                {{ record.ip }}
              </span>
            </div>
            <div v-else>
              <span style="color: #ff4d4f">
                {{ record.ip }}
              </span>
            </div>
          </template>
<template #userAccount="{ record }">
            <div v-if="record.behaviorStatus == '正常'">
              <span style="color: #209e85">
                {{ record.userAccount }}
              </span>
            </div>
            <div v-else>
              <span style="color: #ff4d4f">
                {{ record.userAccount }}
              </span>
            </div>
          </template> -->

          <template #behaviorStatus="{ record }">
            <div>
              <a-tooltip v-if="record.behaviorStatus == '异常'">
                <template #title>
                  <div>异地IP登录</div>
                </template>
                <InfoCircleOutlined :style="{
                  color: '#FF4D4F',
                  fontSize: '16px',
                  width: '22px',
                  height: '22px'
                }" />
              </a-tooltip>

            </div>
          </template>
        </table-list>
      </div>

    </a-card>
  </div>
</template>

<script setup lang="ts">
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { reactive, getCurrentInstance, ref, onMounted, toRef, useTemplateRef, nextTick } from 'vue'
import { Form, Input, Select, InputNumber, Button, Space, Row, Col } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import TableList from '@/components/TableList/index.vue'
import { getoperationLogList, getoperationLogOprator,getuserAccountsList,getuserNamesList } from './index.api.ts'
import { presetTimeRange } from '@/utils/utils'
import dayjs from 'dayjs'
import { debounce } from "lodash";

// 时间配置
const disabledStartDate = (current: dayjs.Dayjs) => current.isBefore(dayjs('2022-01-01'))

const { proxy }: any = getCurrentInstance()
const $route = useRoute()
const router = useRouter()


interface FormData {
  userAccount: string;
  userName: string;
  operationCategories: [];
  dates: string[];
  behaviorStatus: string;
}
let formData = reactive<FormData>({
  userAccount: "",
  userName: "",
  operationCategories: [],
  dates: [],
  behaviorStatus: ''
});

const statusList = ref([])

// 获取所有操作类型
async function getOpratorList() {
  let params = {}
  await getoperationLogOprator(params)
    .then((res) => {
      if (res.code == 0) {
        if (res.data && res.data.length > 0)
          statusList.value = res.data
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
const getOprateTypesAll = (value: any) => {
  console.log(value);
  formData.operationCategories = value
}

const selectedLabel = ref("userAccount");
const isSelectDropdownOpen = ref(false);

const autoCompleteRef = useTemplateRef("autoCompleteRef");

const changeSelect = (value: string) => {
  console.log(value);

  if (value == 'userName') {
    formData.userAccount = ''
  } else {
    formData.userName = ''
  }

  isSelectDropdownOpen.value = false;

  // 在下次DOM更新循环后执行，确保相关操作在DOM更新完成后进行
  nextTick(() => {
    // 阻止事件冒泡，这里使用原生的事件对象获取方式
    const event = window.event || {};
    // 防止 a-auto-complete自动获取焦点
    if (autoCompleteRef.value) {
      autoCompleteRef.value.$el.blur();
    }
  });
};


// 操作人
const nameSuggestions = ref<any[]>([]);
// 操作人姓名输入
const handleNameInput = debounce(async (e: any) => {
  const value = e.target.value;
  await fetchNameSuggestions(value);
}, 1000);
// 操作人下拉选项的事件
const handleNameSelect = (values: any) => {
  formData.userName = values;
  clearAutoAccount()
  // getIdcardAarr(values);
};
// 清空
function clearAutoName() {
  nameSuggestions.value = [];
  formData.userName = "";
}
// 远程 接口请求
const fetchNameSuggestions = async (value: string) => {
  getNameAarr(value);
};
async function getNameAarr<T>(values: T) {
  nameSuggestions.value = [];
  const obj = {
    value: values,
  };
  await getuserNamesList(obj).then((res: any) => {
    if (res?.code == 0) {
      return res.data.map((item: any) =>
        nameSuggestions.value.push({
          value: item,
          label: item,
        })
      );
    } else {
      proxy.$message.error(res?.errorMsg);
    }
  });
}
// 账户

const accountSuggestions = ref<any[]>([]);
// 操作人姓名输入
const handleAccountInput = debounce(async (e: any) => {
  const value = e.target.value;
  await fetchAccountSuggestions(value);
}, 1000);
// 操作人下拉选项的事件
const handleAccountSelect = (values: any) => {
  formData.userAccount = values;
  clearAutoName()
  // getIdcardAarr(values);
};
// 清空
function clearAutoAccount() {
  accountSuggestions.value = [];
  formData.userAccount = "";
}
// 远程 接口请求
const fetchAccountSuggestions = async (value: string) => {
  getAccountAarr(value);
};
async function getAccountAarr<T>(values: T) {
  accountSuggestions.value = [];
  const obj = {
    value: values,
  };
  await getuserAccountsList(obj).then((res: any) => {
    if (res?.code == 0) {
      return res.data.map((item: any) =>
      accountSuggestions.value.push({
          value: item,
          label: item,
        })
      );
    } else {
      proxy.$message.error(res?.errorMsg);
    }
  });
}


// 搜索事件
function handleSearch(value: any) {
  console.log(value);

  let data = {
    userAccount: formData.userAccount,
    userName: formData.userName,
    operationCategories: formData.operationCategories,
    dates: formData.dates,
    behaviorStatus: formData.behaviorStatus
  };

  pagiNation.current = 1;
  pagiNation.size = 10;
  formData = Object.assign(formData, data);
  getList(data);
}

// 清空事件
function handleClear() {
  let data = {
    userAccount: "",
    userName: "",
    operationCategories: [],
    dates: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    behaviorStatus: ''
  };

  pagiNation.current = 1;
  pagiNation.size = 10;
  formData = Object.assign(formData, data);
  getList(formData);
}

const tableList = ref([])
const tableProps = ref([
  {
    id: 8,
    title: 'IP地址',
    dataIndex: 'ip',
    key: 'ip',
    ellipsis: true,
    // slot: 'ip'
  },
  {
    id: 1,
    title: '账号',
    dataIndex: 'userAccount',
    key: 'userAccount',
    ellipsis: true,
    // slot: 'userAccount'
  },

  {
    id: 9,
    title: '操作人',
    dataIndex: 'userName',
    key: 'userName'
  },
  // {
  //   id: 2,
  //   title: '操作人',
  //   dataIndex: 'description',
  //   key: 'description',

  // },
  // {
  //   id: 3,
  //   title: '角色',
  //   dataIndex: 'status',
  //   key: 'status',
  //   ellipsis: true,
  //   width: 100,
  //   slot: 'status',

  //   hideTooltip: true
  // },
  {
    id: 4,
    title: '操作模块',
    dataIndex: 'type',
    key: 'type',
    ellipsis: true
  },
  {
    id: 5,
    title: '操作类型',
    dataIndex: 'category',
    key: 'category',

    ellipsis: true
  },
  {
    id: 6,
    title: '操作时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 80,
    ellipsis: true
  },
  {
    id: 7,
    title: '异常行为标志',
    dataIndex: 'behaviorStatus',
    key: 'behaviorStatus',
    ellipsis: true,
    slot: 'behaviorStatus',
    hideTooltip: true
  }
])

const total = ref(0)
const pagiNation = reactive({
  current: 1,
  size: 10
})
// 获取日志列表
async function getList(value: { userAccount: string; userName: string; operationCategories: []; dates: string[]; behaviorStatus: string }) {
  let params = {
    current: pagiNation.current,
    size: pagiNation.size,
    userAccount: value.userAccount,
    userName: value.userName,
    operationCategories: value.operationCategories,
    from: value.dates[0],
    to: value.dates[1],
    behaviorStatus: value.behaviorStatus
  }

  // "behaviorStatus": "",
  // "current": 0,
  // "from": "",
  // "operationCategories": [],
  // "size": 0,
  // "to": "",
  // "userAccount": "",
  // "userName": ""

  await getoperationLogList(params)
    .then((res) => {
      if (res.code == 0) {
        tableList.value = res?.data?.records ?? []
        total.value = res?.data?.total ?? 0
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
// 分页事件
function handleChangePage({ pageNum, pageSize }) {
  pagiNation.current = pageNum
  pagiNation.size = pageSize
  getList(formData)
}

onMounted(() => {
  getOpratorList()
  formData.dates = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  console.log(formData, 99000);

  getList(formData)
})
</script>

<style scoped lang="less">
::v-deep .ant-tabs-nav-list {
  padding-left: 36px;
}

// .form-data-inline {
//   width: 100%;
//   display: inline-flex;
// }

// .table {
//   margin-top: 24px;
// }

.btn-export {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-bottom: 24px;
}

:deep(.select-styles .ant-select-selector) {
  border-radius: 4px 0 0 4px !important;
  width: 100px;
  background-color: var(--select-selector-styles);
}

:deep(.input-group-styles .auto-input-styles) {
  border-radius: 0px 4px 4px 0 !important;
}

.form-data-inline {
  width: 100%;
  display: inline-flex;
  justify-content: space-between;
  flex-wrap: wrap;

  .ant-form-item {
    margin-bottom: 16px;
    margin-right: 24px;
  }

  .ant-form-item:nth-last-child(2) {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
  }
}

.btn-export {
  width: 70px;
  height: 34px;
  display: flex;
  justify-content: flex-end;
  flex-direction: row;
  float: right;
  flex: 1;
}
</style>
