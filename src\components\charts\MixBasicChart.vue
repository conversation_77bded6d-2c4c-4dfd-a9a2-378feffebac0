<template>
  <div
    ref="chartContainer"
    :style="{ width, height }"
  ></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  option: {
    type: Object,
    required: true // option 是必须的
  },
  width: {
    type: String,
    default: '100%' // 默认宽度
  },
  height: {
    type: String,
    default: '100%' // 默认高度
  },
  up: {
    type: Function,
    required: false
  }
})

const chartContainer = ref(null)
let chartInstance = null
const emit = defineEmits() // 用来触发事件

// 初始化图表
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose() // 销毁已有实例，避免重复渲染
  }
  chartInstance = echarts.init(chartContainer.value)
  chartInstance.on('click', (params) => {
    // 通过 emit 触发 'up' 事件，传递参数
    emit('up', params)
  })
  chartInstance.setOption(props.option)
}

// 监听 option 的变化并更新图表
watch(
  () => props.option,
  (newOption) => {
    if (chartInstance) {
      console.log(newOption, 'newOption')
      chartInstance.clear()
      chartInstance.setOption(newOption)
    }
  },
  { deep: true } // 深度监听
)

// 处理窗口大小变化时，重新调整图表尺寸
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 在组件挂载时初始化图表，并添加 resize 事件监听
onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

// 在组件卸载时销毁图表，并移除 resize 事件监听
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 可以根据需要设置图表的样式 */
</style>
