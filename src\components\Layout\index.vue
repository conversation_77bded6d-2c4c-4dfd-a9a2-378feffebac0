<template>
  <a-layout class="sider-layout">
    <!-- 滚回顶部 -->
    <a-back-top
      :target="targetFn"
      :visibilityHeight="120"
    />
    <a-layout-sider
      v-model:collapsed="store.collapse"
      :trigger="null"
      collapsible
    >
      <!-- logo -->
      <Logo :collapsed="store.collapse" />
      <!-- sidebar -->
      <Siderbar />
      <!-- 收缩按钮 -->
      <div
        class="shrink-btn"
        @click="store.switchCollapse"
      >
        <svg-icon
          name="collapsed"
          v-if="store.collapse"
          className="my"
          color="#209E85"
          width="32"
          height="80"
        />
        <svg-icon
          name="collapsed-close"
          v-if="!store.collapse"
          className="my"
          color="#209E85"
          size="32"
          height="80"
        />
      </div>
    </a-layout-sider>
    <a-layout class="main-content">
      <!-- header -->
      <Header />
      <!-- main -->
      <Main />
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import Header from './header/index.vue'
import Main from './main/index.vue'
import Siderbar from './siderbar/index.vue'
import Logo from './logo/index.vue'
import { useCollapseStore } from '@/store/side/collapse'
import { onMounted } from 'vue'

const store = useCollapseStore()
const targetFn = () => {
  return document.getElementById('main-info-content')
}
</script>

<style lang="less" scoped>
:deep(.ant-layout-sider) {
  height: var(--sidebar-height, 100vh) !important;
  width: var(--sidebar-width) !important;
  min-width: var(--sidebar-width) !important;
  max-width: var(--sidebar-width) !important;
  background-color: #ffffff;
}

:deep(.ant-layout-sider-collapsed) {
  width: var(--sidebar-collapsed-width) !important;
  min-width: var(--sidebar-collapsed-width) !important;
  max-width: var(--sidebar-collapsed-width) !important;
}

.shrink-btn {
  top: 360px;
  right: -20px;
  position: absolute;
  z-index: 999;
  cursor: pointer;
}
</style>
