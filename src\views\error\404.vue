<template>
  <a-result status="404" title="404" sub-title="Sorry, the page you visited does not exist.">
    <template #extra>
      <a-button type="primary" @click="handleBackHome" v-if="!isSource">Back Home</a-button>
    </template>
  </a-result>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { Result as AResult, Button as AButton } from 'ant-design-vue'
import { usePageSourceStore } from '@/store/pageSource'
import { useRouter } from "vue-router";
import { storeToRefs } from 'pinia';
const router = useRouter()

const pageSourceStore = usePageSourceStore();
const { getPageSource } = storeToRefs(pageSourceStore)
console.log("🚀 ~ getPageSource:", getPageSource)
const isSource = computed(() => getPageSource.value == 'specialDiseaseBank')
function handleBackHome() {
  router.replace({
    path: '/home'
  })
}
defineOptions({
  name: 'err404'
})
</script>

<style lang="less" scoped></style>
