import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import { ref, h } from 'vue'
import { Modal, Textarea, message, type UploadProps } from 'ant-design-vue'

export const statusEnum = {
  0: '正常',
  1: '删除',
  2: '待审核',
  3: '已审核',
  4: '已驳回'
}

export const levelEnum = [
  {
    code: 'level1Cn',
    name: '一级节点（疾病大类）'
  },
  {
    code: 'level2Cn',
    name: '二级节点（疾病名称）'
  },
  {
    code: 'level3Cn',
    name: '三级节点（疾病属性）'
  },
  {
    code: 'level4Cn',
    name: '四级节点（属性详情）'
  },
  {
    code: 'level5Cn',
    name: '五级节点（来源）'
  }
]

export const rejectPopUpWindow = (component: any, props = {}, slots = {}) => {
  let reason = ref<string | undefined>()
  let propsData = {
    style: {
      width: '100%',
    },
    onInput: (e) => {
      reason.value = e.target.value
    },
    placeholder: '请输入驳回原因',
    title: "驳回原因：",

    labelStyle: {
      width: '100px',
    },
    ...props,
    labelClass: `input-label ${props?.labelClass}`
  }
  let com = component || Textarea
  let vnode = h('div',
    {
      class: "input-container",
      style: {
        width: '100%',
        display: 'flex',
        padding: '20px 0 30px',
        borderTop: '1px solid #f0f0f0',
      }
    },
    [
      h('span', { class: propsData.labelClass, style: propsData.labelStyle }, propsData.title),
      h(com, {
        class: propsData.labelClass,
        style: propsData.style,
        placeholder: propsData.placeholder,
        onInput: propsData.onInput,
      }, { ...slots, })
    ])
  return { vnode, reason }
}

export async function convertToPDF(document: Document) {
  const doc: any = new jsPDF()
  const canvas = await html2canvas(document.body)
  const imgData = canvas.toDataURL('image/png')
  const imgProps = doc.getImageProperties(imgData)
  const pdfWidth = doc.internal.pageSize.getWidth()
  const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width
  doc.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight)

  // 实现模糊搜索功能
  const searchInput = document.createElement('input')
  searchInput.type = 'text'
  searchInput.placeholder = '输入搜索内容'
  document.body.appendChild(searchInput) // 将input添加到新打开页面的body中

  searchInput.addEventListener('input', () => {
    const searchText = searchInput.value.toLowerCase()
    const pageElements = doc.internal.pages.map((page: any) => page.canvas).flat()
    pageElements.forEach((page: any) => {
      const pageContent = page.getContext('2d')!.getTextContent()!.toLowerCase()
      const isMatch = pageContent.includes(searchText)
      page.style.display = isMatch ? 'block' : 'hidden'
    })
  })

  // 实现分页功能
  const totalPages = doc.internal.getNumberOfPages()
  const paginationDiv = document.createElement('div')
  paginationDiv.style.textAlign = 'center'
  for (let i = 1; i <= totalPages; i++) {
    const pageButton = document.createElement('button')
    pageButton.textContent = `${i}`
    pageButton.addEventListener('click', () => {
      doc.setPage(i)
      const pageCanvas = doc.internal.pages[i - 1].canvas
      pageCanvas.style.display = 'block'
      for (let j = 1; j <= totalPages; j++) {
        if (j !== i) {
          doc.internal.pages[j - 1].canvas.style.display = 'hidden'
        }
      }
    })
    paginationDiv.appendChild(pageButton)
  }
  document.body.appendChild(paginationDiv)

  // 实现缩放功能
  const zoomInButton = document.createElement('button')
  zoomInButton.textContent = '放大'
  zoomInButton.addEventListener('click', () => {
    const currentScale = doc.internal.scale
    doc.internal.scale = currentScale * 1.2
    doc.output('pdf')
  })
  const zoomOutButton = document.createElement('button')
  zoomOutButton.textContent = '缩小'
  zoomOutButton.addEventListener('click', () => {
    const currentScale = doc.internal.scale
    doc.internal.scale = currentScale / 1.2
    doc.output('pdf')
  })
  document.body.appendChild(zoomInButton)
  document.body.appendChild(zoomOutButton)

  doc.save('output.pdf')
}
