import request from '@/utils/request.ts'

export function getAuthorizations(params: any) {
  return request({
    url: '/v1/authorizations',
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'get',
    params
  })
}

export function getPermissions(params: any) {
  return request({
    url: '/v1/permissions',
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'get',
    params
  })
}

export function getUsers(userId: string) {
  return request({
    url: `/v1/users/${userId}`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'get',
  })
}

export function getEditUsersPassword(userId: string, params: any) {
  return request({
    url: `/v1/users/${userId}/pass-modify`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'put',
    params
  })
}

export function getEditUserName(userId: string, params: any) {
  return request({
    url: `/v1/users/${userId}/edit-name`,
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'put',
    params
  })
}
export function getRolesList(params: any) {
  return request({
    url: '/v1/roles',
    baseURL: import.meta.env.VITE_API_PREFIX,
    method: 'get',
    params
  })
}