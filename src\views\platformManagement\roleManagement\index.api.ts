import request from '@/utils/request.ts'
import _ from 'lodash'

// 字典
export function getDicList(params: any) {
  return request({
    url: '/api/visitDict/listAll',
    method: 'get',
    params
  })
}

// 分页
export function getPageList(params: any) {
  return request({
    url: '/v1/sortedRoles',
    method: 'post',
    baseURL: import.meta.env.VITE_API_PREFIX,
    data: params
  })
}


export function getAppsList() {
  return request({
    url: '/v1/apps',
    method: 'get',
    baseURL: import.meta.env.VITE_API_PREFIX,
  })
}

export function getEditRoles(data: any) {
  return request({
    url: `/v1/roles`,
    method: 'post',
    baseURL: import.meta.env.VITE_API_PREFIX,
    data
  })
}

export function getPermission(params: any) {
  return request({
    url: `/v1/permissions`,
    method: 'get',
    baseURL: import.meta.env.VITE_API_PREFIX,
    params
  })
}

interface ApiParams {
  appCode: string
  roleCode: string
}
// 获取角色下的权限列表
export function getRolesPermission(params: ApiParams) {
  return request({
    url: `/v1/apps/${params.appCode}/roles/${params.roleCode}/permissions`,
    method: 'get',
    baseURL: import.meta.env.VITE_API_PREFIX,
    params
  })
}

// 绑定角色权限
export function bindPermissions({ appCode, roleCode }: ApiParams, permissions: string[]) {
  return request({
    url: `/v1/apps/${appCode}/roles/${roleCode}/permissions`,
    method: 'post',
    baseURL: import.meta.env.VITE_API_PREFIX,
    data: { permissions }
  })
}

// 解绑角色权限
export function unbindPermissions({ appCode, roleCode }: ApiParams, permissions: string[]) {
  return request({
    url: `/v1/apps/${appCode}/roles/${roleCode}/permissions`,
    method: 'delete',
    baseURL: import.meta.env.VITE_API_PREFIX,
    data: { permissions }
  })
}

// 设置角色权限
export async function setRolePermissions(rolePk: ApiParams, permissions: string[]) {
  const originPermissions = (await getRolesPermission(rolePk)).data.map((d: { code: string }) => d.code)

  const append = _.difference(permissions, originPermissions)
  const removed = _.difference(originPermissions, permissions)
  if (append.length) await bindPermissions(rolePk, append)
  if (removed.length) await unbindPermissions(rolePk, removed)
}
