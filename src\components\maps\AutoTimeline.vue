<template>
  <div class="timeline">
    <!-- 渲染时间线项 -->
    <div
      v-for="(item, index) in items"
      :key="index"
      class="timeline-item"
    >
      <!-- 标签文字 -->
      <div
        class="label"
        :class="{ active: currentIndex === index }"
        @click="onItemClick(index)"
      >
        <CheckCircleFilled />
        {{ item.label }}
      </div>

      <!-- 时间线横线（最后一个不显示） -->
      <div
        v-if="index < items.length - 1"
        class="line"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { CheckCircleFilled } from '@ant-design/icons-vue'
import Bus from '@/utils/Bus'
import { useSharedStore } from '@/store/geoState/state'
// import { storeToRefs } from 'pinia'

const store = useSharedStore()
// const { storeInfectiousType, dValue } = storeToRefs(store)
// 定义时间线数据
interface TimelineItem {
  label: string
}

const items: TimelineItem[] = [{ label: '全部传染病' }, { label: '甲类传染病' }, { label: '乙类传染病' }, { label: '丙类传染病' }, { label: '其他传染病' }]

// 当前选中项索引
const currentIndex = ref(0)

// 是否手动点击暂停
const isPaused = ref(false)

// 定时器实例
let timer: number | null = null

// 监听页面是否可见
const handleVisibilityChange = () => {
  if (document.visibilityState === 'hidden') {
    stopTimer() // 页面不可见时停止轮询
  } else {
    resetTimeline() // 页面可见时重置轮询
  }
}
// 停止轮询
const stopTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}
const getType = () => {
  if (currentIndex.value == 0) {
    // store.updatestoreInfectiousType(store,'全部')
    Bus.emit('getDiseaseAutoType', '全部')
  } else if (currentIndex.value == 1) {
    Bus.emit('getDiseaseAutoType', '甲类')
    // store.updatestoreInfectiousType(store,'甲类')
  } else if (currentIndex.value == 2) {
    Bus.emit('getDiseaseAutoType', '乙类')
    // store.updatestoreInfectiousType(store,'乙类')
  } else if (currentIndex.value == 3) {
    Bus.emit('getDiseaseAutoType', '丙类')
    // store.updatestoreInfectiousType(store,'丙类')
  } else {
    Bus.emit('getDiseaseAutoType', '其他')
    // store.updatestoreInfectiousType(store,'其他')
  }
}
// 开始自动轮播
const startAutoPlay = () => {
  stopTimer() // 确保不会创建多个定时器
  timer = setInterval(() => {
    if (!isPaused.value) {
      currentIndex.value = (currentIndex.value + 1) % items.length
      getType()
    }
  }, 10000)
}

// 重置时间线到初始状态
const resetTimeline = () => {
  isPaused.value = false
  currentIndex.value = 0 // 从第一个种类开始
  getType()
  startAutoPlay()
}

// 点击时间线项
const onItemClick = (index: number) => {
  if (currentIndex.value === index) {
    getType()
    isPaused.value = false // 恢复自动播放
  } else {
    getType()
    currentIndex.value = index // 选中当前项
    isPaused.value = true // 暂停自动播放
  }
}
// 组件挂载时启动自动播放和页面监听
onMounted(() => {
  startAutoPlay()
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

// 组件卸载时清理定时器和监听
onBeforeUnmount(() => {
  stopTimer()
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})
</script>

<style scoped>
.timeline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.timeline-item {
  display: flex;
  align-items: center;
  position: relative;
}

/* 圆形勾选框样式 */
.circle {
  width: 15px;
  height: 15px;
  /* border: 2px solid #ccc; */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.circle.active {
  background-color: #016eff;
  border-color: #016eff;
}

.checkmark {
  color: #fff;
  background-color: #fff;
  font-size: 14px;
  font-weight: bold;
}

/* 标签文字样式 */
.label {
  margin-left: 8px;
  font-size: 16px;
  color: #5e6580;
  cursor: pointer;
  transition: color 0.3s ease;
}

.label.active {
  color: #016eff;
  font-weight: bold;
}

/* 时间线样式 */
.line {
  position: absolute;
  width: 150px;
  height: 1px;
  background-color: #cbd5e1;
  top: 50%;
  left: calc(100% + 18px);
  transform: translateY(-50%);
}
</style>
