import request from '@/utils/request.ts'
import qs from 'qs'
// 获取下拉战区
export function getAreaData(params) {
  return request({
    url: '/platformUserAnalysis/areas',
    method: 'get',
    params
  })
}
// 获取下拉医院
export function getHospitalData(params) {
  return request({
    url: '/platformUserAnalysis/hospitals',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: "repeat" })
  })
}

// 获取下拉科室
export function getDepartmentData(params) {
  return request({
    url: '/platformUserAnalysis/departments',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: "repeat" })
  })
}

// // 获取指标数据
// export function getIndicatorData(data) {
//   return request({
//     url: '/api/indicator/data/query',
//     method: 'post',
//     data
//   })
// }
//平台用户量
export function usersPlatform(data) {
  return request({
    url: '/platformUserAnalysis/users/count',
    method: 'post',
    data
  })
}

// 操作类型占比
export function operationTypesData(data) {
  return request({
    url: '/platformUserAnalysis/users/operationTypes',
    method: 'post',
    data
  })
}


// 用户活跃度
export function usersAactivity(data) {
  return request({
    url: '/platformUserAnalysis/users/activity',
    method: 'post',
    data
  })
}

// 注册用户分布
export function registerDistributionCount(data) {
  return request({
    url: '/platformUserAnalysis/users/registerDistribution',
    method: 'post',
    data
  })
}



// 活跃用户分布
export function activeDistributionCount(data) {
  return request({
    url: '/platformUserAnalysis/users/activeDistribution',
    method: 'post',
    data
  })
}