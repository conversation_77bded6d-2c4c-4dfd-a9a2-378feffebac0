let config = {
  APP_CODE: 'PORTAL',
  REDIRECT_URI: import.meta.env.MODE == 'production' ? `${window.location.origin}/sso-front/home` : 'http://localhost:3300/'
}

function setConfig(options: any) {
  config = {
    ...config,
    ...options,
    APP_CODE: options.APP_CODE || config.APP_CODE,
    REDIRECT_URI: options.REDIRECT_URI || config.REDIRECT_URI
  }
}

type ConfigType = typeof config
function getConfig(key: string, isAll: boolean = false) {
  if (isAll) return config
  else return config[key as keyof ConfigType]
}

export { config, setConfig, getConfig }
