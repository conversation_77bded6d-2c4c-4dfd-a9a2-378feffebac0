<template>
  <div>
    <a-card style="height: 48px; padding: 0">
      <div class="main">
        <span class="titleArea">高原病确诊人群区域分布图</span>
        <div class="selectPart">
          <!-- <a-radio-group
            v-model:value="mode"
            :style="{ marginBottom: '8px', marginRight: '32px' }"
            @change="changeGroup"
          >
            <a-radio-button value="top">传染病发病分布</a-radio-button>
            <a-radio-button value="left">传染病症候群分布</a-radio-button>
          </a-radio-group> -->

          <div class="rangClass">
            <a-form
              :model="formState"
              name="time_related_controls"
              class="forms"
            >
              <a-form-item
                name="range-picker"
                label="统计时间范围"
              >
                <a-range-picker
                  v-model:value="rangePickerValue"
                  @change="handleChangeDate"
                  
                  :presets="presetTimeRange()"
                  value-format="YYYY-MM-DD"
                  :allowClear="false"
                  popupClassName="rangePickerIceGai"
                />
                <!-- :disabled-date="disabledStartDate" -->
              </a-form-item>
            </a-form>
          </div>
        </div>
      </div>
    </a-card>
    <div class="partMain">
      <div class="mainTop">
        <div
          class="mainTop-part"
          v-for="(item, index) in getCoreIndicatorAll"
          :key="index"
        >
          <div class="part-left-box">
            <div
              v-if="item.img.includes('-')"
              class="part-left"
            >
              <svg-icon
                :name="item?.img"
                size="36"
              ></svg-icon>
            </div>
            <div
              class="part-left part-left-img"
              v-else
            >
              {{ item.img }}
            </div>
          </div>
          <div class="part-right">
            <div class="part-right-num">
              <span>{{ item.value }}</span>
              <span class="part-right-valueTips">{{ item.tips }}</span>
            </div>
            <div class="part-right-text">{{ item.label }}</div>
          </div>
        </div>
      </div>
      <div class="mainShow">
        <div style="width: 105%; height: 4%; margin: 16px 0">
          <!-- <a-form-item
            name="diseaseType"
            label="传染病类型"
            class="diseaseTypeSelect"
          >
            <a-select
              v-model:value="selectedDiseaseType"
              :options="options"
              class="selectTypes"
              @change="handleselectedDiseaseType"
            >
            </a-select>
          </a-form-item> -->
        </div>
        <pillarShape
          class="chartMain"
          :dateRangeTime="timeValue"
          :dataSelectType="selectedDiseaseType"
          v-model="timeValue"
        ></pillarShape>
        <chinaMap
          :dateRangeTimes="timeValue"
          class="mapChinaClass"
          :dataSelectType="selectedDiseaseType"
        />
        <!-- <AutoTimeline class="buttomSelect" /> -->
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, toRef, onMounted, getCurrentInstance, onBeforeMount, watch } from 'vue'
import type { TabsProps } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import {operationLog } from '@/api/log'

import { presetTimeRange } from '@/utils/utils'
import { message } from 'ant-design-vue'

import chinaMap from '@/components/maps/chinaMap.vue'

import pillarShape from '@/components/maps/pillarShape.vue'
import AutoTimeline from '@/components/maps/AutoTimeline.vue'
import { coreIndicator } from '@/components/maps/gisIndicator'
const { proxy }: any = getCurrentInstance()

const mode = ref<TabsProps['tabPosition']>('top')

const formState: any = ref({
  rangePicker: []
})
const rangePickerValue = toRef(formState.value, 'rangePicker')

// 定义响应式数据
const options = ref([
  { label: '全部', value: '全部' },
  { label: '甲类', value: '甲类' },
  { label: '乙类', value: '乙类' },
  { label: '丙类', value: '丙类' },
  { label: '其他', value: '其他' }
])
const selectedDiseaseType = ref<any>('全部')
const handleselectedDiseaseType = (value) => {
  selectedDiseaseType.value = value
}
// const requestSent = ref(false)
const timeValue = ref<any[]>(rangePickerValue.value)

const getCoreIndicatorAll = ref<any>([])
const getDateRange = (value: []) => {
  getCoreIndicator(value)
}
// 查询核心指标
const getCoreIndicator = async (value: any[]) => {
  const obj = {
    from: value[0],
    to: value[1]
  }
  await coreIndicator(obj).then((res: any) => {
    if (res?.code == 0) {
      // // requestSent.value = true
      const dataList = res.data
      const data: any[] = []
      Object.keys(dataList).forEach((item: any, index: number) => {
        data.push({
          label: ['患者总人数', '患者就诊医院', '门诊总次数', '住院总人数', ][index],
          value: dataList[item].toLocaleString(),
          img: ['dashbord-datas', 'dashbord-desease', '门', '住', ][index],
          tips: ['人', '家', '人次', '人次',][index]
        })
      })
      getCoreIndicatorAll.value = data
      console.log(getCoreIndicatorAll.value, 'getCoreIndicatorAll.value')
    } else {
      proxy.$message.error(res?.errorMsg)
    }
  })
}

const handleChangeDate = (e) => {
  if (!e) {
    message.error('请选择报告统计日期')
    return
  } else {
    getDateRange(e)
    timeValue.value = e
  }

  console.log(timeValue.value, 'timeValue1111')
}

watch(
  () => timeValue.value,
  (newOption: any) => {
    timeValue.value = newOption
    console.log(newOption, 'newOption')
  }
  // { deep: true } // 深度监听
)
// 指标数据千分位
const numberWithCommas = (x: string | number) => {
  x = x.toString()
  var pattern = /(-?\d+)(\d{3})/
  while (pattern.test(x)) x = x.replace(pattern, '$1,$2')
  return x
}

const disabledStartDate = (current: dayjs.Dayjs) => {
  return current.isBefore(dayjs('2022-01-01'))
}
onBeforeMount(() => {
  // formState.value['rangePicker'] = [dayjs().subtract(1, 'months').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  formState.value['rangePicker'] = [dayjs().subtract(7, 'days').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  console.log(formState.value['rangePicker'], 999)
  timeValue.value = rangePickerValue.value
})
onMounted(() => {
  getDateRange(timeValue.value)
})

const baseIP = import.meta.env.BASE_IP
const operationLogs = async () => {
  const obj = {
    category: '查看GIS分析',
    ip: baseIP,
    type: '高原病发病地理信息'
  }
  await operationLog(obj)
    .then((res) => {
      if (res?.code == 0) {
        console.log(res, 'res')
        
      }
    })
    .catch((err) => {
      console.log(err, 'err')
    })
}
onMounted(() => {
  operationLogs()
})
</script>

<style lang="less" scoped>
::v-deep .ant-card-body {
  padding: 0;
}
.main {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  padding: 8px 24px 8px 24px;
}
.titleArea {
  color: #07123c;
  height: 100%;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  // margin-left: 20px;
}
.selectPart {
  // display: flex;
  // flex-wrap: nowrap;
  // justify-content: space-between;
  // width: 60%;
  height: 100%;
  // padding: 8px 24px 0 0;
  // padding: 8px 24px 8px 24px;
}
.forms {
  width: 100%;
  // display: flex;
  // justify-content: space-between;
  line-height: 5px;
}
.rangClass {
  // height: 100%;
  width: 100%;
  // display: flex;
  // justify-content: space-between;
  line-height: 5px;
}
.diseaseTypeSelect {
  width: 19%;
}
.titleSpan {
  height: 22px;
  width: 192px;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #07123c;
  font-family: PingFang SC;
}
.partMain {
  padding: 8px 24px 8px 24px;
  gap: 16px;
  background: #ffffff;
}
.mainTop {
  display: flex;
  flex-wrap: nowrap;
  height: 68px;
  justify-content: space-between;
  border: 0.1px solid #ececf5;
  border-top: none;
  border-left: none;
  border-right: none;
  margin: 16px0 0 0;
  // padding-bottom: 10px;
  .mainTop-part {
    width: 16%;
    height: 48px;
    display: flex;
    align-items: center;
    .part-left-box {
      height: 100%;
      display: flex;
      align-items: center;
    }
    .part-left {
      width: 36px;
      height: 36px;
    }
    .part-left-img {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: #2316ab;
      color: #fff;
      font-size: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .part-right {
      width: 70%;
      height: 100%;
      margin-left: 10px;
      &-num {
        font-size: 22px;
        font-weight: 600;
        color: #07123c;
        font-family: PingFang SC;
        line-height: 26px;
      }
      &-text {
        font-size: 14px;
        font-weight: 400;
        color: #8f94a7;
        font-family: Noto Sans SC;
      }
      &-valueTips {
        font-size: 12px;
        font-weight: 600;
        color: #07123c;
        font-family: PingFang SC;
        line-height: 22px;
        margin-left: 10px;
      }
    }
  }
}
.svgSpan {
  margin-top: 15px;
}
.firstPart {
  display: flex;
  flex-wrap: nowrap;
}
.firstPart .ant-statistic {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
}
// .firstPart .ant-statistic-title{
//   margin-top: 8px;
//   margin-bottom: 0;
// }
.firstPart .ant-statistic-content .ant-statistic-content-value .ant-statistic-content-value-int {
  font-size: 22px;
  color: #07123c;
  font-family: PingFang SC;
  font-weight: 600;
}
// .statistic {
//   height: 48px;
//   line-height: 20px;

// }

#Home {
  width: 100%;
  min-height: 600px;
  position: relative;

  .select {
    position: absolute;
    left: 1%;
    top: 15%;
    z-index: 15;

    .select_item {
      padding: 6px 25px;
      color: rgb(179, 239, 255);
      background: rgba(147, 235, 248, 0.35);
      border-radius: 15px;
      margin: 25px 0;
      cursor: pointer;

      .active {
        color: rgb(254, 217, 50);
      }
    }
  }
}
.mainShow {
  height: 800px;
  width: 100vw;
  position: relative;
  // padding: 8px;
}
.mapChinaClass {
  position: absolute;
  right: 10%;
  height: 90%;
  width: calc(70% - 48px);
  margin-left: 48px;
}
.chartMain {
  position: absolute;
  left: 0;
  height: 77%;
  width: 20%;
}
.buttomSelect {
  position: absolute;
  bottom: 0;
  width: 70%;
  left: 5%;
  height: 20%;
}
.part-right-num{
  font-family: Noto-Sans-SC;
}
</style>
<!-- <style lang="less">
:where(.css-dev-only-do-not-override-cceck0).ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
  background: #e9f5f3;
}
:where(.css-dev-only-do-not-override-cceck0).ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before, :where(.css-dev-only-do-not-override-cceck0).ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
    background: #e9f5f3;
}
</style> -->
