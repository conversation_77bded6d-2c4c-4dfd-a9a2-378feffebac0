<template>
  <div class="logo" v-if="!collapsed">
    <!-- <div class="logo-img">
      <img
        src="../../../assets/png/titleLogo.png"
        alt=""
      />
    </div> -->
    <div class="title">
      <div class="main-title">陆军高原病研究数据支撑系统</div>
      <div class="sub-title">Army High Altitude Disease Research Data Support System</div>
    </div>
    <!-- <svg-icon name="programlogo" width="207" height="104"></svg-icon> -->
  </div>
  <div class="logo" v-else>
    <div class="logo-close-img">
      <!-- <img src="../../../assets/png/titleLogo.png" alt="" /> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps } from 'vue'
  const props = defineProps({
    collapsed: {
      type: Boolean,
      default: false
    }
  })
</script>

<style lang="less" scoped>
  [data-theme='dark'] .logo {
    background-color: #555555;
  }

  .logo {
    width: 100%;
    height: var(--logo-height, 80px);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
    border-inline-end: 1px solid rgba(5, 5, 5, 0.06);
  }

  .logo-close-img {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .logo-img {
    width: 80px;
    height: var(--logo-height, 80px);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    margin-left: 10px;

    .main-title {
      color: #07123c;
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      font-weight: 600;
    }

    .sub-title {
      color: #6b7280;
      font-variant-numeric: lining-nums tabular-nums;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-weight: 400;
    }
  }
</style>
